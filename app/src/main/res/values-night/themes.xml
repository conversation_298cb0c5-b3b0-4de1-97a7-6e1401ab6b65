<resources xmlns:ns2="http://schemas.android.com/apk/res-auto" xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.EDC" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryVariant">@color/colorBackground</item>
        <item name="colorOnPrimary">@color/black</item>
        <item name="colorPrimaryDark">@color/colorPrimary</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/colorPrimary</item>
        <item name="colorSecondaryVariant">@color/colorBackground</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor" tools:targetApi="l">?attr/colorPrimaryVariant</item>
        <!-- Opt out of edge-to-edge enforcement for Android 15+ -->
        <item name="android:windowOptOutEdgeToEdgeEnforcement" tools:targetApi="35">true</item>
        <!-- Customize your theme here. -->
    </style>

    <style name="Splash" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="android:windowBackground">@drawable/splash_image</item>
        <item name="colorPrimary">@color/independence_day_color</item>
        <item name="colorPrimaryDark">@color/independence_day_color</item>
        <item name="colorAccent">@color/btn_color_state</item>
        <!-- Opt out of edge-to-edge enforcement for Android 15+ -->
        <item name="android:windowOptOutEdgeToEdgeEnforcement" tools:targetApi="35">true</item>
    </style>

    <style name="Base.Widget.MaterialComponents.TextInputEditText" parent="Widget.Design.TextInputEditText">
        <item name="enforceMaterialTheme">true</item>
        <item name="enforceTextAppearance">true</item>
        <item name="android:background">@null</item>
        <item name="android:paddingStart" ns2:ignore="NewApi">12dp</item>
        <item name="android:paddingEnd" ns2:ignore="NewApi">12dp</item>
        <item name="android:paddingLeft">12dp</item>
        <item name="android:paddingRight">12dp</item>
        <item name="android:paddingTop">16dp</item>
        <item name="android:paddingBottom">16dp</item>
        <item name="android:textAppearance">?attr/textAppearanceSubtitle1</item>
        <item name="textInputLayoutFocusedRectEnabled">true</item>
    </style>

    <style name="ToolbarTheme">
        <item name="android:background">@color/colorPrimary</item>
        <item name="colorControlNormal">@color/white</item>
        <item name="android:titleTextAppearance">@style/Heading2</item>
        <item name="titleTextColor">@color/white</item>
    </style>

    <style name="BaseTextView" parent="Widget.AppCompat.TextView">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:fontFamily">@font/roboto</item>
        <item name="android:lineSpacingExtra">6sp</item>
        <item name="android:textColor">@color/black_80</item>
    </style>

    <style name="BaseHeading" parent="BaseTextView">
        <item name="android:fontFamily">@font/roboto_bold</item>
    </style>

    <style name="Heading2" parent="BaseHeading">
        <item name="android:textSize">18sp</item>
    </style>

    <style name="Heading3" parent="BaseHeading">
        <item name="android:textSize">16sp</item>
    </style>

    <style name="ButtonFill" parent="Widget.MaterialComponents.Button.TextButton.Icon">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:paddingBottom">11dp</item>
        <item name="android:paddingTop">11dp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textAppearance">@style/Heading3</item>
        <item name="android:textColor">@color/btn_text_color_state</item>
        <item name="backgroundTint">@color/btn_color_state</item>
        <item name="iconGravity">textStart</item>
        <item name="iconTint">@null</item>
    </style>

    <style name="DefaultDialogTheme" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:windowBackground">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
    </style>

    <style name="FullScreenDialogTheme" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:windowBackground">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowMinWidthMajor">90%</item>
        <item name="android:windowMinWidthMinor">90%</item>
    </style>

    <style name="BottomSheetModal.RoundCorner" parent="Widget.Design.BottomSheet.Modal">
        <item name="android:background">@drawable/bg_top_rounded_corner</item>
    </style>

    <style name="BottomSheetDialogTheme.RoundCorner">
        <item name="bottomSheetStyle">@style/BottomSheetModal.RoundCorner</item>
    </style>

    <!-- Material3 themes with edge-to-edge opt-out -->
    <style name="Theme.Material3.Dark.NoActionBar.OptOut" parent="Theme.Material3.Dark.NoActionBar">
        <!-- Opt out of edge-to-edge enforcement for Android 15+ -->
        <item name="android:windowOptOutEdgeToEdgeEnforcement" tools:targetApi="35">true</item>
    </style>

    <style name="Theme.Material3.DayNight.NoActionBar.OptOut" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Opt out of edge-to-edge enforcement for Android 15+ -->
        <item name="android:windowOptOutEdgeToEdgeEnforcement" tools:targetApi="35">true</item>
    </style>
</resources>