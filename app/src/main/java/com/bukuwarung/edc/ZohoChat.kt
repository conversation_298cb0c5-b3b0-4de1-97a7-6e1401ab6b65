package com.bukuwarung.edc

import android.app.Application
import android.os.Build
import android.widget.Toast
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.ppob.constants.PpobAnalytics
import com.bukuwarung.edc.util.BuildUtils
import com.bukuwarung.edc.util.Utils
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.zoho.commons.InitConfig
import com.zoho.livechat.android.listeners.InitListener
import com.zoho.livechat.android.operation.SalesIQApplicationManager
import com.zoho.salesiqembed.ZohoSalesIQ

object ZohoChat {

    const val CARD_READER_INSTRUCTION = "card_reader_instruction"

    /**
     * Initialize Zoho SalesIQ with appropriate app and access keys
     */
    fun initialize(application: Application) {
        val initConfig = InitConfig()
        val zohoAppKey = BuildUtils.getZohoAppKey()
        val zohoAccessKey = BuildUtils.getZohoAccessKey()

        ZohoSalesIQ.init(
            application,
            zohoAppKey,
            zohoAccessKey,
            initConfig,
            object : InitListener {
                override fun onInitSuccess() {
                    ZohoSalesIQ.showLauncher(false)
                }

                override fun onInitError(errorCode: Int, errorMessage: String) {
                    // Handle initialization error
                }
            }
        )
    }

    fun openZohoChat(entryPoint: String) {
        try {
            ZohoSalesIQ.Chat.setLanguage("in")
            ZohoSalesIQ.present(ZohoSalesIQ.Tab.Conversations)
            ZohoSalesIQ.Visitor.setContactNumber(
                Utils.getPhoneNumber()
            )
            ZohoSalesIQ.Visitor.setEmail(
                Utils.getPhoneNumber().plus("@merchant.com")
            )
            if (Utils.getBusinessName().isNullOrEmpty()) {
                ZohoSalesIQ.Visitor.setName("No Name")
            } else {
                ZohoSalesIQ.Visitor.setName(Utils.getBusinessName())
            }
            ZohoSalesIQ.Visitor.addInfo("terminal_id", Utils.getTerminalId())
            ZohoSalesIQ.Visitor.addInfo("connected_serial_number", Utils.getDeviceSerialNumber())
            if (!Utils.isFixedTerminal() && Utils.getDeviceSerialNumber().isNotEmpty()) {
                ZohoSalesIQ.Visitor.addInfo(
                    "device_activation",
                    if (Utils.getConnectedDeviceMasterKey(Utils.getDeviceSerialNumber())
                            .isNullOrEmpty()
                    ) {
                        "not_activated"
                    } else {
                        "activated"
                    }
                )
                ZohoSalesIQ.Visitor.addInfo(
                    "TMS",
                    BuildUtils.getApiBaseUrl().plus(
                        "/panacea/panacea/edc/terminal-management-system?search=" +
                            Utils.getDeviceSerialNumber()
                    )
                )
            }
            if (Utils.isFixedTerminal()) {
                ZohoSalesIQ.Visitor.addInfo(
                    "manufacturer",
                    Build.MANUFACTURER
                )
            }
            val map = HashMap<String, String>()
            map[PpobAnalytics.ENTRY_POINT] = entryPoint
            Analytics.trackEvent("customer_support_chat", map)
        } catch (e: Exception) {
            FirebaseCrashlytics.getInstance().recordException(e)
        }
        try {
            if (BuildUtils.isDebugBuild()) {
                SalesIQApplicationManager.getCurrentActivity()?.let {
                    Toast.makeText(it, "Zoho Open from $entryPoint", Toast.LENGTH_SHORT).show()
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * Determines if Zoho Chat functionality is available in the current build configuration.
     * Zoho Chat is only available in staging and production environments, but disabled in development
     * to avoid unnecessary chat support load during development and testing.
     *
     * @return true if Zoho Chat should be available (staging or production), false otherwise (development)
     */
    fun isZohoChatAvailable(): Boolean = BuildUtils.isStaging() || BuildUtils.isProduction()

    object EntryPoint {
        const val CASH_WITHDRAWAL = "cash_withdrawal"
        const val BALANCE_INQUIRY = "balance_inquiry"
        const val TRANSFER = "transfer"
        const val EDC_ORDER = "edc_order"
        const val RNL_WITHDRAWAL = "rnl_withdrawal"
        const val TERMINAL_ID_OR_DEVICE_SERIAL_BLOCKED = "terminal_id_or_device_serial_blocked"
        const val INVALID_MERCHANT = "invalid_merchant"
        const val BCA_NOT_SUPPORTED = "bca_not_supported"
    }
}

typealias ZohoChatEntryPoint = ZohoChat.EntryPoint
