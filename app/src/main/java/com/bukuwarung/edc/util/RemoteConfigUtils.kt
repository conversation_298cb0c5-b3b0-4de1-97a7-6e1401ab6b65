package com.bukuwarung.edc.util

import com.bukuwarung.edc.BuildConfig
import com.bukuwarung.edc.card.transfermoney.constants.CheckBalancePinConfig
import com.bukuwarung.edc.homepage.constant.HomePageRemoteConfig
import com.bukuwarung.edc.login.constant.LoginRemoteConfig
import com.bukuwarung.edc.payments.constant.PaymentRemoteConfig
import com.bukuwarung.edc.ppob.constants.PpobRemoteConfig
import com.bukuwarung.edc.verifyotp.constant.VerityOtpRemoteConfig
import com.bukuwarung.edc.worker.location.constant.LocationConfigConstants
import com.bukuwarung.edc.worker.location.constant.LocationConfigConstants.EDC_WELCOME_SCREENS
import com.google.firebase.ktx.Firebase
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.google.firebase.remoteconfig.ktx.remoteConfig
import com.google.firebase.remoteconfig.ktx.remoteConfigSettings
import com.google.gson.reflect.TypeToken

object RemoteConfigUtils {

    private const val FAVORITE_CUSTOMER_ENABLED = "favorite_customer_enabled"
    private const val PAYMENT_PENDING_TIME_IN_MINUTES = "payment_pending_time_in_minutes"
    private const val PHYSICAL_VISIT_CITIES = "physical_visit_cities"
    private const val APPLY_NEW_COMPRESSION = "apply_new_compression"

    private val DEFAULTS: HashMap<String, Any> =
        hashMapOf(
            LocationConfigConstants.LOCATION_SEND_TIME to LocationConfigConstants.SEND_TIME,
            VerityOtpRemoteConfig.EDC_DASHBOARD to VerityOtpRemoteConfig.EDC_DASHBOARD_URL,
            LoginRemoteConfig.TNC to LoginRemoteConfig.tnc(),
            EDC_WELCOME_SCREENS to LocationConfigConstants.WELCOME_SCREENS,
            LoginRemoteConfig.PRIVACY to LoginRemoteConfig.privacyPolicy(),
            HomePageRemoteConfig.EDC_HOMEPAGE_SCHEMA to HomePageRemoteConfig.HOMEPAGE_SCHEMA_VAL,
            HomePageRemoteConfig.EDC_MINIATMPRO_HOMEPAGE_SCHEMA to
                HomePageRemoteConfig.EDC_MINIATMPRO_HOMEPAGE_SCHEMA_VAL,
            HomePageRemoteConfig.EDC_SAKU_HOMEPAGE_SCHEMA to
                HomePageRemoteConfig.EDC_SAKU_HOMEPAGE_SCHEMA_VAL,
            HomePageRemoteConfig.EDC_LEADERBOARD_CONFIG_NAME to
                HomePageRemoteConfig.EDC_LEADERBOARD_CONFIG_VAL,
            HomePageRemoteConfig.HOMEPAGE_SCHEMA_FAILSAFE to
                HomePageRemoteConfig.HOMEPAGE_SCHEMA_VAL_FAILSAFE,
            HomePageRemoteConfig.HOMEPAGE_SCHEMA_FAILSAFE_MINIATMPRO to
                HomePageRemoteConfig.HOMEPAGE_SCHEMA_VAL_FAILSAFE_MINIATMPRO,
            HomePageRemoteConfig.EDC_HOMEPAGE_SALDO to
                HomePageRemoteConfig.HOMEPAGE_SALDO_BODY_CONTENT,
            HomePageRemoteConfig.EDC_HOMEPAGE_TICKER to HomePageRemoteConfig.HOMEPAGE_TICKER,
            HomePageRemoteConfig.EDC_HOMEPAGE_HISTORY to HomePageRemoteConfig.HOMEPAGE_HISTORY,
            HomePageRemoteConfig.EDC_HOMEPAGE_CARD to HomePageRemoteConfig.HOMEPAGE_CARD,
            HomePageRemoteConfig.EDC_HOMEPAGE_TILES_PER_ROW to 3,
            PaymentRemoteConfig.SALDO_BONUS to PaymentRemoteConfig.SALDO_BONUS_URL,
            PaymentRemoteConfig.FAQ_USED to PaymentRemoteConfig.FAQ_USED_ACCOUNT_BW_URL,
            CheckBalancePinConfig.EDC_DYNAMIC_KEYBOARD to
                CheckBalancePinConfig.EDC_DYNAMIC_KEYBOARD_BODY,
            HomePageRemoteConfig.HOUR to 24,
            HomePageRemoteConfig.MINS to 60,
            PpobRemoteConfig.PPOB_CONFIG to PpobRemoteConfig.PPOB_CONFIG_VALUE,
            PpobRemoteConfig.PPOB_ERROR_POPUP_CONTENT to
                PpobRemoteConfig.PPOB_ERROR_POPUP_CONTENT_VALUE,
            PpobRemoteConfig.EDC_HOMEPAGE_PPOB to PpobRemoteConfig.EDC_HOMEPAGE_PPOB_VALUE,
            PpobRemoteConfig.EDC_SHOW_PPOB_TOP_SECTION to false,
            HomePageRemoteConfig.SAKU_APP_UPDATE_VERSION_CODE to
                HomePageRemoteConfig.SAKU_APP_UPDATE_VERSION_CODE_VAL,
            CheckBalancePinConfig.PENDING_TRANSACTION_TNC_URL to
                CheckBalancePinConfig.PENDING_TRANSACTION_TNC_URL_VALUE,
            CheckBalancePinConfig.PENDING_TRANSACTION_INFO_HIGHLIGHT to
                CheckBalancePinConfig.PENDING_TRANSACTION_INFO_HIGHLIGHT_VALUE,
            PaymentRemoteConfig.TRANSFER_DESTINATION_WARNING to
                PaymentRemoteConfig.TRANSFER_DESTINATION_WARNING_VALUE,
            HomePageRemoteConfig.EDC_KOMISI_AGEN to HomePageRemoteConfig.EDC_KOMISI_AGEN_VAL,
            HomePageRemoteConfig.TICKER_CHOOSE_ACCOUNT to
                HomePageRemoteConfig.CHOOSE_ACCOUNT_TICKER_DEFAULT
        )

    val remoteConfig: FirebaseRemoteConfig by lazy {
        getFirebaseRemoteConfig()
    }

    fun initialize() {
        remoteConfig.toString()
    }

    private fun getFirebaseRemoteConfig(): FirebaseRemoteConfig {
        val remoteConfig = Firebase.remoteConfig

        val configSettings = remoteConfigSettings {
            minimumFetchIntervalInSeconds = if (BuildConfig.DEBUG) {
                0 // Kept 0 for quick debug
            } else {
                1 * 60 * 60
            }
            fetchTimeoutInSeconds = 5000
        }

        remoteConfig.setConfigSettingsAsync(configSettings)
        remoteConfig.setDefaultsAsync(DEFAULTS)
        remoteConfig.fetchAndActivate().addOnCompleteListener {
            if (it.isSuccessful) {
                remoteConfig.fetchAndActivate()
            }
        }
        return remoteConfig
    }

    fun getPaymentPendingTimeInMinutes(): Int =
        remoteConfig.getLong(PAYMENT_PENDING_TIME_IN_MINUTES).toInt()

    object FavoriteCustomer {
        fun isEnabled(): Boolean = remoteConfig.getBoolean(FAVORITE_CUSTOMER_ENABLED)
    }

    fun getWelcomeScreens(): String = remoteConfig.getString(EDC_WELCOME_SCREENS)

    fun physicalVisitCities(): ArrayList<String> {
        val json = getRemoteConfigString(PHYSICAL_VISIT_CITIES)
        val type = object : TypeToken<ArrayList<String>>() {}.type
        return Utils.jsonToObject(json, type) ?: arrayListOf()
    }

    private fun getRemoteConfigString(configKey: String): String {
        var originalValue = remoteConfig.getString(configKey)
        var budRemoteConfig =
            originalValue.replace("api-v3.bukuwarung.com", "api-v4.bukuwarung.com")
                .replace("api-v2.bukuwarung.com", "api-v4.bukuwarung.com")
        return budRemoteConfig
    }

    fun applyNewCompression(): Boolean = remoteConfig.getBoolean(APPLY_NEW_COMPRESSION)
}
