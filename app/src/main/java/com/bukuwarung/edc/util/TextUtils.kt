package com.bukuwarung.edc.util

import android.graphics.Typeface
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextPaint
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.text.style.StrikethroughSpan
import android.text.style.StyleSpan
import android.text.style.UnderlineSpan
import android.view.View
import androidx.annotation.ColorInt

object TextUtils {
    data class TextDecorations(
        @ColorInt val textColor: Int? = null,
        val bold: <PERSON>olean = false,
        val underline: Boolean = false,
        val onClick: ((view: View) -> Unit)? = null,
        val strikethrough: Boolean = false
    )

    fun decorateTextString(
        text: String,
        decorations: HashMap<String, TextDecorations>
    ): SpannableStringBuilder {
        val builder = SpannableStringBuilder()
        builder.append(text)
        decorations.entries.forEach { decoration ->
            val subSpan = decoration.key
            val spanDecorations = decoration.value
            if (subSpan.isNotNullOrBlank()) {
                val startingIndex = text.indexOf(subSpan)
                val endingIndex = startingIndex + (subSpan.length)
                if (startingIndex > 0 && endingIndex > 0) {
                    spanDecorations.textColor?.let {
                        builder.setSpan(
                            ForegroundColorSpan(it),
                            startingIndex,
                            endingIndex,
                            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                        )
                    }
                    if (spanDecorations.bold) {
                        builder.setSpan(
                            StyleSpan(Typeface.BOLD),
                            startingIndex,
                            endingIndex,
                            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                        )
                    }
                    if (spanDecorations.underline) {
                        builder.setSpan(
                            UnderlineSpan(),
                            startingIndex,
                            endingIndex,
                            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                        )
                    }
                    if (spanDecorations.strikethrough) {
                        builder.setSpan(
                            StrikethroughSpan(),
                            startingIndex,
                            endingIndex,
                            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                        )
                    }
                    spanDecorations.onClick?.let {
                        val clickableSpan = object : ClickableSpan() {
                            override fun onClick(view: View) {
                                it.invoke(view)
                            }

                            override fun updateDrawState(ds: TextPaint) {
                                spanDecorations.textColor?.let { color -> ds.color = color }
                                if (spanDecorations.bold) {
                                    ds.typeface = Typeface.DEFAULT_BOLD
                                }
                                ds.isUnderlineText = spanDecorations.underline
                            }
                        }
                        builder.setSpan(
                            clickableSpan,
                            startingIndex,
                            endingIndex,
                            Spannable.SPAN_INCLUSIVE_INCLUSIVE
                        )
                    }
                }
            }
        }
        return builder
    }
}
