package com.bukuwarung.edc.util

import android.Manifest
import android.app.Activity
import android.app.Dialog
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.SharedPreferences
import android.content.pm.PackageManager
import android.content.res.Resources
import android.graphics.Typeface
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextPaint
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.text.style.StrikethroughSpan
import android.text.style.StyleSpan
import android.text.style.UnderlineSpan
import android.util.Base64
import android.util.Log
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.Toast
import androidx.annotation.ColorInt
import androidx.annotation.RequiresApi
import androidx.browser.customtabs.CustomTabsIntent
import androidx.core.content.ContextCompat
import com.auth0.android.jwt.JWT
import com.bukuwarung.analytic.BtAnalyticConstant
import com.bukuwarung.analytic.BtAnalytics
import com.bukuwarung.bluetooth_printer.BluetoothDevices
import com.bukuwarung.bluetooth_printer.BluetoothDevices.context
import com.bukuwarung.bluetooth_printer.utils.PermissionConst
import com.bukuwarung.edc.BuildConfig
import com.bukuwarung.edc.R
import com.bukuwarung.edc.app.config.AppConfig
import com.bukuwarung.edc.app.config.VariantConfig
import com.bukuwarung.edc.app.config.types.AppVariant
import com.bukuwarung.edc.card.CardPinDynamicActivity
import com.bukuwarung.edc.card.CardPinFirebaseDynamicActivity
import com.bukuwarung.edc.card.ExternalPinpadActivity
import com.bukuwarung.edc.card.cashwithdrawal.model.SettlementBankAccount
import com.bukuwarung.edc.card.constant.CardAnalyticsConstants
import com.bukuwarung.edc.card.data.model.CheckBalanceRequest
import com.bukuwarung.edc.card.data.model.TransactionReversalRequest
import com.bukuwarung.edc.card.domain.model.TmsFailureType
import com.bukuwarung.edc.card.domain.model.consts.Constants
import com.bukuwarung.edc.card.domain.usecase.ConfigureAidUseCase
import com.bukuwarung.edc.card.transfermoney.model.TransferMoneyRequestResponseBody
import com.bukuwarung.edc.card.ui.edcdevices.model.DeviceInfo
import com.bukuwarung.edc.card.ui.edcdevices.model.DeviceItem
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.global.EdcApplication
import com.bukuwarung.edc.global.enums.TransactionType
import com.bukuwarung.edc.homepage.ui.BTRouterActivity
import com.bukuwarung.edc.homepage.ui.RouterActivity
import com.bukuwarung.edc.login.ui.LoginActivity
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.payments.data.model.BankAccount
import com.bukuwarung.edc.payments.data.model.KycTier
import com.bukuwarung.edc.settings.ui.setting.MiniatmDebugSettingActivity
import com.bukuwarung.lib.webview.util.Utils.launchUriIntent
import com.bukuwarung.network.NetworkConst.BUKUWARUNG_TOKEN
import com.bukuwarung.network.refreshtoken.TokenManager
import com.google.android.gms.tasks.Task
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.firebase.messaging.FirebaseMessaging
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.tiktok.TikTokBusinessSdk
import com.vfi.smartpos.deviceservice.constdefine.ConstPBOCHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import okhttp3.internal.and
import java.lang.reflect.Type
import java.nio.charset.StandardCharsets
import java.text.DecimalFormat
import java.text.DecimalFormatSymbols
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.Locale.getDefault
import java.util.TimeZone
import java.util.UUID
import java.util.regex.Pattern
import kotlin.math.abs
import kotlin.math.roundToLong

object Utils {
    val sharedPreferences: SharedPreferences by lazy {
        EdcApplication.instance.getSharedPreferences(
            "preference_key",
            0
        )
    }

    private const val TICKER_CLOSE_TIME = "ticker_close_time"

    private const val HARDWARE_SERIAL_NUMBER = "hardware_serial_number"

    const val FIRST_TIME_INVALID_PIN = "first_time_invalid_pin"
    const val REGISTERED_DEVICES = "registered_devices"
    const val CASH_WITHDRAWAL_FIRST_TIME_POPUP = "CASH_WITHDRAWAL_FIRST_TIME_POPUP"
    const val CONFIRM_CASH_WITHDRAWAL_ACCOUNT = "CONFIRM_CASH_WITHDRAWAL_ACCOUNT"
    const val SETTLEMENT_BANK_ACCOUNT = "SETTLEMENT_BANK_ACCOUNT"

    const val SHOULD_RETRY_POST_FCM_TOKEN = "should_retry_post_fcm_token"
    const val FCM_DEVICE_ID: String = "fcm_device_id"
    const val LAST_STORED_FCM_TOKEN: String = "last_stored_fcm_token"

    const val SET_BANK_ACCOUNT = "SET_BANK_ACCOUNT"
    const val NO_SAKU_DEVICE_REGISTERED_BT = "NO_SAKU_DEVICE_REGISTERED_BT"

    const val FIRST_TIME_LOCATION_PERMISSION_REQUEST = "first_time_location_permission_request"
    const val MINI_ATM_PRO_CLIENT_NAME = "MINI_ATM_PRO_CLIENT_NAME"

    const val CARD_INSERT_SOUND = "Efek suara masukkan kartu"
    const val ERROR_SOUND = "Efek suara pesan eror"
    const val TRANSACTION_SOUND = "Efek suara transaksi berhasil"

    fun isInternetAvailable(): Boolean {
        val context = EdcApplication.instance
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            isConnectedNewApi(context)
        } else {
            isConnectedOld(context)
        }
    }

    fun getBooleanConfig(key: String): Boolean = sharedPreferences.getBoolean(key, false)

    fun isSerialNumberCheckEnabled(): Boolean {
        if (BuildConfig.FLAVOR.contains("prod", true)) {
            return true
        } else {
            return getBooleanConfig(MiniatmDebugSettingActivity.TEST_WITH_TMS)
        }
    }

    fun setTestingFlow(transType: String) = sharedPreferences.put("transType", transType)

    fun hasLoadedConfig(): Boolean =
        sharedPreferences.getBoolean(ConfigureAidUseCase.EMV_SHARED_PREF, false)

    fun setLoadedConfig(value: Boolean) {
        sharedPreferences.put(ConfigureAidUseCase.EMV_SHARED_PREF, value)
    }

    @Suppress("DEPRECATION")
    private fun isConnectedOld(context: Context): Boolean {
        val connManager =
            context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val networkInfo = connManager.activeNetworkInfo
        return networkInfo!!.isConnected
    }

    @RequiresApi(Build.VERSION_CODES.M)
    private fun isConnectedNewApi(context: Context): Boolean {
        val cm = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val capabilities = cm.getNetworkCapabilities(cm.activeNetwork)
        return capabilities?.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET).isTrue
    }

    private fun internalUUID(uuid: UUID, j: Long): String =
        UUID(j, uuid.leastSignificantBits).toString()

    fun generatedAppId(): String = internalUUID(
        UUID.randomUUID(),
        System.currentTimeMillis()
    )

    fun getDummyCardBundle(): Bundle {
        val cardBundle = Bundle()
        cardBundle.putString(
            ConstPBOCHandler.onConfirmCardInfo.info.KEY_PAN_String,
            "****************"
        )
        cardBundle.putString(
            ConstPBOCHandler.onConfirmCardInfo.info.KEY_TRACK2_String,
            "****************D44102010000035300000"
        )
        cardBundle.putString(ConstPBOCHandler.onConfirmCardInfo.info.KEY_SERVICE_CODE_String, "201")
        cardBundle.putString(
            ConstPBOCHandler.onConfirmCardInfo.info.KEY_EXPIRED_DATE_String,
            "4410"
        )
        return cardBundle
    }

    fun showKeyboard(context: Context) {
        (
            context.getSystemService(
                Context.INPUT_METHOD_SERVICE
            ) as InputMethodManager
            ).toggleSoftInput(
            2,
            0
        )
    }

    fun hideKeyboard(activity: Activity) {
        var view = activity.currentFocus
        // If no view currently has focus, create a new one, just so we can grab a window token from it
        if (view == null) {
            view = View(activity)
        }
        hideKeyboardFrom(activity, view)
    }

    private fun hideKeyboardFrom(context: Context?, view: View?) {
        if (view == null || context == null) return
        try {
            val imm = context.getSystemService(Activity.INPUT_METHOD_SERVICE) as InputMethodManager
            imm.hideSoftInputFromWindow(view.windowToken, 0)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun getUserIdFromToken(): String {
        return try {
            val token: String = EncryptedPreferencesHelper.get(BUKUWARUNG_TOKEN, "")
            if (token == "") {
                return ""
            }
            val parsedJWT = JWT(token)

            val claim = parsedJWT.getClaim("claims").asObject(ClaimStoreId::class.java)
            if (claim?.userId != null) {
                sharedPreferences.put("user_id", claim.userId)
                return claim.userId
            }
            ""
        } catch (e: java.lang.Exception) {
            ""
        }
    }

    fun getUserId(): String {
        var userId: String? = sharedPreferences.get("user_id", "")
        if (userId.isNullOrBlank()) {
            userId = getUserIdFromToken()
            sharedPreferences.put("user_id", userId)
        }
        return userId
    }

    fun getPaymentAccountId(): String = EncryptedPreferencesHelper.get("payment_account_id", "")

    fun setPaymentAccountId(paymentAccountId: String) {
        EncryptedPreferencesHelper.put("payment_account_id", paymentAccountId)
    }

    fun getKycTierFromToken(
        jwtString: String = EncryptedPreferencesHelper.get(
            BUKUWARUNG_TOKEN,
            ""
        )
    ): KycTier {
        var kycTier = KycTier.NON_KYC
        try {
            val jwt = JWT(jwtString)
            val kycTierStringBase64 = jwt.getClaim("user_kyc_tier").asString()
            if (kycTierStringBase64.isNotNullOrEmpty()) {
                val decodedBytes = Base64.decode(kycTierStringBase64, Base64.DEFAULT)
                val tierString = String(decodedBytes, StandardCharsets.UTF_8)
                kycTier = KycTier.valueOf(tierString)
                sharedPreferences.put(PaymentConst.KYC_TIER, tierString)
            }
        } catch (ex: Exception) {
            FirebaseCrashlytics.getInstance().recordException(ex)
        }
        return kycTier
    }

    fun getStorableDateString(date: Date?): String? =
        SimpleDateFormat("yyyy-MM-dd", Locale.US).format(date)

    fun formatAmount(amount: Double?): String {
        val sb = StringBuilder()
        sb.append("Rp")
        if (amount != null) {
            sb.append(
                formatInternationalCurrency(amount.toDouble())
            )
        } else {
            sb.append("0")
        }
        return sb.toString()
    }

    fun formatAmountWithoutRp(amount: Double?): String {
        val sb = StringBuilder()
        if (amount != null) {
            sb.append(
                formatInternationalCurrency(amount.toDouble())
            )
        } else {
            sb.append("0")
        }
        return sb.toString()
    }

    fun formatAmountMasked(amount: Double?): String {
        val sb = StringBuilder()
        sb.append("Rp")
        repeat(formatInternationalCurrency(amount.orNil).length) { sb.append("*") }
        return sb.toString()
    }

    private fun formatInternationalCurrency(d: Double): String {
        val round = (abs(d) * 1000.0).roundToLong().toDouble()
        return DecimalFormat(
            "###,###.###",
            DecimalFormatSymbols.getInstance(Locale("in", "ID"))
        ).format((round / 1000.0))
    }

    fun copyToClipboard(text: String?, context: Context, toastText: String?) {
        val clipboard: ClipboardManager =
            context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        val clip = ClipData.newPlainText("Data", text)
        clipboard.setPrimaryClip(clip)
        if (toastText != null && toastText.isNotEmpty()) {
            Toast.makeText(context, toastText, Toast.LENGTH_SHORT).show()
        }
    }

    fun <T> jsonToObject(json: String, type: Type): T? = try {
        Gson().fromJson(json, type)
    } catch (ex: Exception) {
        FirebaseCrashlytics.getInstance().recordException(ex)
        null
    }

    fun validatePhoneNumber(phone: String): Boolean {
        val lengthValid = phone.length in 7..14
        val prefixValid = if (BuildConfig.DEBUG) {
            phone.startsWith("08") || phone.startsWith("8") || phone.startsWith("9") ||
                phone.startsWith(
                    "62"
                ) || phone.startsWith("+62") || phone.startsWith("11")
        } else {
            phone.startsWith("08") || phone.startsWith("8") || phone.startsWith("62") ||
                phone.startsWith(
                    "+62"
                ) || phone.startsWith("11")
        }

        return lengthValid && prefixValid
    }

    /**
     * Checks if both T1 and T2 are not null and executes the block
     */
    inline fun <T1 : Any, T2 : Any, R : Any> safeLet(p1: T1?, p2: T2?, block: (T1, T2) -> R?): R? =
        if (p1 != null && p2 != null) block(p1, p2) else null

    /**
     * Checks if T1, T2 and T3 are not null and executes the block
     */
    inline fun <T1 : Any, T2 : Any, T3 : Any, R : Any> safeLet(
        p1: T1?,
        p2: T2?,
        p3: T3?,
        block: (T1, T2, T3) -> R?
    ): R? = if (p1 != null && p2 != null && p3 != null) block(p1, p2, p3) else null

    data class TextDecorations(
        @ColorInt val textColor: Int? = null,
        val bold: Boolean = false,
        val underline: Boolean = false,
        val onClick: ((view: View) -> Unit)? = null,
        val strikethrough: Boolean = false
    )

    fun decorateTextString(
        text: String,
        decorations: HashMap<String, TextDecorations>
    ): SpannableStringBuilder {
        val builder = SpannableStringBuilder()
        builder.append(text)
        decorations.entries.forEach { decoration ->
            val subSpan = decoration.key
            val spanDecorations = decoration.value
            if (subSpan.isNotNullOrBlank()) {
                val startingIndex = text.indexOf(subSpan)
                val endingIndex = startingIndex + (subSpan.length)
                if (startingIndex > 0 && endingIndex > 0) {
                    spanDecorations.textColor?.let {
                        builder.setSpan(
                            ForegroundColorSpan(it),
                            startingIndex,
                            endingIndex,
                            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                        )
                    }
                    if (spanDecorations.bold) {
                        builder.setSpan(
                            StyleSpan(Typeface.BOLD),
                            startingIndex,
                            endingIndex,
                            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                        )
                    }
                    if (spanDecorations.underline) {
                        builder.setSpan(
                            UnderlineSpan(),
                            startingIndex,
                            endingIndex,
                            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                        )
                    }
                    if (spanDecorations.strikethrough) {
                        builder.setSpan(
                            StrikethroughSpan(),
                            startingIndex,
                            endingIndex,
                            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                        )
                    }
                    spanDecorations.onClick?.let {
                        val clickableSpan = object : ClickableSpan() {
                            override fun onClick(view: View) {
                                it.invoke(view)
                            }

                            override fun updateDrawState(ds: TextPaint) {
                                spanDecorations.textColor?.let { color -> ds.color = color }
                                if (spanDecorations.bold) {
                                    ds.typeface = Typeface.DEFAULT_BOLD
                                }
                                ds.isUnderlineText = spanDecorations.underline
                            }
                        }
                        builder.setSpan(
                            clickableSpan,
                            startingIndex,
                            endingIndex,
                            Spannable.SPAN_INCLUSIVE_INCLUSIVE
                        )
                    }
                }
            }
        }
        return builder
    }

    fun cleanBalance(price: String): String = price.replace(".", "")
        .replace(",", ".")

    fun <T> debounce(
        waitMs: Long = 300L,
        coroutineScope: CoroutineScope,
        destinationFunction: (T) -> Unit
    ): (T) -> Unit {
        var debounceJob: Job? = null
        return { param: T ->
            debounceJob?.cancel()
            debounceJob = coroutineScope.launch {
                delay(waitMs)
                destinationFunction(param)
            }
        }
    }

    fun launchBrowser(context: Context, url: String) {
        val builder: CustomTabsIntent.Builder = CustomTabsIntent.Builder()
        builder.enableUrlBarHiding()
        builder.setToolbarColor(context.getColorCompat(R.color.colorPrimary))

        val customTabsIntent: CustomTabsIntent = builder.build()

        try {
            customTabsIntent.launchUrl(context, Uri.parse(url))
        } catch (e: Exception) {
            launchUriIntent(context, Uri.parse(url))
        }
    }

    fun <T> tryToGetValueOrDefault(
        tryBlock: () -> T?,
        defaultValue: T?,
        catchBlock: (() -> Unit)? = null
    ): T? = try {
        tryBlock()
    } catch (ex: Exception) {
        FirebaseCrashlytics.getInstance().recordException(ex)
        catchBlock?.invoke()
        defaultValue
    }

    fun getPhoneNumber(): String = sharedPreferences.get("phone_number", "")

    fun getBusinessName(): String = sharedPreferences.get("store_name", "")

    fun getBusinessNameForSelectedSerialNumber(): String =
        sharedPreferences.get("store_name_sn", "")

    fun getBusinessAddress(): String = sharedPreferences.get("store_address", "")

    fun setBusinessAddress(storeAddress: String) {
        sharedPreferences.put("store_address", storeAddress)
    }

    fun getBeneficiaryName(): String = sharedPreferences.get("beneficiary_name", "")

    fun getDeviceId(): String = sharedPreferences.get("device_id", "")

    fun getDeviceSerialNumber(): String = sharedPreferences.get("serial_number", "")

    // hardcoded value during development
    fun getTerminalId(): String {
        val panaceaTid = sharedPreferences.get("t_id", "-")
        return if (panaceaTid.length > 3) {
            panaceaTid
        } else {
            getTmsTerminalId()
        }
    }

    fun setTerminalId(terminalId: String) {
        sharedPreferences.put("t_id", terminalId)
    }

    /*
     * terminal id set from TMS can be used to validate TMS deviceId with backend db terminal id
     */
    fun getTmsTerminalId(): String = sharedPreferences.get("TMS_TERMINAL_ID", "-")

    fun setTmsTerminalId(t_id: String) {
        sharedPreferences.put("TMS_TERMINAL_ID", t_id)
    }

    fun getMerchantId(): String =
        sharedPreferences.get("MERCHANT_ID", BuildConfig.DEFAULT_MERCHANT_ID)

    fun setMerchantId(merchantId: String) {
        sharedPreferences.put("MERCHANT_ID", merchantId)
    }

    fun getWorkKey(): String = EncryptedPreferencesHelper.get("TWK", "-")

    fun setWorkKey(masterKey: String) {
        EncryptedPreferencesHelper.put("TWK", masterKey)
    }

    fun getConnectedDeviceMasterKey(serialNumber: String): String = try {
        sharedPreferences.get("TMK$serialNumber", "")
    } catch (e: Exception) {
        getMasterKey()
    }

    fun setConnectedDeviceMasterKey(reactivationKey: String, serialNumber: String) {
        try {
            sharedPreferences.put("TMK$serialNumber", reactivationKey)
        } catch (e: Exception) {
            setTerminalMasterKey(reactivationKey)
        }
    }

    fun getMasterKey(): String = EncryptedPreferencesHelper.get("TMK", "")

    fun setTerminalMasterKey(masterKey: String) {
        EncryptedPreferencesHelper.put("TMK", masterKey)
    }

    fun setTerminalMerchantPhone(phoneNumber: String) {
        sharedPreferences.put("TMS_MERCHANT_PHONE", phoneNumber)
    }

    fun getTerminalMerchantPhone(): String = sharedPreferences.get("TMS_MERCHANT_PHONE", "")

    fun getTickerCloseTime(): Long = sharedPreferences.getLong(TICKER_CLOSE_TIME, 0L)

    fun getHardwareSerialNumber(): String = sharedPreferences.get(HARDWARE_SERIAL_NUMBER, "")

    fun setHardwareSerialNumber(serialNumber: String) {
        sharedPreferences.put(HARDWARE_SERIAL_NUMBER, serialNumber)
    }

    fun setTickerCloseTime() {
        sharedPreferences.put(
            TICKER_CLOSE_TIME,
            System.currentTimeMillis()
        )
    }

    fun byte2HexStr(var0: ByteArray?): String? = if (var0 == null) {
        ""
    } else {
        var var1 = ""
        val var2 = java.lang.StringBuilder("")
        for (var3 in var0.indices) {
            var1 = Integer.toHexString(var0[var3] and 255)
            var2.append(if (var1.length == 1) "0$var1" else var1)
        }
        var2.toString().uppercase(Locale.getDefault()).trim { it <= ' ' }
    }

    fun formatPanNumber(pan: String?): String {
        if (pan.isNullOrEmpty()) {
            return ""
        }
        val sanitizedPan = pan.replace(" ", "")

        return sanitizedPan.chunked(4).joinToString(" ")
    }

    fun isCardExpired(expiredDate: String?): Boolean {
        if (expiredDate != null && expiredDate.length == 4) {
            val dateYYMM: String = DateTimeUtils.getCurrentDateYYMM()
            val currentYearMonth = dateYYMM.toInt()
            val yearMonth: Int = expiredDate.toInt()
            if (yearMonth < currentYearMonth) {
                return true
            }
        }
        return false
    }

    fun maskCardNo(card: String?): String {
        if (card.isNullOrEmpty()) {
            return ""
        }
        // check length to avoid crash
        if (card.length < 10) {
            return card
        }

        val firstSix = card.substring(0, 6)
        val lastFour = card.substring(card.length - 4)
        val maskedPart = "*".repeat(card.length - 10)
        return "$firstSix$maskedPart$lastFour"
    }

    fun maskSensitiveInfo(input: String, numCharsToKeep: Int = 4): String {
        val maskChar = '*'
        if (input.isNullOrEmpty()) return "-"

        return if (input.length <= 4) {
            if (input.length == 1) {
                maskChar.toString()
            } else {
                input.substring(0, input.length - 1) + maskChar
            }
        } else {
            val maskedPart = maskChar.toString().repeat(input.length - numCharsToKeep)
            input.substring(0, numCharsToKeep) + maskedPart
        }
    }

    fun getDeviceServiceProvider(): String? =
        sharedPreferences.getString("DEVICE_SERVICE", "ANDROID")

    fun getDeviceBrand(): String {
        try {
            if (getDeviceServiceProvider().equals(BtAnalyticConstant.MOREFUN, true)) {
                return "MoreFun"
            } else if (getDeviceServiceProvider().equals(BtAnalyticConstant.TIANYU, true)) {
                return "Tianyu"
            } else if (getDeviceServiceProvider().equals(BtAnalyticConstant.MINIATMPRO, true)) {
                return "MiniAtmPro"
            } else {
                return Build.MANUFACTURER
            }
        } catch (e: Exception) {
            e.printStackTrace()
            return "Tianyu"
        }
    }

    fun getDeviceModel(): String? {
        try {
            if (getDeviceServiceProvider().equals(BtAnalyticConstant.MOREFUN, true)) {
                return "MP63"
            } else if (getDeviceServiceProvider().equals(BtAnalyticConstant.TIANYU, true)) {
                return "MP45"
            } else {
                return Build.MODEL
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return "Tianyu"
    }

    fun hexStr2Byte(hexString: String?): ByteArray? {
        if (hexString == null || hexString.length == 0) {
            return byteArrayOf(0)
        }
        val hexStrTrimed = hexString.replace(" ", "")
        run {
            var hexStr = hexStrTrimed
            var len = hexStrTrimed.length
            if (len % 2 == 1) {
                hexStr = hexStrTrimed + "0"
                ++len
            }
            var highChar: Char
            var lowChar: Char
            var high: Int
            var low: Int
            val result = ByteArray(len / 2)
            var s: String
            var i = 0
            while (i < hexStr.length) {
                // read high byte and low byte to convert
                highChar = hexStr[i]
                lowChar = hexStr[i + 1]
                high = charToInt(highChar)
                low = charToInt(lowChar)
                result[i / 2] = (high * 16 + low).toByte()
                i++
                i++
            }
            return result
        }
    }

    fun charToInt(c: Char): Int = if (c >= '0' && c <= '9' ||
        c == '=' // for track2
    ) {
        c - '0'
    } else if (c >= 'a' && c <= 'f') {
        c - 'a' + 10
    } else if (c >= 'A' && c <= 'F') {
        c - 'A' + 10
    } else {
        0
    }

    fun getTrack2(track2: String): String? {
        var track2Formatted = track2.replace("=", "D")
        if (track2Formatted.endsWith("F")) {
            return track2.trimEnd('F').uppercase(getDefault())
        }
        return track2Formatted.uppercase(getDefault())
    }

    fun getDummyIccData(): String =
        "9F260853AD1FEE7A710B9A9F2701809F10120114A00001240000000000000000000000FF9F3704BD433F1B9F36020085950580000008009A032403219C01009F02060000000000005F2A020156820218009F1A0201569F03060000000000009F3303E0F9C89F34031E03009F3501229F1E0845303139323134328407A00000000410109F090200029F410400000026"

    fun getDummyBalanceCheckIccData(): String =
        "950580000488009F2608FE0415B39F13CA1D9F2701809F101C0101A00011000009ED3DE300000000000000000000000000000000009F370448C609C69F360204519A032405015F3401005F2A0203609F1A020360820254008407A00000060210109F02060000000000009C0130"

    fun getDummyPinblock(): String = "7490C9B52E077825"

    fun getLength(length: Int, maxBytes: Int): String? {
        val sLength = Integer.toHexString(length)
        val len = sLength.length
        return if (len > maxBytes shl 1) {
            null
        } else if (1 == len and 0x01) {
            //
            "0$sLength"
        } else {
            sLength
        }
    }

    fun setIncompleteBalanceCheckTransaction(checkBalanceRequest: CheckBalanceRequest) {
        val reversalRequest = TransactionReversalRequest(
            stan = "",
            transactionDate = DateTimeUtils.getCurrentDateYYYYMMDD(),
            terminalId = getTerminalId(),
            merchantId = getMerchantId(),
            transactionType = TransactionType.BALANCE_INQUIRY.type,
            reversalIccData = checkBalanceRequest.iccData,
            cardNumber = checkBalanceRequest.cardNumber,
            cardExpiry = checkBalanceRequest.cardExpiry,
            pinBlock = checkBalanceRequest.pinBlock,
            track2Data = checkBalanceRequest.track2Data,
            accountType = checkBalanceRequest.accountType
        )
        setIncompleteTransaction(reversalRequest)
    }

    /*
     *
     * keep ongoing transaction in shared preference to complete reversal incase of device shutdown or backend going down before sending response to frontend
     * ideally, it should be done with ISO data packet. It needs to be done later by storing every transaction ISO data in db.
     * Note: Only most recent transaction is allowed to be reversed. User should be able to create new transaction unless previous incomplete transaction is reversed
     */
    private fun setIncompleteTransaction(reversalRequest: TransactionReversalRequest) {
        if (reversalRequest == null) {
            Log.d("INCOMPLETE", "set: incomplete txn []")
            sharedPreferences.put(Constants.KEY_INCOMPLETE_TRANSACTION, "")
        } else {
            val json: String = Gson().toJson(reversalRequest)
            Log.d("INCOMPLETE", "set: incomplete txn $json")
            sharedPreferences.put(Constants.KEY_INCOMPLETE_TRANSACTION, json)
        }
    }

    fun setIncompleteTransferPostingTransaction(
        transferMoneyRequestResponseBody: TransferMoneyRequestResponseBody,
        transType: String
    ) {
        val reversalRequest = TransactionReversalRequest(
            stan = "",
            transactionDate = DateTimeUtils.getCurrentDateYYYYMMDD(),
            terminalId = getTerminalId(),
            merchantId = getMerchantId(),
            transactionType = if (transType == "CASH_WITHDRAWAL") {
                TransactionType.CASH_WITHDRAWAL_POSTING.type
            } else {
                TransactionType.TRANSFER_POSTING.type
            },
            reversalIccData = transferMoneyRequestResponseBody.iccData,
            cardNumber = transferMoneyRequestResponseBody.cardNumber,
            cardExpiry = transferMoneyRequestResponseBody.cardExpiry,
            pinBlock = transferMoneyRequestResponseBody.pinBlock,
            track2Data = transferMoneyRequestResponseBody.track2Data,
            accountType = transferMoneyRequestResponseBody.accountType
        )
        setIncompleteTransaction(reversalRequest)
    }

    fun clearIncompleteTransaction() {
        Log.d("INCOMPLETE", "clear: incomplete txn list")
        sharedPreferences.put(Constants.KEY_INCOMPLETE_TRANSACTION, "")
        val data = getIncompleteTransaction()
        val iccCorrupt = getBooleanConfig(MiniatmDebugSettingActivity.TEST_CORRUPT_ICC)
        val forceIncomplete = getBooleanConfig(MiniatmDebugSettingActivity.TEST_FORCE_INCOMPLETE)
        Log.d(
            "INCOMPLETE",
            "clear: list [$data] debug flag: $iccCorrupt force reversal: $forceIncomplete"
        )
    }

    fun getIncompleteTransaction(): TransactionReversalRequest? {
        try {
            val json = sharedPreferences.getString(Constants.KEY_INCOMPLETE_TRANSACTION, "")

            if (json.isNullOrBlank()) {
                return null
            }

            val type = object : TypeToken<TransactionReversalRequest>() {}.type
            val transactionRequest: TransactionReversalRequest = Gson().fromJson(json, type)
//            clearIncompleteTransaction()
            return transactionRequest
        } catch (e: Exception) {
            clearIncompleteTransaction()
            Log.d("REVERSAL", "failed to get incomplete transaction data")
            return null
        }
    }

    fun makeSectionOfTextBold(
        completeText: String,
        textToBold: String?,
        actionSpan: ClickableSpan
    ): SpannableStringBuilder {
        val builder = SpannableStringBuilder()
        if (textToBold.isNotNullOrBlank()) {
            val testText = completeText.lowercase()
            val testTextToBold = textToBold?.lowercase().orEmpty()
            val startingIndex = testText.indexOf(testTextToBold)
            val endingIndex = startingIndex + testTextToBold.length
            if (startingIndex < 0 || endingIndex < 0) {
                builder.append(completeText)
            } else {
                builder.append(completeText)
                builder.setSpan(
                    actionSpan,
                    startingIndex,
                    endingIndex,
                    Spannable.SPAN_INCLUSIVE_INCLUSIVE
                )
            }
        } else {
            builder.append(completeText)
        }
        return builder
    }

    fun makeSectionOfTextClickable(
        completeText: String,
        textToClick: String?,
        actionSpan: ClickableSpan,
        boldClickableText: Boolean = true
    ): SpannableStringBuilder {
        val builder = SpannableStringBuilder()
        builder.append(completeText)
        if (textToClick.isNotNullOrBlank()) {
            if (boldClickableText) {
                builder.boldText(textToClick.orEmpty())
            }
            val startingIndex = completeText.indexOf(textToClick ?: "")
            val endingIndex = startingIndex + (textToClick?.length.orNil)
            if (startingIndex > 0 && endingIndex > 0) {
                builder.setSpan(
                    actionSpan,
                    startingIndex,
                    endingIndex,
                    Spannable.SPAN_INCLUSIVE_INCLUSIVE
                )
            }
        }
        return builder
    }

    fun beautifyPhoneNumber(phone: String): String? {
        var phone = phone
        return try {
            if (phone.isBlank()) return ""
            if (phone.startsWith("0") && phone.length > 8) {
                return phone
            } else if (phone.startsWith("+62")) {
                phone = phone.replace("+62", "0")
            } else if (phone.length > 8) {
                phone = "0$phone"
            }
            phone
        } catch (ex: java.lang.Exception) {
            ex.printStackTrace()
            FirebaseCrashlytics.getInstance().log(ex.message!!)
            phone
        }
    }

    fun showBottomSheet(bottomSheetDialog: DialogInterface, state: Int) {
        val dialog = bottomSheetDialog as BottomSheetDialog
        val bottomSheet =
            dialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
        bottomSheet?.let {
            dialog.behavior.state = state
            dialog.behavior.peekHeight = Resources.getSystem().displayMetrics.heightPixels - 100
        }
    }

    fun String.hexStringToByteArray(): ByteArray {
        val len = this.length
        val byteArray = ByteArray(len / 2)
        for (i in 0 until len step 2) {
            byteArray[i / 2] =
                ((this[i].digitToInt(16) shl 4) + this[i + 1].digitToInt(16)).toByte()
        }
        return byteArray
    }

    fun ByteArray?.toHexString(): String {
        if (this == null) return ""
        return this.joinToString("") { "%02x".format(it) }.uppercase()
    }

    fun isMagCardEnabled(): Boolean = true

    fun isMagCardEnabled(flag: Boolean) {
        sharedPreferences.put("MAG_CARD_ENABLED", flag)
    }

    fun setMagEnabledPrefix(prefixList: String) {
        sharedPreferences.edit().putString("MAG_CARD_PREFIX", prefixList).apply()
    }

    fun setApplicationId(aid: String) {
        if (aid.isNotNullOrBlank()) {
            setLoadedConfig(false)
            sharedPreferences.edit().putString("EMV_AID", aid).apply()
        }
    }

    fun getApplicationId(): String? = sharedPreferences.getString("EMV_AID", "")

    fun getInstalledVersion(): Int {
        // feature started from 2.0.1 version update 27 version code
        return this.sharedPreferences.getInt("installed_version_code", 27)
    }

    fun setInstalledVersion(versionCode: Int) {
        sharedPreferences.edit().putInt("installed_version_code", versionCode).apply()
    }

    fun setInstallVersionName(versionName: String?) {
        sharedPreferences.edit().putString("installed_version_name", versionName).apply()
    }

    fun setBackground(context: Context?, view: View, i: Int) {
        val mutate = ContextCompat.getDrawable(
            context!!,
            i
        )?.mutate()
        if (Build.VERSION.SDK_INT >= 16) {
            view.background = mutate
        } else {
            view.setBackgroundDrawable(mutate)
        }
    }

    fun getCardPinActivity(cardNumber: String?): Class<*> = if (isCardReader()) {
        ExternalPinpadActivity::class.java
    } else if (Build.MANUFACTURER == Constants.DEVICE_MANUFACTURER_VERIFONE ||
        Build.MANUFACTURER == Constants.DEVICE_MANUFACTURER_MOREFUN ||
        cardNumber?.length == 16
    ) {
        CardPinFirebaseDynamicActivity::class.java
    } else {
        CardPinDynamicActivity::class.java
    }

    fun getRouterClass(): Class<*> = if (isCardReader()) {
        BTRouterActivity::class.java
    } else {
        RouterActivity::class.java
    }

    fun hasEmptyPin(pinBlock: String): Boolean = pinBlock == "0000000000000000"

    fun isTransactionCancelled(pinBlock: String): Boolean = pinBlock.isNullOrEmpty()

    fun saveUserRegisteredDevices(devices: List<DeviceItem>) {
        val deviceJson = Gson().toJson(devices)
        Log.d("REGISTERED_DEVICES", "saveUserRegisteredDevices: $deviceJson")
        sharedPreferences.put(REGISTERED_DEVICES, deviceJson)
    }

    fun getUserRegisteredDevices(): List<DeviceItem> {
        try {
            val deviceJson = sharedPreferences.getString(REGISTERED_DEVICES, "")
            return Gson().fromJson(deviceJson, object : TypeToken<List<DeviceItem>>() {}.type)
        } catch (e: Exception) {
            return emptyList()
        }
    }

    fun copyBundle(source: Bundle, destination: Bundle) {
        for (key in source.keySet()) {
            when (val value = source.get(key)) {
                is String -> destination.putString(key, value)
                is Int -> destination.putInt(key, value)
                is Boolean -> destination.putBoolean(key, value)
                is Long -> destination.putLong(key, value)
                else -> {}
            }
        }
    }

    fun isCardReader(): Boolean = Build.MANUFACTURER != Constants.DEVICE_MANUFACTURER_VERIFONE &&
        Build.MANUFACTURER != Constants.DEVICE_MANUFACTURER_PAX &&
        Build.MANUFACTURER != Constants.DEVICE_MANUFACTURER_MOREFUN

    fun isMultipleCardReaderDeviceRegistered(): Boolean = getUserRegisteredDevices().size > 1

    fun isDeviceRegisteredForFirstTime(): Boolean {
        Log.d("REGISTERED_DEVICES", "isNoDeviceRegistered: ${getUserRegisteredDevices()}")
        return getUserRegisteredDevices().size == 1 &&
            getUserRegisteredDevices()[0].terminalId.isNullOrEmpty()
    }

    fun findConnectedDeviceItem(
        deviceName: String,
        registeredDevices: List<DeviceItem>?
    ): DeviceItem? {
        if (registeredDevices.isNullOrEmpty()) return null
        registeredDevices.forEach { device ->
            val lastSixDigits = device.serialNumber?.takeLast(4)
            val deviceNameSn = deviceName.filter { char -> char.isDigit() }.takeLast(4)
            if (lastSixDigits == deviceNameSn) {
                return device
            }
        }
        return null
    }

    fun extractLinkFromHtml(bodyText: String): String? {
        val pattern = Pattern.compile("href=\"(.*?)\"")
        val matcher = pattern.matcher(bodyText)
        return if (matcher.find()) {
            matcher.group(1)
        } else {
            null
        }
    }

    fun showDialogIfActivityAlive(activity: Activity, dialog: Dialog?) {
        if (dialog == null) {
            return
        }
        if (!activity.isFinishing && !activity.isDestroyed) {
            dialog.show()
        }
    }

    fun trackDeviceActivationError(failureType: TmsFailureType) {
        try {
            val props = HashMap<String, String>()
            val name = BluetoothDevices.getPairedCardReaderList()?.get(0)?.name
            props[BtAnalyticConstant.EDC_BRAND] =
                if (BluetoothDevices.getPairedCardReaderList()?.get(0)?.name?.contains(
                        BtAnalyticConstant.MP_,
                        true
                    ) == true
                ) {
                    BtAnalyticConstant.MOREFUN
                } else {
                    BtAnalyticConstant.TIANYU
                }
            props[BtAnalyticConstant.SAKU_NAME] = name ?: ""
            props["error_code"] = failureType.code
            props["source"] = failureType.name
            BtAnalytics.trackEventMobile("device_mapping_mismatch", props)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun getTransactionTypeAnalytics(transactionType: String): String {
        if (transactionType == TransactionType.TRANSFER_INQUIRY.type) {
            return CardAnalyticsConstants.TRANSFER
        } else if (transactionType == TransactionType.CASH_WITHDRAWAL.type) {
            return TransactionType.CASH_WITHDRAWAL.type
        } else {
            return CardAnalyticsConstants.BALANCE_CHECK
        }
    }

    fun getSettlementBankAccount(): BankAccount? {
        getSettlementBankAccountFromJson()?.let {
            return BankAccount(
                accountNumber = it.accountNumber,
                bankCode = it.bankCode,
                bankName = it.bankName,
                accountHolderName = it.beneficiaryName
            )
        }
        return null
    }

    fun isFixedTerminal(): Boolean = Build.MANUFACTURER == Constants.DEVICE_MANUFACTURER_MOREFUN ||
        Build.MANUFACTURER == Constants.DEVICE_MANUFACTURER_VERIFONE ||
        Build.MANUFACTURER == Constants.DEVICE_MANUFACTURER_PAX

    fun hasPairedPrinter(): Boolean = isFixedTerminal() || BluetoothDevices.hasPairedPrinter()

    fun setSettlementBankAccount(bankAccount: SettlementBankAccount) {
        sharedPreferences.put(SETTLEMENT_BANK_ACCOUNT, Gson().toJson(bankAccount))
    }

    fun getSettlementBankAccountFromJson(): SettlementBankAccount? = Gson().fromJson(
        sharedPreferences.getString(SETTLEMENT_BANK_ACCOUNT, ""),
        SettlementBankAccount::class.java
    )

    fun setConfirmCashWithdrawalAccount() {
        sharedPreferences.put(CONFIRM_CASH_WITHDRAWAL_ACCOUNT, true)
    }

    fun setCashWithdrawalFirstTimePopupShown() {
        sharedPreferences.put(CASH_WITHDRAWAL_FIRST_TIME_POPUP, true)
    }

    fun clearDataBeforeLogout() {
        EncryptedPreferencesHelper.put("uuid", "")
        EncryptedPreferencesHelper.put(BUKUWARUNG_TOKEN, "")
        EncryptedPreferencesHelper.put("payment_account_id", "")
        sharedPreferences.put("t_id", "")
        sharedPreferences.put("user_id", "")
        sharedPreferences.put("show_komisi_agen_dialog", true)
        sharedPreferences.put(Utils.SETTLEMENT_BANK_ACCOUNT, "")
        sharedPreferences.put(Utils.CONFIRM_CASH_WITHDRAWAL_ACCOUNT, false)
        setLoadedConfig(false)
        setShouldRetryPostFcmToken(true)
        if (!isPaxVerifoneDevice()) {
            FirebaseMessaging.getInstance().deleteToken()
                .addOnCompleteListener { _: Task<Void?> -> }
        }
        TikTokBusinessSdk.logout()
        EncryptedPreferencesHelper.clear()
        sharedPreferences.clear()
    }

    fun setShouldRetryPostFcmToken(value: Boolean) {
        sharedPreferences.put(SHOULD_RETRY_POST_FCM_TOKEN, value)
    }

    fun getShouldRetryPostFcmToken(): Boolean =
        sharedPreferences.getBoolean(SHOULD_RETRY_POST_FCM_TOKEN, true)

    fun getFcmDeviceId(): String {
        var deviceId: String? = sharedPreferences.getString(FCM_DEVICE_ID, null)
        if (deviceId.isNullOrEmpty()) {
            val mostSigBits = System.currentTimeMillis()
            val leastSigBits = UUID.randomUUID().leastSignificantBits
            deviceId = UUID(mostSigBits, leastSigBits).toString()
            sharedPreferences.put(FCM_DEVICE_ID, deviceId)
        }
        return deviceId
    }

    fun getLastStoredFcmToken(): String? =
        this.sharedPreferences.getString(LAST_STORED_FCM_TOKEN, null)

    fun setLastStoredFcmToken(value: String?) {
        sharedPreferences.put(LAST_STORED_FCM_TOKEN, value)
    }

    fun isPaxVerifoneDevice(): Boolean = (
        Build.MANUFACTURER == Constants.DEVICE_MANUFACTURER_VERIFONE ||
            Build.MANUFACTURER == Constants.DEVICE_MANUFACTURER_PAX
        )

    fun isMfAndroidDevice(): Boolean = Build.MANUFACTURER == Constants.DEVICE_MANUFACTURER_MOREFUN

    var edcOrderId: String
        get() {
            val orderId = sharedPreferences.get("edc_order_id", "")
            return orderId
        }
        set(value) {
            sharedPreferences.put("edc_order_id", value)
        }

    var setBankAccount: Boolean
        get() {
            return sharedPreferences.getBoolean(SET_BANK_ACCOUNT + getPaymentAccountId(), false)
        }
        set(value) {
            sharedPreferences.put(SET_BANK_ACCOUNT + getPaymentAccountId(), value)
        }

    var isNoSakuDeviceRegisteredBTShown: Boolean
        get() = sharedPreferences.getBoolean(NO_SAKU_DEVICE_REGISTERED_BT, false)
        set(value) = sharedPreferences.put(NO_SAKU_DEVICE_REGISTERED_BT, value)

    fun dashDividedString(str1: String?, str2: String?): String =
        String.format("%s - %s", str1, str2)

    fun hasLocationPermission(): Boolean {
        if (Build.VERSION.SDK_INT < 23) {
            return true
        }
        val context = context ?: return false
        return (
            context.checkSelfPermission(Manifest.permission.ACCESS_FINE_LOCATION) ==
                PackageManager.PERMISSION_GRANTED &&
                context.checkSelfPermission(Manifest.permission.ACCESS_COARSE_LOCATION) ==
                PackageManager.PERMISSION_GRANTED
            )
    }

    fun requestLatLongPermission(activity: Activity) {
        if (Build.VERSION.SDK_INT >= 23) {
            activity.requestPermissions(
                arrayOf<String>(
                    Manifest.permission.ACCESS_COARSE_LOCATION,
                    Manifest.permission.ACCESS_FINE_LOCATION
                ),
                PermissionConst.REQUEST_CODE_LAT_LONG
            )
        }
    }

    fun bwLog(tag: String, msg: String) {
        try {
            if (BuildConfig.DEBUG) {
                msg.let { Log.d(tag, it) }
            } else {
                msg.let { FirebaseCrashlytics.getInstance().log("[$tag] $it") }
            }
        } catch (e: Exception) {
            FirebaseCrashlytics.getInstance().recordException(e)
        }
    }

    fun bwLog(msg: String? = null, e: Exception? = null) {
        try {
            if (BuildConfig.DEBUG) {
                msg?.let { Log.i("EdcApp", it) }
                e?.printStackTrace()
            } else {
                msg?.let { FirebaseCrashlytics.getInstance().log(it) }
                e?.let { FirebaseCrashlytics.getInstance().recordException(it) }
            }
        } catch (e: Exception) {
            Log.e("EdcApp", "${e.message}")
        }
    }

    fun extractPaymentAccountIdTerminalIdSerialNumber(
        deviceName: String,
        registeredDevices: List<DeviceItem>
    ): DeviceInfo? {
        val deviceNameSn = deviceName.filter { char -> char.isDigit() }.takeLast(6)

        val matchedDevice = registeredDevices.find { device ->
            device.serialNumber?.takeLast(6) == deviceNameSn
        }

        return matchedDevice?.let {
            DeviceInfo(
                it.serialNumber.orEmpty(),
                it.paymentAccountId.orEmpty(),
                it.terminalId.orEmpty()
            )
        }
    }

    fun getActivationMasterKey(tid: String): String? =
        this.sharedPreferences.getString("ACTIVATION_KEY$tid", null)

    fun setActivationMasterKey(tid: String?, value: String) {
        sharedPreferences.put("ACTIVATION_KEY$tid", value)
    }

    fun isTokenExpired(): Boolean = try {
        val idToken = EncryptedPreferencesHelper.get(BUKUWARUNG_TOKEN, "")
        val jwt = JWT(idToken)
        val expirationTime = jwt.expiresAt
        expirationTime?.before(Date()) == true
    } catch (e: Exception) {
        bwLog(e = e)
        true
    }

    fun setBusinessName(storeName: String) {
        sharedPreferences.put("store_name", storeName)
    }

    fun checkIfPinNotAllowed(pinEntered: String): Boolean {
        val pinNotAllowedList = listOf(
            "123456",
            "234567",
            "345678",
            "456789",
            "987654",
            "876543",
            "765432",
            "654321",
            "111111",
            "222222",
            "333333",
            "444444",
            "555555",
            "666666",
            "777777",
            "888888",
            "999999",
            "000000"
        )
        return pinNotAllowedList.contains(pinEntered)
    }

    fun getDeviceTypeBySerialNumber(): String {
        val serialNumber = getHardwareSerialNumber()
        return when {
            serialNumber.startsWith("63") -> "MoreFun"
            serialNumber.startsWith("98") -> Constant.MOREFUN_ANDROID
            serialNumber.startsWith("V9E") -> Constant.VERIFONE
            serialNumber.startsWith("12") || serialNumber.startsWith("524") -> "Tianyu"
            serialNumber.startsWith("18") -> Constant.PAX
            else -> ""
        }
    }

    fun getAppVersion(context: Context, packageName: String): String = try {
        val packageInfo = context.packageManager.getPackageInfo(packageName, 0)
        packageInfo.versionName.orEmpty()
    } catch (e: PackageManager.NameNotFoundException) {
        e.message ?: "NameNotFoundException"
    }

    private fun isAtmPro(): Boolean = AppConfig.current.appVariant == AppVariant.ATM_PRO

    fun getEntryPoint(variantConfig: VariantConfig): String =
        "entryPoint=${variantConfig.variantIdentifier}"

    fun isBuku(): Boolean = AppConfig.current.appVariant == AppVariant.BUKU

    fun isNusa(): Boolean = AppConfig.current.appVariant == AppVariant.NUSA_CITA

    fun storeEDCMiniATMProClientName(clientName: String?) {
        sharedPreferences.put(MINI_ATM_PRO_CLIENT_NAME, clientName)
    }

    fun getEDCMiniATMProClientName(): String? =
        sharedPreferences.getString(MINI_ATM_PRO_CLIENT_NAME, null)

    fun getDeviceNameBasedOnVendor(vendor: String, sn: String): String {
        val firstPart =
            if (vendor.contains("MoreFun", true)) {
                "MP"
            } else if (vendor.contains(
                    "Tianyu",
                    true
                )
            ) {
                "Suppay"
            } else {
                vendor
            }
        val secondPart = if (vendor.contains("MoreFun", true)) {
            if (sn.length >= 8) sn.takeLast(8) else sn
        } else if (vendor.contains("Tianyu", true)) {
            if (sn.length >= 6) sn.takeLast(6) else sn
        } else {
            sn
        }
        return if (vendor.contains("MoreFun", true)) {
            firstPart.plus("-")
                .plus(secondPart)
        } else {
            firstPart.plus("_").plus(secondPart)
        }
    }

    /**
     * Determines if a device should use Android activation flow instead of Saku connection flow.
     * This is based on the device vendor and specific device characteristics.
     *
     * @param vendor The device vendor string
     * @param serialNumber The device serial number
     * @return true if device should use Android activation flow, false otherwise
     */
    fun shouldUseAndroidActivationFlow(vendor: String, serialNumber: String?): Boolean =
        !isSakuDeviceBasedOnVendor(vendor) ||
            ((isAtmPro() || isNusa()) && isFixedTerminal()) || serialNumber?.startsWith(
                "98",
                true
            ) == true

    fun isBusinessNameChanged(): Boolean =
        sharedPreferences.getBoolean("is_business_name_changed", false)

    fun setBusinessNameChanged(isChanged: Boolean) {
        sharedPreferences.put("is_business_name_changed", isChanged)
    }

    fun setBusinessNameForSelectedSerialNumber(businessName: String) {
        sharedPreferences.put("store_name_sn", businessName)
    }

    fun getBusinessNameForSelectedSerialNumber(serialNumber: String): String {
        val businessNamesJson = sharedPreferences.get("store_name_sn_map", "{}")
        val businessNamesMap = jsonToMap(businessNamesJson)
        return businessNamesMap[serialNumber] ?: ""
    }

    fun setBusinessNameForSelectedSerialNumber(serialNumber: String, businessName: String) {
        val businessNamesJson = sharedPreferences.get("store_name_sn_map", "{}")
        val businessNamesMap = jsonToMap(businessNamesJson).toMutableMap()
        businessNamesMap[serialNumber] = businessName
        sharedPreferences.put("store_name_sn_map", mapToJson(businessNamesMap))
    }

    private fun jsonToMap(json: String): Map<String, String> = try {
        val type = object : TypeToken<Map<String, String>>() {}.type
        Gson().fromJson(json, type) ?: emptyMap()
    } catch (e: Exception) {
        emptyMap()
    }

    private fun mapToJson(map: Map<String, String>): String = Gson().toJson(map)

    fun isSakuDeviceBasedOnVendor(vendor: String): Boolean =
        vendor.equals("MoreFun", true) || vendor.contains("Tianyu", true)

    fun hasAMoreFunSakuDevice(): Boolean = getUserRegisteredDevices().find {
        it.vendor.equals("MoreFun", true)
    } != null

    fun getTime(date: String?): Long {
        if (date == null) {
            return 0
        }
        val utcFormat = SimpleDateFormat(DateTimeUtils.YYYY_MM_DD_T_HH_MM_SS, Locale.getDefault())
        utcFormat.timeZone = TimeZone.getTimeZone("UTC")

        return utcFormat.parse(date).time
    }

    // sessionEnded value will be true only for blacklisted users to show a dialog box upon logout.
    fun clearDataAndLogout(sessionEnded: Boolean) {
        try {
            TokenManager.clear()
            // clear only necessary data before logout
            clearDataBeforeLogout()
            // after clearing the necessary date, redirect the user to login screen.
            val context = EdcApplication.instance.applicationContext
            val bundle = Bundle()
            bundle.putBoolean(LoginActivity.BUNDLE_KEY_SHOW_SESSION_ENDED_DIALOG, sessionEnded)
            val intent = Intent(context, LoginActivity::class.java)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            intent.putExtra(LoginActivity.BUNDLE_KEY_DATA, bundle)
            context.startActivity(intent)
        } catch (e: Exception) {
            bwLog(e = e)
        }
    }

    fun setThisOrderHasShownVerificationDialog(orderId: String) {
        sharedPreferences.put("show_dialog_order_verification_$orderId", true)
    }

    fun isThisOrderHasShownVerificationDialog(orderId: String): Boolean =
        sharedPreferences.get("show_dialog_order_verification_$orderId", false)

    var referralCode: String
        get() = sharedPreferences.get("referral_code", "")
        set(value) {
            sharedPreferences.put("referral_code", value)
        }

    fun getKycLandingQuery(): String = if (isAtmPro()) {
        "landing=MINIATMPRO"
    } else {
        "landing=BUKUAGEN"
    }

    /**
     * Determines whether partnership features should be restricted for a given connected device.
     *
     * This function evaluates whether the current app configuration and device properties
     * require partnership restrictions to be applied. Partnership restrictions are enforced
     * when a device is marked as a partnership user device but the current app instance
     * is not running in ATM Pro mode.
     *
     * @param connectedDeviceItem The device item representing the currently connected device.
     *                           Can be null if no device is connected. The device item contains
     *                           information about whether this is a partnership user device.
     *
     * @return `true` if partnership features should be restricted, `false` otherwise.
     *         Returns `true` when:
     *         - The connected device is marked as a partnership user (isPartnerShipUser == true)
     *         - AND the current app is NOT running in ATM Pro mode (!isAtmPro())
     *
     *         Returns `false` when:
     *         - No device is connected (connectedDeviceItem is null)
     *         - The device is not a partnership user device (isPartnerShipUser == false or null)
     *         - The app is running in ATM Pro mode (isAtmPro() == true)
     *
     * @since 1.0.0
     * @see DeviceItem.isPartnerShipUser
     */
    fun shouldRestrictPartnership(connectedDeviceItem: DeviceItem?): Boolean =
        connectedDeviceItem?.isPartnerShipUser == true && !isAtmPro()
}
