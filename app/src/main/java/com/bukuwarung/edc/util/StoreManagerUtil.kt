package com.bukuwarung.edc.util

import android.os.Build
import cn.verifone.VeristoreMsgTool.client.manager.receiver.VeristoreManager
import com.bukuwarung.edc.card.domain.model.consts.Constants
import com.bukuwarung.edc.global.EdcApplication

object StoreManagerUtil {

    fun setStoreState(isBusy: <PERSON>olean) {
        try {
            if (Build.MANUFACTURER == Constants.DEVICE_MANUFACTURER_VERIFONE) {
                VeristoreManager.isBusy(EdcApplication.instance, isBusy)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}
