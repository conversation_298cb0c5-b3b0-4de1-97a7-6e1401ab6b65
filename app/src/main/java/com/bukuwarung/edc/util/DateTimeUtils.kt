package com.bukuwarung.edc.util

import android.text.format.DateUtils
import android.util.Log
import com.google.firebase.crashlytics.FirebaseCrashlytics
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.TimeZone

object DateTimeUtils {

    private const val UTC = "UTC"
    const val YYYY_MM_DD_T_HH_MM_SS = "yyyy-MM-dd'T'HH:mm:ss"
    private const val YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss"
    const val EEE_DD_MMM_YYYY = "EEE, dd MMM yyyy"

    const val DD_MMM_YYYY_HH_MM = "dd MMM yyyy, HH:mm"
    const val DD_MMM_YY = "dd MMM yy"
    const val DD_MMM_YYYY = "dd MMM yyyy"
    const val YYYY_MM_DD = "yyyy-MM-dd"
    private const val ONE_HOUR_IN_MILLIS = (60 * 60 * 1000).toLong()
    private const val ONE_MINUTE_IN_MILLIS = 60 * 1000

    /**
     * Returns date string in the passed format.
     *
     * @param utcString, expected format is 2022-03-14T04:13:40.333
     */
    fun getStringFromUtc(utcString: String?, format: String): String {
        if (utcString == null) return ""
        /*
            Sometimes server is sending time in "yyyy-MM-dd HH:mm:ss" format
         */
        val utcStringFormat = if (utcString.contains("T")) {
            YYYY_MM_DD_T_HH_MM_SS
        } else {
            YYYY_MM_DD_HH_MM_SS
        }
        val utcFormat = SimpleDateFormat(utcStringFormat, Locale.getDefault())
        utcFormat.timeZone = TimeZone.getTimeZone(UTC)
        return try {
            val date = utcFormat.parse(utcString)
            return date?.let {
                val dateFormat = SimpleDateFormat(format, Locale.getDefault())
                dateFormat.format(it)
            } ?: run {
                FirebaseCrashlytics.getInstance().recordException(
                    ParseException("Parse null string", 0)
                )
                ""
            }
        } catch (ex: Exception) {
            FirebaseCrashlytics.getInstance().recordException(ex)
            ""
        }
    }

    fun getLocalStringFromUtc(utcDate: String?, dateFormat: String?): String? {
        if (utcDate == null) return ""
        val utcStringFormat = if (utcDate.contains("T")) {
            YYYY_MM_DD_T_HH_MM_SS
        } else {
            YYYY_MM_DD_HH_MM_SS
        }
        val utcFormat = SimpleDateFormat(utcStringFormat, Locale.getDefault())
        utcFormat.timeZone = TimeZone.getTimeZone("UTC+7")
        return try {
            val date = utcFormat.parse(utcDate)
            return date?.let {
                val localDateFormat = SimpleDateFormat(dateFormat, Locale("ID", "id"))
                localDateFormat.format(it)
            } ?: run {
                FirebaseCrashlytics.getInstance().recordException(
                    ParseException("Parse null string", 0)
                )
                ""
            }
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
            ""
        }
    }

    fun getFormattedLocalDateTime(utcDate: String?, dateFormat: String?): String? {
        val utcStringFormat = if (utcDate?.contains("T").isTrue) {
            YYYY_MM_DD_T_HH_MM_SS
        } else {
            YYYY_MM_DD_HH_MM_SS
        }
        val utcFormatter = SimpleDateFormat(utcStringFormat)
//        utcFormatter.timeZone = TimeZone.getTimeZone(UTC)
        return try {
            val localDateFormat = SimpleDateFormat(dateFormat, Locale("ID", "id"))
//            utcFormatter.timeZone = TimeZone.getTimeZone("UTC+7")
            localDateFormat.format(utcFormatter.parse(utcDate))
        } catch (e: java.lang.Exception) {
            // return original data if formatting fails
            e.printStackTrace()
            utcDate
        }
    }

    fun getUTCTimeToLocalDateTime(utcDate: String?, dateFormat: String?): String? {
        val utcStringFormat = if (utcDate?.contains("T").isTrue) {
            YYYY_MM_DD_T_HH_MM_SS
        } else {
            YYYY_MM_DD_HH_MM_SS
        }
        val utcFormatter = SimpleDateFormat(utcStringFormat)
        utcFormatter.timeZone = TimeZone.getTimeZone(UTC)
        return try {
            val localDateFormat = SimpleDateFormat(dateFormat)
            localDateFormat.timeZone = TimeZone.getDefault()
            localDateFormat.format(utcFormatter.parse(utcDate))
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
            utcDate
        }
    }

    fun getFormattedDateTime(timestamp: Long, dateFormat: String?): String? {
        val date = Date(timestamp)
        val sdf = SimpleDateFormat(dateFormat, Locale("ID", "id"))
        sdf.timeZone = TimeZone.getDefault()
        return sdf.format(date)
    }

    fun getTimestampFromDateString(dateString: String, dateFormat: String): Long {
        val utcFormat = SimpleDateFormat(dateFormat, Locale.getDefault())
        utcFormat.timeZone = TimeZone.getTimeZone(UTC)
        return try {
            val date = utcFormat.parse(dateString)
            return date?.time ?: run { 0 }
        } catch (ex: Exception) {
            FirebaseCrashlytics.getInstance().recordException(ex)
            0
        }
    }

    fun isToday(dateString: String?, dateFormat: String): Boolean {
        if (dateString == null) return false
        val utcFormat = SimpleDateFormat(dateFormat, Locale.getDefault())
        utcFormat.timeZone = TimeZone.getTimeZone(UTC)
        return try {
            val date = utcFormat.parse(dateString)
            date?.let {
                val calendar = Calendar.getInstance()
                calendar.time = it
                DateUtils.isToday(calendar.timeInMillis)
            } ?: run { false }
        } catch (ex: Exception) {
            FirebaseCrashlytics.getInstance().recordException(ex)
            false
        }
    }

    fun getFormattedExpiryDate(expiryDate: String?): String? {
        return try {
            expiryDate?.let {
                if (it.length >= 4) {
                    val month = it.substring(2, 4) // Extract MM
                    val year = it.substring(0, 2) // Extract YY
                    return "$month/$year" // Return formatted expiry date
                }
                expiryDate
            }
        } catch (e: Exception) {
            Log.d("DateTimeUtils", "Error formatting expiry date: ${e.message}")
            expiryDate
        }
    }

    fun getCurrentDateYYMM(): String {
        val date = Date()
        val simpleDateFormat = SimpleDateFormat("yyyyMMddHHmmss")
        val formatYYMM = simpleDateFormat.format(date).substring(2, 6)
        return formatYYMM
    }

    fun getCurrentDateYYYYMMDD(): String {
        val date = Date()
        val simpleDateFormat = SimpleDateFormat("yyyy/MM/dd")
        return simpleDateFormat.format(date)
    }

    fun getMonthIntFromMonthName(monthName: String): Int {
        val cal = Calendar.getInstance()
        try {
            val date = SimpleDateFormat("MMMM yyyy", Locale("ID", "id")).parse(monthName)
            cal.time = date
        } catch (e: java.lang.Exception) {
        }
        return cal[Calendar.MONTH]
    }

    fun getMonthAndYearNameList(): ArrayList<String> {
        val formatter = SimpleDateFormat("MMMM yyyy", Locale("ID", "id"))
        val list = ArrayList<String>()
        val calendar = Calendar.getInstance()
        calendar.add(Calendar.MONTH, 0)
        for (i in 0..11) {
            list.add(formatter.format(calendar.timeInMillis))
            calendar.add(Calendar.MONTH, 1)
        }
        return list
    }

    fun getInvoiceExpiryTime(utcDate: String?): Long {
        if (utcDate.isNullOrEmpty()) return 0
        val diff: Long = getTimestampFromUtcDate(utcDate) - getCurrentUTCTime()
        return if (diff > 0) diff else 0
    }

    fun getTimestampFromUtcDate(utcDate: String?): Long {
        val utcFormatter = SimpleDateFormat(YYYY_MM_DD_T_HH_MM_SS)
        utcFormatter.timeZone = TimeZone.getTimeZone(UTC)
        try {
            val date = utcFormatter.parse(utcDate)
            return date.time
        } catch (e: ParseException) {
            e.printStackTrace()
        }
        return 0
    }

    fun getCurrentUTCTime(): Long {
        val utcFormatter = SimpleDateFormat(YYYY_MM_DD_T_HH_MM_SS)
        utcFormatter.timeZone = TimeZone.getTimeZone(UTC)
        try {
            val date = utcFormatter.parse(utcFormatter.format(Calendar.getInstance().time))
            return date.time
        } catch (e: ParseException) {
            e.printStackTrace()
        }
        return 0
    }

    fun millisecondsToTime(milliseconds: Long): String {
        val days = milliseconds / 1000 / 60 / 60 / 24
        val hours = milliseconds / 1000 / 60 / 60 % 24
        val minutes = milliseconds / 1000 / 60 % 60
        val seconds = milliseconds / 1000 % 60
        val secs: String = formatTime(java.lang.Long.toString(seconds))
        val mins: String = formatTime(java.lang.Long.toString(minutes))
        val hrs: String = formatTime(java.lang.Long.toString(hours))
        val day: String = formatTime(java.lang.Long.toString(days))
        return "$day:$hrs:$mins:$secs"
    }

    private fun formatTime(time: String): String = if (time.length >= 2) {
        time.substring(0, 2)
    } else {
        "0$time"
    }

    fun convertHoursToMillis(hrs: Int): Long = hrs * ONE_HOUR_IN_MILLIS

    fun convertMillisToHours(millis: Long): Long = millis / ONE_HOUR_IN_MILLIS

    fun convertMinsToMillis(mins: Long): Long = mins * ONE_MINUTE_IN_MILLIS
}
