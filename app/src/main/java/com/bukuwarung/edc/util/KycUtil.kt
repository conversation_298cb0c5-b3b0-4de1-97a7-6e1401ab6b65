package com.bukuwarung.edc.util

import android.app.Activity
import com.bukuwarung.edc.app.config.AppConfig
import com.bukuwarung.edc.homepage.ui.home.HomePageActivity
import com.bukuwarung.edc.payments.constant.PaymentRemoteConfig.getPaymentConfigs
import com.bukuwarung.edc.webview.WebviewActivity
import com.bukuwarung.edc.webview.constant.ClassConstants

object KycUtil {
    fun navigateToKycFlow(activity: Activity) {
        if (Utils.isCardReader()) {
            val redirectionUrl =
                if (AppConfig.current.variantConfig.isPartnershipForKycRedirection) {
                    getPaymentConfigs().miniAtmEdcOrderKyc
                } else {
                    getPaymentConfigs().kycKybVerificationUrl
                }
            // Device is a card reader, allow navigation to KYC webview
            activity.openActivity(WebviewActivity::class.java) {
                putString(
                    ClassConstants.WEBVIEW_URL,
                    redirectionUrl
                )
                putBoolean(ClassConstants.HIDE_TOOLBAR, true)
            }
        } else {
            activity.openActivity(HomePageActivity::class.java)
            activity.finishAffinity()
        }
    }
}
