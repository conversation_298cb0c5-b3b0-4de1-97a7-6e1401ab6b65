package com.bukuwarung.edc.util

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import com.bukuwarung.edc.global.EdcApplication

object PermissionUtil {

    fun hasLocationPermission(): <PERSON><PERSON>an {
        var isGranted = true
        if (Build.VERSION.SDK_INT < 23) {
            return true
        }
        val context: Context = EdcApplication.instance
        if (context.checkSelfPermission(Manifest.permission.ACCESS_FINE_LOCATION) !=
            PackageManager.PERMISSION_GRANTED &&
            context.checkSelfPermission(Manifest.permission.ACCESS_COARSE_LOCATION) !=
            PackageManager.PERMISSION_GRANTED
        ) {
            isGranted = false
        }
        return isGranted
    }

    fun hasBluetoothPermission(): Bo<PERSON>an {
        var isGranted = true
        val context: Context = EdcApplication.instance
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            if (context.checkSelfPermission(Manifest.permission.BLUETOOTH_SCAN) !=
                PackageManager.PERMISSION_GRANTED &&
                context.checkSelfPermission(Manifest.permission.BLUETOOTH_CONNECT) !=
                PackageManager.PERMISSION_GRANTED
            ) {
                isGranted = false
            }
        }
        return isGranted
    }

}
