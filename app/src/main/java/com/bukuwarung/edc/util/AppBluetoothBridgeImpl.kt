package com.bukuwarung.edc.util

import android.content.Context
import com.bukuwarung.edc.login.ui.LoginActivity
import com.bukuwarung.utils.AppBluetoothBridge

class AppBluetoothBridgeImpl : AppBluetoothBridge {
    override fun clearDataAndLogout() {
        Utils.clearDataAndLogout(false)
    }

    override fun goToLoginPage(context: Context) {
        context.openActivity(LoginActivity::class.java)
    }

    override fun getReferralCode(): String = Utils.referralCode
}
