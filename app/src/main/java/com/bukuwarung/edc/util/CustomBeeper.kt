package com.bukuwarung.edc.util

import android.content.Context
import android.media.AudioManager
import android.media.MediaPlayer
import com.bukuwarung.edc.R

class CustomBeeper(private val context: Context) {

    private var mediaPlayer: MediaPlayer? = null

    fun playCustomBeep() {
        // Release any previous MediaPlayer
        mediaPlayer?.release()

        // Initialize MediaPlayer with the custom beep sound
        mediaPlayer = MediaPlayer.create(context, R.raw.bukuagen_beep)

        mediaPlayer?.setAudioStreamType(AudioManager.STREAM_SYSTEM)

        // Set an onCompletionListener to release the MediaPlayer after the sound is played
        mediaPlayer?.setOnCompletionListener {
            it.release()
        }

        // Start playing the sound
        mediaPlayer?.start()
    }
}
