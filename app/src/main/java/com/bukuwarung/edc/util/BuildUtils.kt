package com.bukuwarung.edc.util

import com.bukuwarung.edc.BuildConfig

/**
 * Utility object for handling BuildConfig-related operations and configuration logic
 */
object BuildUtils {

    /**
     * Represents the different environment flavors available in the app
     */
    enum class Environment {
        DEV,
        STAGING,
        PROD;

        companion object {
            /**
             * Creates an Environment from the flavor string
             * @param flavor The environment flavor string from BuildConfig
             * @return The corresponding Environment enum value
             */
            fun fromFlavor(flavor: String): Environment = when (flavor.lowercase()) {
                "dev" -> DEV
                "stg" -> STAGING
                "prod" -> PROD
                else -> throw IllegalArgumentException("Unknown environment flavor: $flavor")
            }
        }
    }

    /**
     * Checks if the current application is the Bukuwarung EDC variant
     * @return true if application ID starts with "com.bukuwarung.edc", false otherwise
     */
    fun isVeri(): Boolean {
        return BuildConfig.APPLICATION_ID.startsWith("com.bukuwarung.edc")
    }

    /**
     * Gets the appropriate Zoho App Key based on the application variant
     * @return Zoho App Key string for the current application variant
     */
    fun getZohoAppKey(): String {
        return if (isVeri()) {
            BuildConfig.BUKUWARUNG_EDC_ZOHO_APP_KEY
        } else {
            BuildConfig.ZOHO_APP_KEY
        }
    }

    /**
     * Gets the appropriate Zoho Access Key based on the application variant
     * @return Zoho Access Key string for the current application variant
     */
    fun getZohoAccessKey(): String {
        return if (isVeri()) {
            BuildConfig.BUKUWARUNG_EDC_ZOHO_APP_KEY
        } else {
            BuildConfig.ZOHO_ACCESS_KEY
        }
    }

    /**
     * Gets the base API URL from BuildConfig
     * @return API base URL string
     */
    fun getApiBaseUrl(): String {
        return BuildConfig.API_BASE_URL
    }

    /**
     * Checks if the app is running in debug mode
     * @return true if debug build, false otherwise
     */
    fun isDebugBuild(): Boolean {
        return BuildConfig.DEBUG
    }

    /**
     * Gets the current environment based on the build configuration
     * @return The current Environment enum value
     */
    fun getCurrentEnvironment(): Environment = Environment.fromFlavor(BuildConfig.FLAVOR_env)

    /**
     * Checks if the app is running in staging environment
     * @return true if environment is staging, false otherwise
     */
    fun isStaging(): Boolean = getCurrentEnvironment() == Environment.STAGING

    /**
     * Checks if the app is running in development environment
     * @return true if environment is development, false otherwise
     */
    fun isDevelopment(): Boolean = getCurrentEnvironment() == Environment.DEV

    /**
     * Checks if the app is running in production environment
     * @return true if environment is production, false otherwise
     */
    fun isProduction(): Boolean = getCurrentEnvironment() == Environment.PROD
}
