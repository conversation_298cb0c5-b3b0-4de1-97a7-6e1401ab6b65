package com.bukuwarung.edc.util

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.Typeface
import android.net.Uri
import android.widget.ImageView
import androidx.core.content.FileProvider
import com.bukuwarung.edc.BuildConfig
import com.bukuwarung.edc.app.config.AppConfig
import com.bumptech.glide.Glide
import java.io.File
import java.io.FileOutputStream

object ImageUtils {

    fun <T> loadImageCircleCropped(
        context: Context?,
        imageView: ImageView?,
        imageRes: T,
        placeholderRes: Int,
        errorRes: Int
    ) {
        Glide.with(context!!)
            .load(imageRes)
            .circleCrop()
            .placeholder(placeholderRes)
            .error(errorRes)
            .into(imageView!!)
    }

    fun addWatermark(canvas: Canvas, width: Int, height: Int) {
        val paint = Paint().apply {
            color = Color.LTGRAY
            alpha = 30 // Adjust alpha for transparency
            textSize = 90f
            isAntiAlias = true
            isUnderlineText = false
            typeface = Typeface.create(Typeface.DEFAULT, Typeface.BOLD)
        }

        val text = AppConfig.current.variantConfig.receiptHeaderText
        val textWidth = paint.measureText(text)

        val rect = Rect()
        paint.getTextBounds(text, 0, text.length, rect)
        val textHeight = rect.height().toFloat()
        val step = textHeight * 3 + 10f // Adding some padding

        canvas.save()
        canvas.rotate(-25f, (width / 2).toFloat(), (height / 2).toFloat())

        var x = -width.toFloat()
        while (x < width + textWidth) {
            var y = -height.toFloat()
            while (y < height) {
                canvas.drawText(text, x, y, paint)
                y += step
            }
            x += textWidth + 100f // Adding some padding horizontally
        }

        canvas.restore()
    }

    fun shareImage(context: Context, bitmap: Bitmap, businessName: String) {
        try {
            val file = File(context.externalCacheDir, "screenshot.png")
            val fOut = FileOutputStream(file)
            bitmap.compress(Bitmap.CompressFormat.PNG, 100, fOut)
            fOut.flush()
            fOut.close()
            file.setReadable(true, false)
            val uri: Uri =
                FileProvider.getUriForFile(context, "${context.packageName}.provider", file)

            val additionalText = if (BuildConfig.FLAVOR_partner == "atmpro") {
                """
                    Halo, berikut ini adalah tanda bukti transaksi Anda.

                    Terima kasih sudah bertransaksi dengan $businessName.
                    ---
                    Pesan ini dibuat dengan MiniATM Pro.
                """.trimIndent()
            } else {
                """
                    Halo, berikut ini adalah tanda bukti transaksi Anda.
                    Terima kasih sudah bertransaksi dengan $businessName.
                    ---
                    Pesan ini dibuat dengan BukuAgen bagian dari BukuWarung.
                """.trimIndent()
            }

            val whatsappIntent = Intent(Intent.ACTION_SEND).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
                putExtra(Intent.EXTRA_STREAM, uri)
                putExtra(Intent.EXTRA_TEXT, additionalText)
                type = "image/png"
                setPackage("com.whatsapp")
            }

            val chooserIntent = Intent.createChooser(whatsappIntent, "Share image via")
            val resInfoList = context.packageManager.queryIntentActivities(
                whatsappIntent,
                PackageManager.MATCH_DEFAULT_ONLY
            )

            if (resInfoList.isNotEmpty()) {
                context.startActivity(chooserIntent)
            } else {
                // WhatsApp not installed, show generic chooser
                val genericIntent = Intent(Intent.ACTION_SEND).apply {
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                    putExtra(Intent.EXTRA_STREAM, uri)
                    putExtra(Intent.EXTRA_TEXT, additionalText)
                    type = "image/png"
                }
                context.startActivity(Intent.createChooser(genericIntent, "Share image via"))
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun isAppInstalled(context: Context, packageName: String): Boolean = try {
        context.packageManager.getPackageInfo(packageName, PackageManager.GET_ACTIVITIES)
        true
    } catch (e: PackageManager.NameNotFoundException) {
        true
    }
}
