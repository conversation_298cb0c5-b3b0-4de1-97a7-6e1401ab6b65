package com.bukuwarung.edc.util

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import android.telephony.TelephonyManager
import com.bukuwarung.analytic.BtAnalytics.bwLog
import com.bukuwarung.edc.global.EdcApplication
import com.google.firebase.crashlytics.BuildConfig
import com.google.firebase.crashlytics.FirebaseCrashlytics
import java.net.HttpURLConnection
import java.net.InetAddress
import java.net.URL
import java.util.Locale
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

object NetworkUtils {

    fun checkInternetHealthAndLog(throwable: Throwable) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val isConnected = isNetworkConnected(EdcApplication.instance)
                val networkInfo = getNetworkInfoDescription(EdcApplication.instance)

                var isHealthy = false
                var downloadSpeedKbps: Double? = null
                var dnsResolvedIp: String? = null
                var dnsResolutionFailed = false

                if (isConnected) {
                    try {
                        val inetAddress = InetAddress.getByName("bukuwarung.com")
                        dnsResolvedIp = inetAddress.hostAddress
                        bwLog("DNS Resolved: bukuwarung.com -> $dnsResolvedIp")
                    } catch (e: Exception) {
                        dnsResolutionFailed = true
                        bwLog("DNS resolution failed: ${e.message}")
                        FirebaseCrashlytics.getInstance().apply {
                            setCustomKey("dns_resolution", "failed")
                            recordException(e)
                        }
                    }

                    try {
                        val url = URL("https://bukuwarung.com/")
                        val connection = url.openConnection() as HttpURLConnection
                        connection.connectTimeout = 2000
                        connection.readTimeout = 2000
                        connection.requestMethod = "HEAD"
                        connection.connect()
                        isHealthy = connection.responseCode in 200..399
                    } catch (e: Exception) {
                        bwLog(e)
                    }

                    try {
                        val startTime = System.currentTimeMillis()
                        val testUrl =
                            URL(
                                "https://www.google.com/images/branding/googlelogo/2x/googlelogo_light_color_92x30dp.png"
                            )
                        val connection = testUrl.openConnection() as HttpURLConnection
                        connection.connectTimeout = 3000
                        connection.readTimeout = 3000
                        connection.connect()

                        val bytes = connection.inputStream.readBytes()
                        val totalBytes = bytes.size
                        val endTime = System.currentTimeMillis()

                        val durationSec = (endTime - startTime) / 1000.0
                        downloadSpeedKbps = (totalBytes * 8) / 1000.0 / durationSec
                    } catch (e: Exception) {
                        bwLog("Download speed test failed")
                        bwLog(e)
                    }
                }

                val deviceInfo = mapOf(
                    "deviceModel" to Build.MODEL,
                    "osVersion" to Build.VERSION.RELEASE,
                    "appVersion" to BuildConfig.VERSION_NAME,
                    "isConnectedToInternet" to isConnected.toString(),
                    "networkType" to networkInfo,
                    "locale" to Locale.getDefault().toString(),
                    "exception" to throwable::class.java.simpleName,
                    "exceptionMessage" to throwable.message.orEmpty(),
                    "stackTrace" to throwable.stackTraceToString(),
                    "timestamp" to System.currentTimeMillis().toString(),
                    "dnsResolvedIp" to "$dnsResolvedIp",
                    "dnsResolutionFailed" to "$dnsResolutionFailed"
                )

                val logMessage = buildString {
                    append("Internet health check: connected=$isConnected, healthy=$isHealthy, ")
                    append("networkInfo=$networkInfo")
                    if (downloadSpeedKbps != null) {
                        append(", downloadSpeed=${String.format("%.2f", downloadSpeedKbps)} Kbps")
                    }
                    append(deviceInfo.toString())
                }
                bwLog(logMessage)
                FirebaseCrashlytics.getInstance().apply {
                    setCustomKey("error_type", "DNS_UnknownHostException")
                    deviceInfo.forEach { (key, value) ->
                        setCustomKey(key, value)
                    }
                    recordException(throwable)
                }
            } catch (e: Exception) {
                bwLog(e)
            }
        }
    }

    private fun isNetworkConnected(context: Context): Boolean {
        return try {
            val connectivityManager =
                context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val network = connectivityManager.activeNetwork ?: return false
                val capabilities =
                    connectivityManager.getNetworkCapabilities(network) ?: return false
                capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            } else {
                val networkInfo = connectivityManager.activeNetworkInfo
                networkInfo != null && networkInfo.isConnected
            }
        } catch (e: Exception) {
            bwLog(e)
            false
        }
    }

    private fun getNetworkInfoDescription(context: Context): String = try {
        val connectivityManager =
            context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val telephonyManager =
            context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager

        val type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            when {
                capabilities == null -> "No network"
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> "Wi-Fi"
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> "Mobile"
                else -> "Other"
            }
        } else {
            val info = connectivityManager.activeNetworkInfo
            info?.typeName ?: "Unknown"
        }

        val carrierName = telephonyManager.networkOperatorName
        val isRoaming = telephonyManager.isNetworkRoaming

        "Type=$type, Carrier=$carrierName, Roaming=$isRoaming"
    } catch (e: Exception) {
        val msgException = "Failed to retrieve network info"
        bwLog(msgException)
        bwLog(e)
        msgException
    }
}
