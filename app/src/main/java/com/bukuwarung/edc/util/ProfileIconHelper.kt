package com.bukuwarung.edc.util

import android.content.Context
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.bukuwarung.edc.R
import com.bukuwarung.edc.util.Utils.setBackground
import com.google.firebase.crashlytics.FirebaseCrashlytics
import java.util.Locale

object ProfileIconHelper {
    private fun loadProfileImage(
        context: Context?,
        imageView: ImageView,
        nameInitialsTv: TextView,
        imageUrl: String?
    ) {
        if (imageUrl != null) {
            imageView.visibility = View.VISIBLE
            nameInitialsTv.visibility = View.GONE
            ImageUtils.loadImageCircleCropped(
                context,
                imageView,
                imageUrl,
                R.color.white,
                R.mipmap.default_icon
            )
        }
    }

    fun setProfilePic(
        context: Context,
        imageView: ImageView,
        nameInitialsTv: TextView,
        nameInitialsStr: String?,
        imageUrl: String?
    ) {
        try {
            var hasInitials = true
            ComponentUtil.setVisible(imageView, imageUrl != null)
            ComponentUtil.setVisible(nameInitialsTv, imageUrl == null)
            if (imageUrl != null) {
                loadProfileImage(context, imageView, nameInitialsTv, imageUrl)
                return
            }
            if (nameInitialsStr != null) {
                val upperCase = nameInitialsStr.uppercase(Locale.getDefault())
                if (upperCase.length <= 0) {
                    hasInitials = false
                }
                if (hasInitials) {
                    nameInitialsTv.visibility = View.VISIBLE
                    nameInitialsTv.text = upperCase[0].toString()
                }
                setBackground(
                    context,
                    nameInitialsTv,
                    context.resources.getIdentifier("oval_0", "drawable", context.packageName)
                )
            }
        } catch (e: Exception) {
            FirebaseCrashlytics.getInstance().recordException(e)
        }
    }
}
