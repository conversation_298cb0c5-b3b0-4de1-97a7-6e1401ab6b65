package com.bukuwarung.edc.util

import android.nfc.NfcAdapter

enum class NfcStatus {
    NOT_AVAILABLE,
    DISABLED,
    ENABLED
}

object NfcUtils {
    fun getNfcStatus(nfcAdapter: NfcAdapter?): NfcStatus = when {
        nfcAdapter == null -> NfcStatus.NOT_AVAILABLE
        nfcAdapter.isEnabled -> NfcStatus.ENABLED
        else -> NfcStatus.DISABLED
    }

    fun isDeviceHasNfcIssues(brand: String, nfcAdapter: NfcAdapter): Boolean =
        when (brand.lowercase()) {
            "morefun" -> getNfcStatus(nfcAdapter) == NfcStatus.ENABLED
            else -> false
        }
}
