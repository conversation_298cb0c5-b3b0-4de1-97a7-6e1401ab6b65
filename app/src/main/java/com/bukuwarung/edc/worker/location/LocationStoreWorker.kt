package com.bukuwarung.edc.worker.location

import android.annotation.SuppressLint
import android.content.ContentValues
import android.content.Context
import android.os.Build
import android.os.Looper
import android.util.Log
import androidx.hilt.work.HiltWorker
import androidx.work.CoroutineWorker
import androidx.work.Data
import androidx.work.WorkerParameters
import com.bukuwarung.edc.card.domain.model.consts.Constants
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.global.EdcApplication
import com.bukuwarung.edc.util.PermissionUtil
import com.pax.dal.entity.ETermInfoKey
import com.pax.market.android.app.sdk.BaseApiService
import com.pax.market.android.app.sdk.CommonConstants
import com.pax.market.android.app.sdk.SyncApiStrategy
import com.pax.market.android.app.sdk.dto.DcUrlInfo
import com.pax.market.android.app.sdk.util.PreferencesUtils
import com.pax.market.api.sdk.java.base.exception.NotInitException
import dagger.assisted.Assisted
import dagger.assisted.AssistedInject
import java.util.concurrent.CountDownLatch
import java.util.concurrent.Semaphore
import java.util.concurrent.TimeUnit

@HiltWorker
class LocationStoreWorker @AssistedInject constructor(
    @Assisted private val appContext: Context,
    @Assisted workerParams: WorkerParameters,
    private val locationUtil: LocationUtil
) : CoroutineWorker(appContext, workerParams) {

    var semaphore: Semaphore? = null
    var syncApi: SyncApiStrategy? = null
    private var longitude: Float? = null
    private var latitude: Float? = null

    @SuppressLint("MissingPermission")
    override suspend fun doWork(): Result {
        if (PermissionUtil.hasLocationPermission()) {
            if (Build.MANUFACTURER != Constants.DEVICE_MANUFACTURER_PAX &&
                Build.MANUFACTURER != Constants.DEVICE_MANUFACTURER_VERIFONE
            ) {
                val location = locationUtil.getCurrentLocation(appContext)
                longitude = location?.longitude?.toFloat()
                latitude = location?.latitude?.toFloat()
            } else {
                syncApi = SyncApiStrategy(
                    applicationContext,
                    null,
                    Constant.PAX_STORE_CLIENT_ID,
                    Constant.PAX_STORE_CLIENT_SECRET,
                    EdcApplication.dal?.sys?.termInfo?.getValue(ETermInfoKey.SN)
                ).setProxyDelegate(BaseApiService.getInstance(applicationContext))
                semaphore = Semaphore(2)

                val locationObject = syncApi().locationInfo
                longitude = locationObject.longitude
                latitude = locationObject.latitude
            }

            if (longitude != null && latitude != null) {
                val locationStoreStatus: Pair<Boolean, Data>? =
                    locationUtil.storeLocation(longitude!!, latitude!!)
                return if (locationStoreStatus != null) {
                    if (locationStoreStatus.first) {
                        val outputData = Data.Builder()
                            .putAll(locationStoreStatus.second)
                            .putString("streetName", locationStoreStatus.second.getString("street"))
                            .build()
                        Result.success(outputData)
                    } else {
                        Result.failure()
                    }
                } else {
                    Result.failure()
                }
            } else {
                val data = Data.Builder().putString("reason", "failed to get location").build()
                return Result.failure(data)
            }
        } else {
            val data = Data.Builder().putBoolean("GRANTED", false).build()
            return Result.failure(data)
        }
    }

    @Throws(NotInitException::class)
    fun syncApi(): SyncApiStrategy {
        if (syncApi == null) {
            acquireSemaphore()
            if (syncApi == null) {
                throw NotInitException("Not initialized")
            }
        }
        syncApi?.baseUrl = getDcUrl(applicationContext, syncApi?.baseUrl ?: "", false)
        syncApi?.setProxyDelegate<SyncApiStrategy>(BaseApiService.getInstance(applicationContext))
        return syncApi ?: throw NotInitException("Not initialized")
    }

    private fun acquireSemaphore() {
        try {
            semaphore?.tryAcquire(2, 5, TimeUnit.SECONDS)
        } catch (e: InterruptedException) {
            // Do something with the InterruptedException if needed
        }
        if (semaphore?.availablePermits() == 0) {
            semaphore?.release(2)
        }
    }

    @Throws(NotInitException::class)
    fun getDcUrl(context: Context, oriBaseUrl: String, tid: Boolean): String? {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            throw NotInitException("Can not do this on MainThread!!")
        }

        val dcUrl = StringBuilder()
        val countDownLatch = CountDownLatch(1)

        BaseApiService.getInstance(context).getDcUrl(
            object : BaseApiService.DcCallBack {
                override fun initSuccess(baseUrl: String) {
                    saveLastUrl(baseUrl, context)
                    dcUrl.append(baseUrl)
                    countDownLatch.countDown()
                }

                override fun initFailed(e: Exception) {
                    Log.e("StoreSdk", "e:$e")
                    countDownLatch.countDown()
                }
            },
            oriBaseUrl
        )

        try {
            countDownLatch.await(30, TimeUnit.SECONDS)
        } catch (e: InterruptedException) {
            Log.e(ContentValues.TAG, "e:$e")
        }

        if (dcUrl.toString().isEmpty() || dcUrl.toString().equals("null", ignoreCase = true)) {
            if (tid) {
                return null
            } else {
                throw NotInitException(
                    "Get baseUrl failed, client is not installed or terminal is not activated."
                )
            }
        }
        return dcUrl.toString()
    }

    private fun saveLastUrl(baseUrl: String, context: Context) {
        val localDcUrlInfo = PreferencesUtils.getObject(
            context,
            CommonConstants.SP_LAST_GET_DCURL_TIME,
            DcUrlInfo::class.java
        )
        // update last getDcUrl time if there has been more than one hour.
        if (localDcUrlInfo == null ||
            (
                System.currentTimeMillis() - localDcUrlInfo.lastAccessTime >
                    CommonConstants.ONE_HOUR_INTERVAL
                )
        ) {
            val dcUrlInfo = DcUrlInfo()
            dcUrlInfo.dcUrl = baseUrl
            dcUrlInfo.lastAccessTime = System.currentTimeMillis()
            PreferencesUtils.putObject(context, CommonConstants.SP_LAST_GET_DCURL_TIME, dcUrlInfo)
        }
    }
}
