package com.bukuwarung.edc.worker.location.constant

import com.bukuwarung.edc.util.RemoteConfigUtils
import com.bukuwarung.edc.worker.location.DateHourMinute
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

object LocationConfigConstants {

    const val LOCATION_SEND_TIME = "location_send_time"
    const val EDC_WELCOME_SCREENS = "edc_welcome_screen"

    const val WELCOME_SCREENS = """
        [
  {
    "screenName": "customer_testimonial",
    "backGroundImage": "",
    "titleText": "Semua Bisa Jadi Agen Pembayaran",
    "subTitleText": "<PERSON>kses Mudah &amp; <PERSON><PERSON><PERSON>"
  },
  {
    "screenName": "customer_payment",
    "backGroundImage": "",
    "titleText": "Raih Keuntungan Tanpa Beban",
    "subTitleText": "Bebas Bertransaksi Tanpa Target Bulanan"
  },
  {
    "screenName": "customer_debt",
    "backGroundImage": "",
    "titleText": "Penghasilan Naik 2x Lipat",
    "subTitleText": "Dengan jualan beragam produk digital seperti pulsa, token listrik, dll"
  }
]
    """

    const val SEND_TIME = """
        {
            "hour": 8,
            "minute": 15
        }
    """

    fun fetchLocationSendTime(): DateHourMinute = try {
        val type = object : TypeToken<DateHourMinute>() {}.type
        val json = RemoteConfigUtils.remoteConfig.getString(LOCATION_SEND_TIME)
        Gson().fromJson(json, type)
    } catch (ex: Exception) {
        DateHourMinute()
    }
}
