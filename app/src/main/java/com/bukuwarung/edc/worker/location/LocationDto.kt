package com.bukuwarung.edc.worker.location

import androidx.work.Data
import com.bukuwarung.edc.util.Utils

data class LocationDto(
    val deviceId: String,
    val latitude: Double,
    val longitude: Double,
    val province: String? = "",
    val city: String? = "",
    val district: String? = "",
    val subDistrict: String? = "",
    val streetName: String? = "",
    val streetNumber: String? = "",
    val postalCode: String? = "",
    val addressLine: String? = ""
)

fun LocationDto.toWorkerData(): Data.Builder = Data.Builder()
    .putString(LocationUtil.PROVINCE, province)
    .putString(LocationUtil.CITY, city)
    .putString(LocationUtil.DISTRICT, district)
    .putString(LocationUtil.SUB_DISTRICT, subDistrict)
    .putString(LocationUtil.STREET_NAME, streetName)
    .putString(LocationUtil.STREET_NUMBER, streetNumber)
    .putString(LocationUtil.ADDRESS_LINE, addressLine)
    .putString(LocationUtil.POSTAL_CODE, postalCode)

fun Data.toLocationDto(): LocationDto? = try {
    LocationDto(
        deviceId = Utils.generatedAppId(),
        latitude = 0.0,
        longitude = 0.0,
        province = getString(LocationUtil.PROVINCE),
        city = getString(LocationUtil.CITY),
        district = getString(LocationUtil.DISTRICT),
        subDistrict = getString(LocationUtil.SUB_DISTRICT),
        streetName = getString(LocationUtil.STREET_NAME),
        streetNumber = getString(LocationUtil.STREET_NUMBER),
        postalCode = getString(LocationUtil.POSTAL_CODE),
        addressLine = getString(LocationUtil.ADDRESS_LINE)
    )
} catch (ex: Exception) {
    null
}
