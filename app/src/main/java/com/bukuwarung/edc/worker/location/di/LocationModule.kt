package com.bukuwarung.edc.worker.location.di

import com.bukuwarung.edc.worker.location.repository.LocationRemoteService
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton
import retrofit2.Retrofit

@Module
@InstallIn(SingletonComponent::class)
class LocationModule {

    @Singleton
    @Provides
    fun provideLocationRemoteService(
        @Named("location") retrofitForLocation: Retrofit
    ): LocationRemoteService = retrofitForLocation.create(
        LocationRemoteService::class.java
    )
}
