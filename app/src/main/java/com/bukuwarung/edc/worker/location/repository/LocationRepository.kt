package com.bukuwarung.edc.worker.location.repository

import android.content.Context
import com.bukuwarung.edc.BuildConfig
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.Utils.sharedPreferences
import com.bukuwarung.edc.util.put
import com.bukuwarung.edc.worker.location.LocationDto
import java.util.*
import javax.inject.Inject

class LocationRepository @Inject constructor(
    context: Context,
    private val locationService: LocationRemoteService
) {

    suspend fun storeLocation(userId: String?, locationDto: LocationDto): Boolean {
        val versionName = BuildConfig.VERSION_NAME
        val versionCode = BuildConfig.VERSION_CODE
        val response = locationService.storeLocation(userId, versionName, versionCode, locationDto)
        if (response.isSuccessful) {
            setLocationSaved()
            return true
        } else {
            return false
        }
    }

    private fun setLocationSaved() {
        sharedPreferences.put("saved_location", Utils.getStorableDateString(Date()))
    }
}
