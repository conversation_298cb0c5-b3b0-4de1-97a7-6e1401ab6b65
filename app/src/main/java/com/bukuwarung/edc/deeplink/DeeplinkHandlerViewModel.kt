package com.bukuwarung.edc.deeplink

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.analytic.BtAnalytics.bwLog
import com.bukuwarung.edc.app.config.VariantConfig
import com.bukuwarung.edc.card.domain.model.consts.Constants
import com.bukuwarung.edc.card.ui.edcdevices.model.DeviceItem
import com.bukuwarung.edc.card.ui.edcdevices.usecase.DeviceListUseCase
import com.bukuwarung.edc.order.orderdetail.model.EdcOrderDetailResponse
import com.bukuwarung.edc.order.orderdetail.usecase.EdcOrderDetailUseCase
import com.bukuwarung.edc.order.orderhistory.enums.EdcOrderStatus
import com.bukuwarung.network.utils.ResourceState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class DeeplinkHandlerViewModel @Inject constructor(
    private val deviceListUseCase: DeviceListUseCase,
    private val edcOrderUseCase: EdcOrderDetailUseCase,
    private val variantConfig: VariantConfig
) : ViewModel() {

    private val _haveSakuDevice = MutableStateFlow<Boolean?>(null)
    val haveSakuDevice: StateFlow<Boolean?> = _haveSakuDevice.asStateFlow()

    private val _isWaitingForOps = MutableStateFlow<Boolean?>(null)
    val isWaitingForOps: StateFlow<Boolean?> = _isWaitingForOps.asStateFlow()

    fun fetchDeviceStatus() {
        viewModelScope.launch {
            try {
                val result = deviceListUseCase.getDeviceList("ALL")
                when (result) {
                    is ResourceState.Success -> {
                        val devices = result.data.data ?: emptyList()
                        updateHaveSakuDevice(devices)
                    }

                    is ResourceState.Failure -> {
                        // Handle error - default to false for safety
                        _haveSakuDevice.value = false
                        bwLog("Error-fetchDeviceStatus(): ${result.message}")
                    }

                    is ResourceState.Loading -> {
                        // Do nothing, wait for final result
                    }
                }
            } catch (e: Exception) {
                _haveSakuDevice.value = false
                bwLog("Exception in fetchDeviceStatus: ${e.message}")
            }
        }
    }

    fun fetchOrderStatus() {
        viewModelScope.launch {
            try {
                if (variantConfig.shouldCheckOrderStatusOnDeeplink) {
                    val orderDetails = getEdcOrderDetailByPhoneNumber()
                    orderDetails?.let { orderResult ->
                        val isWaiting = orderResult.data?.status?.equals(
                            EdcOrderStatus.WAITING_FOR_OPS.name,
                            true
                        ) ?: false
                        _isWaitingForOps.value = isWaiting
                    } ?: run {
                        _isWaitingForOps.value = false
                    }
                } else {
                    _isWaitingForOps.value = false
                }
            } catch (e: Exception) {
                _isWaitingForOps.value = false
                bwLog("Exception in fetchOrderStatus: ${e.message}")
            }
        }
    }

    private suspend fun getEdcOrderDetailByPhoneNumber(): EdcOrderDetailResponse? = try {
        when (val response = edcOrderUseCase.getEdcOrderDetailByPhoneNumber()) {
            is ResourceState.Success -> {
                response.data
            }

            else -> {
                null
            }
        }
    } catch (e: Exception) {
        bwLog(e)
        null
    }

    fun fetchAllData() {
        fetchDeviceStatus()
        fetchOrderStatus()
    }

    private fun updateHaveSakuDevice(devices: List<DeviceItem>) {
        val currentValue = _haveSakuDevice.value
        if (currentValue != true) {
            val hasSaku = devices.any {
                it.vendor.equals(Constants.DEVICE_MANUFACTURER_VERIFONE, true).not() &&
                    it.vendor.equals(Constants.DEVICE_MANUFACTURER_PAX, true).not()
            }
            _haveSakuDevice.value = hasSaku
        }
    }

    // Methods to get current cached values synchronously
    fun getCurrentHaveSakuDevice(): Boolean? = _haveSakuDevice.value
    fun getCurrentIsWaitingForOps(): Boolean? = _isWaitingForOps.value
}
