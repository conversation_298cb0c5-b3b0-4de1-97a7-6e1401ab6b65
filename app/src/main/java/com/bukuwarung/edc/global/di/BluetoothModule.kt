package com.bukuwarung.edc.global.di

import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothManager
import android.content.Context
import com.bukuwarung.bluetooth_printer.BwBluetoothManager
import com.bukuwarung.bluetooth_printer.activities.print.adapter.PrinterPrefManager
import com.bukuwarung.edc.util.AppBluetoothBridgeImpl
import com.bukuwarung.utils.AppBluetoothBridge
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class BluetoothModule {
    @Singleton
    @Provides
    fun providePrinterPrefManager(@ApplicationContext context: Context): PrinterPrefManager =
        PrinterPrefManager(context)

    @Singleton
    @Provides
    fun provideBluetoothAdapter(@ApplicationContext context: Context): BluetoothAdapter? =
        (context.getSystemService(Context.BLUETOOTH_SERVICE) as? BluetoothManager)?.adapter

    @Singleton
    @Provides
    fun provideBwBluetoothManager(
        pref: PrinterPrefManager,
        btAdapter: BluetoothAdapter?
    ): BwBluetoothManager = BwBluetoothManager(pref, btAdapter)

    @Provides
    fun providesAppBluetoothBridge(): AppBluetoothBridge = AppBluetoothBridgeImpl()
}
