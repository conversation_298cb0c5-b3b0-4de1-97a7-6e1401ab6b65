package com.bukuwarung.edc.global

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.bukuwarung.analytic.BtAnalyticConstant
import com.bukuwarung.analytic.BtAnalytics.bwLog
import com.bukuwarung.edc.BuildConfig
import com.bukuwarung.edc.global.Constant.EDC_SAKU
import com.bukuwarung.edc.global.Constant.MOBILE_PHONE
import com.bukuwarung.edc.global.Constant.USER_PROP_DEVICE
import com.bukuwarung.edc.util.EncryptedPreferencesHelper
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.Utils.getEDCMiniATMProClientName
import com.bukuwarung.edc.util.get
import com.bukuwarung.edc.util.isNotNullOrBlank
import com.bukuwarung.edc.util.isNotNullOrEmpty
import com.bukuwarung.edc.util.put
import com.google.gson.Gson
import com.mixpanel.android.mpmetrics.MixpanelAPI
import com.tiktok.TikTokBusinessSdk
import com.tiktok.appevents.base.EventName
import com.tiktok.appevents.base.TTBaseEvent
import com.tiktok.appevents.contents.TTAddToCartEvent
import com.tiktok.appevents.contents.TTCheckoutEvent
import com.tiktok.appevents.contents.TTPurchaseEvent
import com.tiktok.appevents.contents.TTViewContentEvent
import org.json.JSONObject
import com.bukuwarung.edc.app.config.AppConfig

object Analytics {

    private lateinit var mp: MixpanelAPI

    private const val PREFERENCES_NAME = "app_analytics"
    private const val KEY_HAS_SENT_INSTALL = "has_sent_install"

    fun initialize(context: Context) {
        mp = MixpanelAPI.getInstance(context, Constant.MIXPANEL_TOKEN, false)
        val uuid = EncryptedPreferencesHelper.get("uuid", "")
        if (uuid.isNotNullOrEmpty()) {
            mp.identify(uuid)
            var propJson = JSONObject()
            propJson.put(Constant.USER_PROP_BW_USER_ID, uuid)
            propJson.put(Constant.USER_PROP_DEVICE, Constant.DEVICE_ID)
            val edcBrand = getDeviceName()
            propJson.put(Constant.USER_PROP_EDC_BRAND, edcBrand)
            mp.registerSuperProperties(propJson)
        }
        initTiktok(context)
        trackInstallOrUpdate()
    }

    fun getDeviceId(): String = mp.distinctId

    fun trackEvent(eventName: String, eventProps: HashMap<String, *>? = null) {
        // Convert eventProps to a mutable map or create a new HashMap if null
        val updatedEventProps = eventProps?.toMutableMap() ?: HashMap<String, Any>()

        if (Utils.getHardwareSerialNumber().isNotNullOrBlank()) {
            updatedEventProps[Constant.USER_PROP_EDC_BRAND] = Utils.getDeviceTypeBySerialNumber()
            updatedEventProps["serial_number"] = Utils.getHardwareSerialNumber()
        }
        if (Utils.getTerminalId().isNotNullOrBlank()) {
            updatedEventProps["terminal_id"] = Utils.getTerminalId()
        }

        var jsonObject = JSONObject()

        if (updatedEventProps.isNotEmpty()) {
            jsonObject = JSONObject(Gson().toJson(updatedEventProps))
        }

        if (jsonObject.length() > 0) {
            mp.track(eventName, jsonObject)
        } else {
            mp.track(eventName)
        }

        trackTikTokCustomEvents(eventName, updatedEventProps)
    }

    fun trackTikTokCustomEvents(eventName: String, updatedEventProps: MutableMap<String, Any>) {
        try {
            val tiktokEvent = TTBaseEvent.newBuilder(eventName)
            tiktokEvent.addProperty("description", updatedEventProps.toString())
            TikTokBusinessSdk.trackTTEvent(tiktokEvent.build())
        } catch (e: Exception) {
            bwLog(e)
        }
    }

    fun trackTikTokStandardEvents(eventName: String?) {
        lateinit var event: TTBaseEvent
        when (eventName) {
            TikTokStandardEvents.VIEW_CONTENT.value -> {
                event = TTViewContentEvent.newBuilder().build()
                TikTokBusinessSdk.trackTTEvent(event)
            }

            TikTokStandardEvents.ADD_TO_CART.value -> {
                event = TTAddToCartEvent.newBuilder().build()
                TikTokBusinessSdk.trackTTEvent(event)
            }

            TikTokStandardEvents.ADD_PAYMENT_INFO.value -> {
                TikTokBusinessSdk.trackTTEvent(EventName.ADD_PAYMENT_INFO)
            }

            TikTokStandardEvents.CHECKOUT.value -> {
                event = TTCheckoutEvent.newBuilder().build()
                TikTokBusinessSdk.trackTTEvent(event)
            }

            TikTokStandardEvents.PURCHASE.value -> {
                event = TTPurchaseEvent.newBuilder().build()
                TikTokBusinessSdk.trackTTEvent(event)
            }
        }
    }

    fun changeDeviceName(name: String) {
        mp.alias(Constant.USER_PROP_EDC_BRAND, name)
        Utils.sharedPreferences.put("DEVICE_SERVICE", name)
    }

    fun trackEventMobile(eventName: String, eventProps: HashMap<String, String>? = HashMap()) {
        eventProps?.set(USER_PROP_DEVICE, MOBILE_PHONE)
        trackEvent(eventName, eventProps)
    }

    fun trackUserProperty(propName: String, propValue: String) {
        var jsonObject = JSONObject()
        jsonObject.put(propName, propValue)
        mp.people.set(jsonObject)
    }

    fun trackSuperProperty(propName: String, propValue: String) {
        var jsonObject = JSONObject()
        jsonObject.put(propName, propValue)
        mp.registerSuperProperties(jsonObject)
        mp.getPeople().set(jsonObject)
    }

    fun checkAppUpdate() {
        val version = Utils.sharedPreferences.get("app_version", -1)
        if (version > 0 && version > BuildConfig.VERSION_CODE) {
            Utils.sharedPreferences.put("app_version", BuildConfig.VERSION_CODE)
        }
        // consistent event to track app install or update
        // previous app_update event was not following naming convention as bukuwarung-app
        trackInstallOrUpdate()
    }

    private fun getDeviceName(): String {
        val storedDeviceName = Utils.sharedPreferences.getString("DEVICE_SERVICE", EDC_SAKU)
        return when (storedDeviceName) {
            Constant.DEVICE_SERVICE_VERIFONE -> Constant.VERIFONE
            Constant.DEVICE_SERVICE_PAX -> Constant.PAX
            Constant.DEVICE_SERVICE_MF_ANDROID -> Constant.MOREFUN_ANDROID
            BtAnalyticConstant.DEVICE_SERVICE_TIANYU -> BtAnalyticConstant.TIANYU
            BtAnalyticConstant.DEVICE_SERVICE_MOREFUN -> BtAnalyticConstant.MOREFUN
            else -> ""
        }
    }

    private fun trackInstallOrUpdate() {
        try {
            val context = EdcApplication.instance
            val preferences = context.getSharedPreferences(PREFERENCES_NAME, Context.MODE_PRIVATE)
            val pInfo = context.packageManager.getPackageInfo(context.packageName, 0)

            val hasSentInstall = preferences.getBoolean(KEY_HAS_SENT_INSTALL, false)
            val currentVersionCode = pInfo.versionCode
            val installedVersionCode: Int = Utils.getInstalledVersion()

            if (!hasSentInstall) {
                handleFirstInstall(preferences, currentVersionCode)
            } else if (isAppUpdated(installedVersionCode, currentVersionCode)) {
                handleAppUpdate(
                    installedVersionCode,
                    currentVersionCode,
                    pInfo.versionName.orEmpty()
                )
            }

            // Update the installed version name
            Utils.setInstallVersionName(pInfo.versionName)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun handleFirstInstall(preferences: SharedPreferences, currentVersionCode: Int) {
        preferences.edit().putBoolean(KEY_HAS_SENT_INSTALL, true).apply()
        Utils.setInstalledVersion(currentVersionCode)
        trackEvent("app_install")
    }

    private fun handleAppUpdate(oldVersionCode: Int, newVersionCode: Int, newVersionName: String) {
        // Prepare properties for the update event
        val map = java.util.HashMap<String, String>().apply {
            put("old_version", oldVersionCode.toString())
            put("new_version", newVersionCode.toString())
        }

        trackEvent("user_app_update", map)

        // Update the installed version code
        Utils.setInstalledVersion(newVersionCode)
    }

    private fun isAppUpdated(oldVersionCode: Int, newVersionCode: Int): Boolean =
        oldVersionCode < newVersionCode && newVersionCode > 0

    fun trackSuperProperty() {
        trackSuperProperty("app_version_name", BuildConfig.VERSION_NAME)
        trackSuperProperty("app_flow", AppConfig.current.variantConfig.analyticsAppFlow)
        if (AppConfig.current.variantConfig.isPartnershipForAnalytics) {
            getEDCMiniATMProClientName()?.let {
                trackSuperProperty("miniatm_partner_client", it)
            }
        }
    }

    private fun initTiktok(context: Context) {
        try {
            Log.d("--->TikTokBusinessSdk", "initializeSdk")
            val ttConfig = TikTokBusinessSdk.TTConfig(context)
                .setAppId("com.bukuwarung.bukuagen") // Android package name
                .setTTAppId(BuildConfig.TIKTOK_APP_ID) // TikTok App ID from TikTok Events Manager

            if (BuildConfig.DEBUG) {
                ttConfig
                    .openDebugMode()
                    .setLogLevel(TikTokBusinessSdk.LogLevel.DEBUG)
            }
            TikTokBusinessSdk.initializeSdk(
                ttConfig,
                object : TikTokBusinessSdk.TTInitCallback {
                    override fun success() {
                        Log.d("--->TikTokBusinessSdk", "success")
                    }

                    override fun fail(code: Int, msg: String?) {
                        Log.d("--->TikTokBusinessSdk", "failed reason = $msg")
                    }
                }
            )
            TikTokBusinessSdk.startTrack()
        } catch (e: Exception) {
            bwLog(e)
        }
    }

    enum class TikTokStandardEvents(val value: String) {
        VIEW_CONTENT("ViewContent"),
        ADD_TO_CART("AddToCart"),
        ADD_PAYMENT_INFO("AddPaymentInfo"),
        CHECKOUT("Checkout"),
        PURCHASE("Purchase")
    }
}
