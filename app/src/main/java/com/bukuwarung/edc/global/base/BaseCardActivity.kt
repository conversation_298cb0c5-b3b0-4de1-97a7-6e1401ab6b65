package com.bukuwarung.edc.global.base

import android.content.Context
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import com.bukuwarung.edc.card.domain.model.consts.Constants
import com.bukuwarung.edc.card.ui.EdcCardViewModel
import com.bukuwarung.edc.card.ui.ErrorMapping
import com.bukuwarung.edc.global.enums.TransactionType
import com.bukuwarung.edc.payments.ui.dialog.CardErrorDialog
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.Utils.bwLog
import com.bukuwarung.edc.util.isTrue

abstract class BaseCardActivity : AppCompatActivity() {

    protected var tag: String? = null
    abstract fun setViewBinding()
    abstract fun setupView()
    abstract fun subscribeState()
    abstract fun onCardRemove()
    protected val edcCardViewModel: EdcCardViewModel by viewModels()
    private var waitForCardRemoveDialog: CardErrorDialog? = null
    private var timeoutErrorDialog: CardErrorDialog? = null
    private var hasInactivityTimer: Boolean = false
    protected var hasShownInactivityTimer: Boolean = false

    private lateinit var handler: Handler
    private val delayMillis: Long = 55 * 1000

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        hasInactivityTimer = false
        hasShownInactivityTimer = false
        setViewBinding()
        setupView()
        observeCardAvailable()
        subscribeState()
    }

    protected fun checkForCardRemove(context: Context) {
        waitForCardRemoveDialog = ErrorMapping.showErrorDialog(
            context = context,
            source = "CARD_REMOVE_INTERCEPTOR",
            errorCode = ErrorMapping.removeCardErrorCode[0]
        )
        edcCardViewModel.onEventReceived(
            EdcCardViewModel.Event.OnCheckForCardRemove(
                1
            )
        )
    }

    private fun observeCardAvailable() {
        edcCardViewModel.cardAvailable.observe(this) {
//            Log.d("card_remove", "card available $it")
            if (it) {
//                Log.d("card_remove", "card available")
                showCardRemoveDialog(true)
                edcCardViewModel.onEventReceived(
                    EdcCardViewModel.Event.OnCheckForCardRemove(
                        1
                    )
                )
            } else {
                showCardRemoveDialog(false)
                Log.d("card_remove", "card removed")
            }
        }
    }

    private fun showCardRemoveDialog(show: Boolean) {
        if (show && !waitForCardRemoveDialog!!.isShowing) {
            waitForCardRemoveDialog?.show()
        } else if (!show) {
            waitForCardRemoveDialog?.dismiss()
            stopInactivityTimer()
            onCardRemove()
            if (Build.MANUFACTURER == Constants.DEVICE_MANUFACTURER_MOREFUN) {
                edcCardViewModel.stopCheckCard()
                edcCardViewModel.abortEmv()
            }
        }
    }

    private val showDialogRunnable = Runnable {
        showInactivityDialog()
    }

    protected fun startInactivityTimer() {
        if (hasInactivityTimer) {
            handler.postDelayed(showDialogRunnable, delayMillis)
        }
    }

    protected fun stopInactivityTimer(forceCloseDialog: Boolean = false) {
        if (hasInactivityTimer) {
            handler.removeCallbacks(showDialogRunnable)
        }
        if (forceCloseDialog && isActivityRunning && hasInactivityTimer &&
            timeoutErrorDialog != null &&
            !timeoutErrorDialog?.isShowing.isTrue
        ) {
            timeoutErrorDialog?.show()
        }
    }

    protected fun setupInactivityDialog() {
        hasInactivityTimer = true
        handler = Handler(Looper.getMainLooper())
        timeoutErrorDialog = ErrorMapping.showErrorDialog(
            context = this,
            source = "INACTIVITY_TIMER",
            errorCode = Constants.CARD_TRANSACTION_TIMEOUT,
            dismissListener = {
                hasShownInactivityTimer = true
                checkForCardRemove(this)
            }
        )
        Log.d("Error", "Inactive for $delayMillis sec")
    }

    private fun showInactivityDialog() {
        try {
            if (isActivityRunning && hasInactivityTimer && timeoutErrorDialog != null &&
                !timeoutErrorDialog?.isShowing.isTrue
            ) {
                timeoutErrorDialog?.show()
                // temporary add this to trigger log mf9191 chip activity
                if (Build.MANUFACTURER == Constants.DEVICE_MANUFACTURER_MOREFUN) {
                    bwLog(e = Exception("${Utils.getPhoneNumber()}_INACTIVITY_TIMER"))
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private var isActivityRunning = false

    override fun onStart() {
        super.onStart()
        Log.d("BaseCardActivity", "onDestroy")
        isActivityRunning = true
    }

    override fun onStop() {
        super.onStop()
        Log.d("BaseCardActivity", "onDestroy")
        isActivityRunning = false
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d("BaseCardActivity", "onDestroy")
        if (hasInactivityTimer) {
            hasInactivityTimer = false
            handler.removeCallbacksAndMessages(null)
        }
    }

    fun getTitleByTransactionType(transactionType: String): String =
        if (transactionType == TransactionType.TRANSFER_INQUIRY.type) {
            "Transfer Via Kartu"
        } else if (transactionType == TransactionType.CASH_WITHDRAWAL.type) {
            "Tarik Tunai Via Kartu"
        } else {
            "Cek Saldo Via Kartu"
        }
}
