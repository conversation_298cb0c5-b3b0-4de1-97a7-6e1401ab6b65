package com.bukuwarung.edc.global.base

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import com.bukuwarung.edc.R
import com.bukuwarung.edc.global.enums.BaseDialogType

/**
 * Base for Android dialogs.
 */
abstract class BaseDialog : Dialog {
    /**
     * Type of this dialog. Available options are Full Screen and Popup.
     * Defaults to Popup.
     */
    private val type: BaseDialogType

    /**
     * Additional Window Feature to be requested.
     * Defaults to -1.
     */
    private var optionalWindowFeature = -1

    /**
     * Set if the dialog is cancellable (by clicking back btn) or not.
     * Defaults to false.
     */
    private var isCancellable = false

    /**
     * Set if the dialog is full width of the device.
     * Defaults to false.
     */
    private var useFullWidth = false

    /**
     * Set if the dialog has min width.
     * Defaults to 0 (a.k.a not taking this value).
     */
    private var minWidth = 0

    /**
     * Creates a Popup dialog.
     */
    constructor(context: Context) : super(context, R.style.DefaultDialogTheme) {
        type = BaseDialogType.POPUP
    }

    /**
     * Creates a dialog according to the base dialog type,
     * either Full screen or popup.
     */
    constructor(context: Context, type: BaseDialogType) : super(
        context,
        if (type ===
            BaseDialogType.FULL_SCREEN
        ) {
            R.style.FullScreenDialogTheme
        } else {
            R.style.DefaultDialogTheme
        }
    ) {
        this.type = type
    }

    abstract val resId: Int

    fun setOptionalWindowFeature(optionalWindowFeature: Int) {
        this.optionalWindowFeature = optionalWindowFeature
    }

    fun setCancellable(isCancellable: Boolean) {
        this.isCancellable = isCancellable
    }

    fun setUseFullWidth(useFullWidth: Boolean) {
        this.useFullWidth = useFullWidth
    }

    fun setMinWidth(minWidth: Int) {
        this.minWidth = minWidth
    }

    /*
     * Reason : kotlin synthetic is deprecated
     * Propose : Using view binding object inside the dialogue
     * Requirement : return 0 when overriding getResId() and just pass a ViewBinding.root object
     * */
    fun setupViewBinding(rootView: View?) {
        setContentView(rootView!!)
        setupView()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setupView()
    }

    private fun setupView() {
        // setting up optional window feature if there's any
        if (optionalWindowFeature >= 0) requestWindowFeature(optionalWindowFeature)

        // Setting up content view
        if (resId != 0) {
            setContentView(resId)
        }

        // setting up window layout based on type && setting up anim
        if (window != null) {
            if (type ===
                BaseDialogType.POPUP
            ) {
                window!!.setBackgroundDrawableResource(R.drawable.bg_base_dialog_rounded)
            } else if (type ===
                BaseDialogType.POPUP_ROUND_CORNERED
            ) {
                window!!.setBackgroundDrawableResource(
                    R.drawable.bg_solid_white_corner_16dp
                )
            }
            val layoutParams =
                if (type ===
                    BaseDialogType.FULL_SCREEN
                ) {
                    WindowManager.LayoutParams.MATCH_PARENT
                } else {
                    WindowManager.LayoutParams.WRAP_CONTENT
                }
            val popupWidth =
                if (minWidth == 0) WindowManager.LayoutParams.WRAP_CONTENT else minWidth
            window!!.setLayout(
                if (useFullWidth) WindowManager.LayoutParams.MATCH_PARENT else popupWidth,
                layoutParams
            )
        }

        // setting up isCancellable flag on dialog
        setCancelable(isCancellable)
    }
}
