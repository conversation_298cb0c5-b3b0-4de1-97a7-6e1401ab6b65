package com.bukuwarung.edc.global

import android.annotation.SuppressLint
import android.util.Log
import com.google.firebase.crashlytics.FirebaseCrashlytics
import timber.log.Timber

class CrashlyticsTree(private val isDebug: Boolean) : Timber.Tree() {
    @SuppressLint("LogNotTimber")
    override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
        // Log to Crashlytics only on production, and priority more than verbose
        if (!isDebug && priority > Log.VERBOSE) {
            FirebaseCrashlytics.getInstance().log("${tag ?: "Timber"}: $message")
            t?.let { FirebaseCrashlytics.getInstance().recordException(it) }
        }
        // Log to Logcat only if enabled (debug builds)
        if (isDebug) {
            t?.printStackTrace()
            when (priority) {
                Log.VERBOSE -> Log.v(tag, message, t)
                Log.DEBUG -> Log.d(tag, message, t)
                Log.INFO -> Log.i(tag, message, t)
                Log.WARN -> Log.w(tag, message, t)
                Log.ERROR -> Log.e(tag, message, t)
                Log.ASSERT -> Log.wtf(tag, message, t)
            }
        }
    }
}
