package com.bukuwarung.edc.global.messaging.di

import com.bukuwarung.edc.app.config.AppConfig
import com.bukuwarung.edc.app.config.NetworkConfig
import com.bukuwarung.edc.global.messaging.data.datasource.FcmRemoteDataStore
import com.bukuwarung.edc.global.messaging.data.repository.FcmRepository
import com.bukuwarung.edc.global.messaging.usecase.PostFcmTokenUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton
import okhttp3.OkHttpClient
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory

@Module
@InstallIn(SingletonComponent::class)
class MessagingDiModule {

    @Singleton
    @Provides
    fun provideFcmRemoteDataStore(
        @Named("normal") okHttpClient: OkHttpClient,
        gsonFactory: GsonConverterFactory,
        networkConfig: NetworkConfig
    ): FcmRemoteDataStore {
        val retrofit = Retrofit.Builder()
            .baseUrl(networkConfig.apiBaseUrl)
            .addConverterFactory(gsonFactory)
            .client(okHttpClient)
            .build()
        return retrofit.create(FcmRemoteDataStore::class.java)
    }

    @Provides
    fun provideFcmRepository(remoteDataSource: FcmRemoteDataStore) = FcmRepository(remoteDataSource)

    @Singleton
    @Provides
    fun providePostFcmTokenUseCase(repo: FcmRepository) = PostFcmTokenUseCase(repo)
}
