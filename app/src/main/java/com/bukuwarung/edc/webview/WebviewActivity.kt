package com.bukuwarung.edc.webview

import android.app.Activity
import android.content.ContentValues
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.location.Address
import android.location.Geocoder
import android.location.Location
import android.net.Uri
import android.os.Bundle
import android.os.Looper
import android.text.TextUtils
import android.util.Log
import android.view.View
import android.widget.RelativeLayout
import android.widget.Toast
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import com.bukuwarung.bluetooth_printer.utils.PermissionConst
import com.bukuwarung.bluetooth_printer.utils.PermissionUtils.requestLocationPermission
import com.bukuwarung.edc.BuildConfig
import com.bukuwarung.edc.R
import com.bukuwarung.edc.ZohoChat
import com.bukuwarung.edc.app.config.VariantConfig
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.global.EdcApplication
import com.bukuwarung.edc.homepage.ui.home.HomePageActivity
import com.bukuwarung.edc.order.orderdetail.ui.EdcOrderDetailsActivity
import com.bukuwarung.edc.payments.constant.PaymentRemoteConfig
import com.bukuwarung.edc.printer.ui.dialog.BukuDialog
import com.bukuwarung.edc.util.EncryptedPreferencesHelper
import com.bukuwarung.edc.util.RemoteConfigUtils
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.Utils.requestLatLongPermission
import com.bukuwarung.edc.util.Utils.sharedPreferences
import com.bukuwarung.edc.util.addQuery
import com.bukuwarung.edc.util.get
import com.bukuwarung.edc.util.goToDestination
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.isNotNullOrBlank
import com.bukuwarung.edc.util.isNotNullOrEmpty
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.openActivityForResult
import com.bukuwarung.edc.util.orDefault
import com.bukuwarung.edc.util.singleClick
import com.bukuwarung.edc.verifyotp.ui.VerifyOtpActivity
import com.bukuwarung.edc.webview.constant.ClassConstants
import com.bukuwarung.edc.worker.location.LocationUtil
import com.bukuwarung.lib.webview.BaseWebviewActivity
import com.bukuwarung.lib.webview.camera.CameraKycActivity
import com.bukuwarung.lib.webview.data.OtpResponse
import com.bukuwarung.lib.webview.data.PrivyCredentials
import com.bukuwarung.lib.webview.util.DeviceDetails
import com.bukuwarung.lib.webview.util.FileSizeLimits
import com.bukuwarung.lib.webview.util.UploadsValidations
import com.bukuwarung.network.NetworkConst.BUKUWARUNG_TOKEN
import com.bukuwarung.network.NetworkConst.SESSION_TOKEN
import com.bukuwarung.network.interceptors.NoConnectivityException
import com.google.android.gms.location.FusedLocationProviderClient
import com.google.android.gms.location.LocationRequest
import com.google.android.gms.location.LocationServices
import com.google.android.gms.tasks.CancellationTokenSource
import com.google.android.material.floatingactionbutton.FloatingActionButton
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.google.gson.reflect.TypeToken
import com.pax.dal.entity.ETermInfoKey
import com.pax.market.android.app.sdk.BaseApiService
import com.pax.market.android.app.sdk.CommonConstants
import com.pax.market.android.app.sdk.SyncApiStrategy
import com.pax.market.android.app.sdk.dto.DcUrlInfo
import com.pax.market.android.app.sdk.util.PreferencesUtils
import com.pax.market.api.sdk.java.base.dto.LocationObject
import com.pax.market.api.sdk.java.base.exception.NotInitException
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.io.IOException
import java.net.SocketTimeoutException
import java.util.Locale
import java.util.concurrent.CountDownLatch
import java.util.concurrent.Semaphore
import java.util.concurrent.TimeUnit
import javax.inject.Inject

@AndroidEntryPoint
class WebviewActivity : BaseWebviewActivity() {

    @Inject
    lateinit var locationUtil: LocationUtil

    private val viewModel: WebviewViewModel by viewModels()

    @Inject
    lateinit var variantConfig: VariantConfig

    companion object {
        private const val REPLACE = "replace"
        private val REDIRECTION_URL = "redirection_url"
        private const val IS_KYC_REQUIRED = "is_kyc_required"
    }

    private val isKycRequired by lazy {
        intent?.getBooleanExtra(IS_KYC_REQUIRED, false) ?: false
    }
    private val fusedLocationClient: FusedLocationProviderClient by lazy {
        LocationServices.getFusedLocationProviderClient(applicationContext)
    }
    private val cancellationTokenSource = CancellationTokenSource()

    override fun allowDebug(): Boolean = BuildConfig.DEBUG

    var locationObject: LocationObject? = null
    var semaphore: Semaphore? = null
    var syncApi: SyncApiStrategy? = null

    override fun getDeeplinkScheme(): String? = null

    override fun getBWBookId(): String = Utils.getPaymentAccountId()

    override fun getLink(): String {
        val url = intent.getStringExtra(ClassConstants.WEBVIEW_URL).orEmpty()
        if (BuildConfig.DEBUG) {
            Toast.makeText(this@WebviewActivity, url, Toast.LENGTH_LONG).show()
        }
        return url
    }

    override fun getTitleText(): String? = if (intent.hasExtra(ClassConstants.WEBVIEW_TITLE)) {
        intent?.getStringExtra(
            ClassConstants.WEBVIEW_TITLE
        )
    } else {
        null
    }

    override fun getToolbarColor(): Int = R.color.colorPrimary

    override fun hideToolBar(): Boolean = if (!intent.hasExtra(ClassConstants.HIDE_TOOLBAR)) {
        false
    } else {
        intent.getBooleanExtra(ClassConstants.HIDE_TOOLBAR, false)
    }

    override fun getUserAgent(): String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        webView?.settings?.javaScriptEnabled = true

        if (BuildConfig.DEBUG) {
            createFloatingButtonForTest()
        }

        locationUtil.getLocation(this)

        if (intent.hasExtra(ClassConstants.WEBVIEW_URL)) {
            val url = intent.getStringExtra(ClassConstants.WEBVIEW_URL) ?: ""
            webView?.loadUrl(url)
        }
    }

    override fun getBWAppToken(): String = EncryptedPreferencesHelper.get(BUKUWARUNG_TOKEN, "")

    override fun getBWAppVersionName(): String? = BuildConfig.VERSION_NAME

    override fun getAppVersionCode(): String? = BuildConfig.VERSION_CODE.toString()

    override fun getAppVersion() {
        webView?.loadUrl("javascript:appVersionCallback('${BuildConfig.VERSION_NAME}')")
    }

    override fun onBackPressed() {
        super.onBackPressed()
    }

    override fun copyToClipboard(text: String, toastText: String) {
        Utils.copyToClipboard(text, this, toastText)
    }

    override fun onSuccess(type: String?) {
        super.onSuccess(type)
        goToDestination(HomePageActivity::class.java)
    }

    override fun handleKycSuccess(kycSuccessFrom: KYC_SUCCESS) {
        when (kycSuccessFrom) {
            KYC_SUCCESS.BASIC_KYC ->
                webView?.loadUrl(
                    PaymentRemoteConfig.getPaymentConfigs().kycKybVerificationUrl.addQuery(
                        "kycSubmitted=true"
                    )
                )

            KYC_SUCCESS.KYC_EDC_ORDER_DETAIL -> {
                openActivity(EdcOrderDetailsActivity::class.java) {
                    putString(EdcOrderDetailsActivity.ORDER_ID, Utils.edcOrderId)
                }
            }

            KYC_SUCCESS.ADDITIONAL_DOC_KYC -> webView?.loadUrl(
                PaymentRemoteConfig.getPaymentConfigs().kycDocsUrl
            )

            KYC_SUCCESS.IS_LENDING -> {
                webView?.loadUrl(PaymentRemoteConfig.getPaymentConfigs().lendingFormUrl)
            }

            KYC_SUCCESS.IS_BNPL -> {
                webView?.loadUrl(PaymentRemoteConfig.getPaymentConfigs().bnplFormUrl)
            }

            KYC_SUCCESS.ADDITIONAL_DOC_KYC_VIDEO -> {
                webView?.loadUrl(
                    PaymentRemoteConfig.getPaymentConfigs().kycKybVerificationUrl.addQuery(
                        "kycSubmitted=true"
                    )
                )
            }

            KYC_SUCCESS.IS_BNPL_REGISTRATION_COMMERCE -> {
                webView?.loadUrl(PaymentRemoteConfig.getPaymentConfigs().bnplFormUrl)
            }

            KYC_SUCCESS.IS_BNPL_REGISTRATION_PPOB -> {
                webView?.loadUrl(PaymentRemoteConfig.getPaymentConfigs().ppobBnplFormUrl)
            }

            else -> {}
        }
    }

    override fun handleKycSuccess(kycFlowConst: String?) {
        getKycRedirectionData()[kycFlowConst]?.let { webView?.loadUrl(it) }
    }

    override fun getPhoneNumber(): String? =
        "+62" + "-" + sharedPreferences.getString("phone_number", null)

    override fun getAppToken(): String = EncryptedPreferencesHelper.get(BUKUWARUNG_TOKEN, "")

    override fun getBWBookName(): String = "edc"

    override fun sendAppsflyerEvent(eventName: String, eventProp: String?) =
        trackMixpanelEvent(eventName, "", eventProp)

    override fun sendMoengageEvent(eventName: String, eventProp: String?) =
        trackMixpanelEvent(eventName, "", eventProp)

    override fun trackEvent(eventName: String, eventProp: String) =
        trackMixpanelEvent(eventName, "", eventProp)

    override fun trackEvent(
        eventName: String,
        jsonProp: String?,
        amplitude: Boolean,
        cleverTap: Boolean,
        firebase: Boolean,
        appsFlyer: Boolean,
        tiktokEventName: String?
    ) = trackMixpanelEvent(eventName, tiktokEventName, jsonProp)

    private fun trackMixpanelEvent(
        eventName: String,
        tiktokEventName: String?,
        eventProp: String?
    ) {
        if (eventProp.isNotNullOrEmpty()) {
            val map = stringToMap(eventProp.orEmpty())
            Analytics.trackEvent(eventName, map)
        } else {
            Analytics.trackEvent(eventName)
        }
        if (tiktokEventName.isNotNullOrBlank()) {
            Analytics.trackTikTokStandardEvents(tiktokEventName)
        }
    }

    private fun stringToMap(eventProp: String): HashMap<String, String> {
        var map = HashMap<String, String>()
        try {
            map =
                Gson().fromJson(eventProp, object : TypeToken<HashMap<String?, String?>?>() {}.type)
        } catch (ex: Exception) {
            FirebaseCrashlytics.getInstance().recordException(ex)
        }
        return map
    }

    override fun getJanusUrl(): String = BuildConfig.API_BASE_URL_JANUS

    override fun isAddressWithinVisitRange(address: String): Boolean {
        val addressJson: JsonObject? = Gson().fromJson(address, JsonObject::class.java)
        val foundCity = RemoteConfigUtils.physicalVisitCities().firstOrNull {
            addressJson?.get("city")?.asString?.lowercase() == it.lowercase()
        }
        return foundCity != null
    }

    override fun requestLocation() {
        if (Utils.hasLocationPermission()) {
            fetchLocation()
        } else {
            requestLatLongPermission(this)
        }
    }

    private fun fetchLocation() {
        fusedLocationClient.getCurrentLocation(
            LocationRequest.PRIORITY_HIGH_ACCURACY,
            cancellationTokenSource.token
        )
            .addOnSuccessListener { location ->
                location?.let {
                    fetchLocationCallback(location)
                }
            }
            .addOnFailureListener {
                unableToFetchLocationCallback()
            }
    }

    private fun unableToFetchLocationCallback() {
        val locationCallbackJsonObject = JSONObject()
        locationCallbackJsonObject.put("message", "Unable to fetch location")
        locationCallbackJsonObject.put("success", false)
        val locationCallbackJsonString = locationCallbackJsonObject.toString()
        val javascriptCommand = "locationCallback($locationCallbackJsonString)"
        webView?.evaluateJavascript(javascriptCommand, null)
    }

    private fun fetchLocationCallback(location: Location) {
        val geocoder = Geocoder(applicationContext, Locale.getDefault())
        try {
            // Get address list from the geocoder
            CoroutineScope(Dispatchers.IO).launch {
                val addressList = geocoder.getFromLocation(location.latitude, location.longitude, 1)
                withContext(Dispatchers.Main) {
                    if (!addressList.isNullOrEmpty()) {
                        val address = addressList[0]

                        // Extract address components
                        val city = address.locality // City
                        val state = address.adminArea // Province/State
                        val country = address.countryName // Country
                        val postalCode = address.postalCode // Postal Code
                        val premise = address.premises
                        val subLocality = address.subLocality
                        val subAdminArea = address.subThoroughfare
                        val subThoroughfare = address.subThoroughfare
                        val addressLine = address.getAddressLine(0) // Full Address

                        // Use the details as needed
                        Log.d("--->Address", "City: $city")
                        Log.d("--->Address", "State: $state")
                        Log.d("--->Address", "Country: $country")
                        Log.d("--->Address", "Postal Code: $postalCode")
                        Log.d("--->Address", "premise: $premise")
                        Log.d("--->Address", "subLocality: $subLocality")
                        Log.d("--->Address", "subAdminArea: $subAdminArea")
                        Log.d("--->Address", "subThoroughfare: $subThoroughfare")
                        Log.d("--->Address", "Full Address: $addressLine")

                        val locationCallbackJsonObject = JSONObject()
                        locationCallbackJsonObject.put("message", "location fetched")
                        locationCallbackJsonObject.put("success", true)
                        locationCallbackJsonObject.put("latitude", location.latitude)
                        locationCallbackJsonObject.put("longitude", location.longitude)
                        locationCallbackJsonObject.put("City", city)
                        locationCallbackJsonObject.put("State", state)
                        locationCallbackJsonObject.put("Country", country)
                        locationCallbackJsonObject.put("Postal Code", postalCode)
                        locationCallbackJsonObject.put("Full Address", addressLine)
                        locationCallbackJsonObject.put("premise", premise)
                        locationCallbackJsonObject.put("subLocality", subLocality)
                        locationCallbackJsonObject.put("subAdminArea", subAdminArea)
                        locationCallbackJsonObject.put("subThoroughfare", subThoroughfare)
                        val locationCallbackJsonString = locationCallbackJsonObject.toString()
                        val javascriptCommand = "locationCallback($locationCallbackJsonString)"
                        webView?.evaluateJavascript(javascriptCommand, null)
                    } else {
                        unableToFetchLocationCallback()
                        println("No address found for the provided coordinates.")
                    }
                }
            }
        } catch (e: IOException) {
            e.printStackTrace()
            // Handle error: unable to retrieve address
            unableToFetchLocationCallback()
            println("Unable to get address from latitude and longitude")
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        when (requestCode) {
            PermissionConst.REQUEST_CODE_LAT_LONG -> {
                if (grantResults.isNotEmpty() &&
                    grantResults[0] == PackageManager.PERMISSION_GRANTED
                ) {
                    fetchLocation()
                } else {
                    requestLocationPermission(this)
                }
            }
        }
    }

    override fun getAuthUrl(): String = BuildConfig.MWEB_BASE_URL

    override fun getSessionToken(): String = sharedPreferences.get(SESSION_TOKEN, "").orEmpty()

    override fun getFileSizeLimits(): FileSizeLimits =
        PaymentRemoteConfig.getPaymentConfigs().fileSizeLimits ?: FileSizeLimits()

    override fun getUploadValidations(): UploadsValidations =
        PaymentRemoteConfig.getPaymentConfigs().uploadsValidations ?: UploadsValidations()

    override fun shouldApplyNewCompression(): Boolean = RemoteConfigUtils.applyNewCompression()

    override fun fetchLocationAndImage(imageType: String) {
        startActivityForResult(
            CameraKycActivity.createIntent(
                this,
                imageType,
                useCase = CameraKycActivity.UseCase.IMAGE_AND_LOCATION
            ),
            RC_LOCATION_AND_IMAGE
        )
    }

    private val otpVerificationActivityForResult =
        registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result: ActivityResult ->
            if (result.resultCode == Activity.RESULT_OK) {
                val otpResponse = OtpResponse(
                    result.data?.getStringExtra(VerifyOtpActivity.OTP_STATUS),
                    result.data?.getStringExtra(VerifyOtpActivity.OTP_TOKEN)
                )
                webView?.loadUrl("javascript:otpCallback('${Gson().toJson(otpResponse)}')")
            }
        }

    override fun startOtpVerification(phoneNumber: String, countryCode: String, useCase: String) {
        super.startOtpVerification(phoneNumber, countryCode, useCase)
        openActivityForResult(VerifyOtpActivity::class.java, otpVerificationActivityForResult) {
            putString(
                com.bukuwarung.edc.verifyotp.constant.ClassConstants.VERIFY_OTP_PARAM_PHONE,
                phoneNumber
            )
            putString(
                com.bukuwarung.edc.verifyotp.constant.ClassConstants.VERIFY_OTP_PARAM_COUNTRY_CODE,
                "+62"
            )
            putString(VerifyOtpActivity.OTP_USE_CASE, useCase)
        }
    }

    override fun openWebview(url: String?) {
        url?.let {
            val browserIntent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
            startActivity(browserIntent)
        }
    }

    override fun getAccountVerificationUrl(): String =
        PaymentRemoteConfig.getPaymentConfigs().kycWebUrl.addQuery(
            "landing=${variantConfig.variantIdentifier}"
        )

    override fun onCloseWebview() {
        super.onCloseWebview()
    }

    override fun restartKyc() {
        webView?.loadUrl(
            PaymentRemoteConfig.getPaymentConfigs().kycWebUrl.addQuery(
                "landing=${variantConfig.variantIdentifier}"
            )
        )
    }

    override fun launchActivityForResult(requestType: String) {
        super.launchActivityForResult(requestType)
    }

    override fun getKycRedirectionData(): HashMap<String, String> =
        PaymentRemoteConfig.getPaymentConfigs().kycRedirectionData ?: hashMapOf()

    override fun performImageValidations(): Boolean =
        PaymentRemoteConfig.getPaymentConfigs().performImageValidations.isTrue

    override fun getEDCDevice(): DeviceDetails =
        DeviceDetails(Utils.getTerminalId(), Utils.getDeviceSerialNumber())

    override fun getPrivyCredentials(): PrivyCredentials? =
        PaymentRemoteConfig.getPaymentConfigs().privyCredentials

    override fun getEnv(): String = BuildConfig.FLAVOR

    override fun getVideoQuality(): String =
        PaymentRemoteConfig.getPaymentConfigs().videoQuality.orDefault(Constant.SD)

    override fun getImageQuality(): Int =
        PaymentRemoteConfig.getPaymentConfigs().imageQuality.orDefault(
            Constant.IMAGE_QUALITY_DEFAULT
        )

    override fun getCompressionQuality(): String =
        PaymentRemoteConfig.getPaymentConfigs().compressionQuality.orDefault(Constant.LOW)

    override fun getLuminosityCheck(): Boolean =
        PaymentRemoteConfig.getPaymentConfigs().shouldCheckLuminosity

    override fun getMinLuminosity(): Float =
        PaymentRemoteConfig.getPaymentConfigs().minRequiredLuminosity

    override fun getPhotoCompressionSize(): Float =
        PaymentRemoteConfig.getPaymentConfigs().photoCompressionSize

    override fun getWebviewUserUUID(): String {
        var identity = EncryptedPreferencesHelper.get("uuid", "")
        if (TextUtils.isEmpty(identity)) {
            identity = "-"
        }
        return identity
    }

    override fun getBWUserId(): String = Utils.getUserId()

    override fun getUserId(): String? = sharedPreferences.getString("phone_number", null) ?: ""

    override fun fetchRemoteConfig(key: String): String? =
        RemoteConfigUtils.remoteConfig.getString(key)

    override fun webviewRefreshToken(): String? {
        try {
            return viewModel.refreshToken()
        } catch (t: NoConnectivityException) {
            Toast.makeText(this@WebviewActivity, t.message, Toast.LENGTH_LONG).show()
        } catch (t: SocketTimeoutException) {
            Toast.makeText(
                this@WebviewActivity,
                "Terjadi kesalahan, coba lagi nanti",
                Toast.LENGTH_LONG
            ).show()
        } catch (e: Exception) {
            Toast.makeText(
                this@WebviewActivity,
                "Terjadi kesalahan, coba lagi nanti",
                Toast.LENGTH_LONG
            ).show()
        }
        return ""
    }

    override fun getAppealBankAccount() {
        val appealBankAccount = intent.getStringExtra(ClassConstants.APPEAL_BANK_ACCOUNT)
        runOnUiThread {
            webView?.loadUrl("javascript:selectBankCallback('$appealBankAccount')")
        }
    }

    override fun openBWActivity(activity: String, parameter: String, title: String) {
        Log.d("--->openBWActivity", "activity = $activity")
        Log.d("--->openBWActivity", "parameter = $parameter")
        Log.d("--->openBWActivity", "title = $title")
        openActivity(activity, parameter, title)
    }

    override fun openDialog(
        title: String,
        message: String,
        positiveButtonType: String,
        negativeButtonType: String,
        activity: String,
        parameter: String
    ) {
        var dialog: BukuDialog? = null
        dialog = BukuDialog(
            context = this,
            title = title,
            subTitle = message,
            image = R.drawable.ic_transaction_failed,
            isLoader = false,
            btnLeftListener = {
                dialog?.dismiss()
                when (DialogButtonType.valueOf(negativeButtonType)) {
                    DialogButtonType.BACK -> onBackPressed()
                    DialogButtonType.CUSTOMER_SERVICE -> {
                        ZohoChat.openZohoChat("saldo-pin")
                    }

                    DialogButtonType.OPEN_ACTIVITY -> openActivity(activity, parameter, "")
                }
            },
            btnRightListener = {
                dialog?.dismiss()
                when (DialogButtonType.valueOf(positiveButtonType)) {
                    DialogButtonType.BACK -> onBackPressed()
                    DialogButtonType.CUSTOMER_SERVICE -> {
                        ZohoChat.openZohoChat("saldo-pin")
                    }

                    DialogButtonType.OPEN_ACTIVITY -> openActivity(activity, parameter, "")
                }
            },
            btnLeftText = if (negativeButtonType ==
                DialogButtonType.CUSTOMER_SERVICE.name
            ) {
                "Hubungi CS"
            } else {
                "Kembali"
            },
            btnRightText = if (positiveButtonType ==
                DialogButtonType.CUSTOMER_SERVICE.name
            ) {
                "Hubungi CS"
            } else {
                "Kembali"
            }
        )
        Utils.showDialogIfActivityAlive(this, dialog)
    }

    private fun openActivity(activity: String, parameter: String, title: String) {
        var shouldFinish = false
        val intent = Intent(this, Class.forName(activity))
        if (activity == "com.bukuwarung.edc.homepage.ui.home.HomePageActivity") {
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
        }
        if (!parameter.equals("undefined", true)) {
            val jsonObject = JSONObject(parameter)
            val keys = jsonObject.keys()
            while (keys.hasNext()) {
                val key = keys.next()
                if (key.equals(REPLACE, true)) {
                    shouldFinish = jsonObject.optBoolean(key)
                }
                if (key.equals(REDIRECTION_URL, true)) {
                    shouldFinish = true
                }
                intent.putExtra(key, jsonObject[key].toString())
            }
        }
        startActivity(intent)
        if (shouldFinish) {
            finish()
        }
    }

    private fun createFloatingButtonForTest() {
        val fab = FloatingActionButton(this)

        val id = View.generateViewId()
        fab.id = id

        val params = RelativeLayout.LayoutParams(
            RelativeLayout.LayoutParams.WRAP_CONTENT,
            RelativeLayout.LayoutParams.WRAP_CONTENT

        )

        addContentView(fab, params)

        fab.hideView()
        fab.singleClick {
            GlobalScope.launch {
                syncApi = SyncApiStrategy(
                    this@WebviewActivity,
                    null,
                    Constant.PAX_STORE_CLIENT_ID,
                    Constant.PAX_STORE_CLIENT_SECRET,
                    EdcApplication.dal?.sys?.termInfo?.getValue(ETermInfoKey.SN)
                ).setProxyDelegate(BaseApiService.getInstance(this@WebviewActivity))
                semaphore = Semaphore(2)
                try {
                    locationObject = syncApi().locate

                    if (locationObject != null && locationObject!!.latitude != null &&
                        locationObject!!.longitude != null
                    ) {
                        val address = Address(Locale.getDefault())
                        address.latitude = locationObject!!.latitude.toDouble()
                        address.longitude = locationObject!!.longitude.toDouble()
                        runOnUiThread {
                            Toast.makeText(
                                this@WebviewActivity,
                                "latitude -> " + locationObject!!.latitude,
                                Toast.LENGTH_LONG
                            ).show()
                        }
                        viewModel.storeLocation(address)
                    }
                } catch (e: Exception) {
                    Log.d("not init", e.message.toString())
                }
            }
        }
    }

    @Throws(NotInitException::class)
    fun syncApi(): SyncApiStrategy {
        if (syncApi == null) {
            acquireSemaphore()
            if (syncApi == null) {
                throw NotInitException("Not initialized")
            }
        }
        syncApi?.baseUrl = getDcUrl(this, syncApi?.baseUrl ?: "", false)
        syncApi?.setProxyDelegate<SyncApiStrategy>(BaseApiService.getInstance(this))
        return syncApi ?: throw NotInitException("Not initialized")
    }

    fun acquireSemaphore() {
        try {
            semaphore?.tryAcquire(2, 5, TimeUnit.SECONDS)
        } catch (e: InterruptedException) {
            // Do something with the InterruptedException if needed
        }
        if (semaphore?.availablePermits() == 0) {
            semaphore?.release(2)
        }
    }

    @Throws(NotInitException::class)
    fun getDcUrl(context: Context, oriBaseUrl: String, tid: Boolean): String? {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            throw NotInitException("Can not do this on MainThread!!")
        }

        val dcUrl = StringBuilder()
        val countDownLatch = CountDownLatch(1)

        BaseApiService.getInstance(context).getDcUrl(
            object : BaseApiService.DcCallBack {
                override fun initSuccess(baseUrl: String) {
                    saveLastUrl(baseUrl, context)
                    dcUrl.append(baseUrl)
                    countDownLatch.countDown()
                }

                override fun initFailed(e: Exception) {
                    Log.e("StoreSdk", "e:$e")
                    countDownLatch.countDown()
                }
            },
            oriBaseUrl
        )

        try {
            countDownLatch.await(30, TimeUnit.SECONDS)
        } catch (e: InterruptedException) {
            Log.e(ContentValues.TAG, "e:$e")
        }

        if (dcUrl.toString().isEmpty() || dcUrl.toString().equals("null", ignoreCase = true)) {
            if (tid) {
                return null
            } else {
                throw NotInitException(
                    "Get baseUrl failed, client is not installed or terminal is not activated."
                )
            }
        }
        return dcUrl.toString()
    }

    private fun saveLastUrl(baseUrl: String, context: Context) {
        val localDcUrlInfo = PreferencesUtils.getObject(
            context,
            CommonConstants.SP_LAST_GET_DCURL_TIME,
            DcUrlInfo::class.java
        )
        // update last getDcUrl time if there has been more than one hour.
        if (localDcUrlInfo == null ||
            (
                System.currentTimeMillis() - localDcUrlInfo.lastAccessTime >
                    CommonConstants.ONE_HOUR_INTERVAL
                )
        ) {
            val dcUrlInfo = DcUrlInfo()
            dcUrlInfo.dcUrl = baseUrl
            dcUrlInfo.lastAccessTime = System.currentTimeMillis()
            PreferencesUtils.putObject(context, CommonConstants.SP_LAST_GET_DCURL_TIME, dcUrlInfo)
        }
    }

    enum class DialogButtonType {
        BACK,
        CUSTOMER_SERVICE,
        OPEN_ACTIVITY
    }
}
