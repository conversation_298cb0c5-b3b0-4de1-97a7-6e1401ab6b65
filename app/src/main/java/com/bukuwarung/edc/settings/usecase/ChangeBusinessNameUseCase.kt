package com.bukuwarung.edc.settings.usecase

import com.bukuwarung.edc.settings.data.model.BookValidationRequest
import com.bukuwarung.edc.settings.repository.ChangeBusinessNameRepo
import javax.inject.Inject

class ChangeBusinessNameUseCase @Inject constructor(
    private val repository: ChangeBusinessNameRepo
) {

    suspend fun validateBookName(requestBody: BookValidationRequest) =
        repository.validateBookName(requestBody)
}
