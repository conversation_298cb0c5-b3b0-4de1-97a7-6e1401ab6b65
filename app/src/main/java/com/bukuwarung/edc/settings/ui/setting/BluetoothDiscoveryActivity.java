package com.bukuwarung.edc.settings.ui.setting;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.util.Log;
import android.widget.ArrayAdapter;
import android.widget.ListView;

import androidx.appcompat.app.AppCompatActivity;

import com.bukuwarung.edc.R;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

public class BluetoothDiscoveryActivity extends AppCompatActivity {

    private ListView listView;
    private Map<String, String> mDeviceMap = new HashMap<>();
    private ArrayList<String> mDeviceList = new ArrayList<>();
    private final BroadcastReceiver mReceiver = new BroadcastReceiver() {
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (BluetoothDevice.ACTION_FOUND.equals(action)) {
                BluetoothDevice device = intent
                        .getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
                if (mDeviceMap.get(device.getAddress()) == null) {
                    mDeviceMap.put(device.getAddress(), device.getName() + "\n" + device.getAddress());
                    mDeviceList.add(device.getName() + "\n" + device.getAddress());
                }
                Log.i("BT", device.getName() + "\n" + device.getAddress());
                listView.setAdapter(
                        new ArrayAdapter<String>(
                                context,
                                android.R.layout.simple_list_item_1, mDeviceList
                        )
                );
            }
        }
    };
    private BluetoothAdapter mBluetoothAdapter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_bluetooth_discovery);

        listView = (ListView) findViewById(R.id.listView);

        mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        mBluetoothAdapter.startDiscovery();

        IntentFilter filter = new IntentFilter(BluetoothDevice.ACTION_FOUND);
        registerReceiver(mReceiver, filter);

    }

    @Override
    protected void onDestroy() {
        unregisterReceiver(mReceiver);
        super.onDestroy();
    }
}