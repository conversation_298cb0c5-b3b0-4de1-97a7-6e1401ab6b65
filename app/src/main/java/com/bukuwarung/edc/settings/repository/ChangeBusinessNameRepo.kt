package com.bukuwarung.edc.settings.repository

import com.bukuwarung.edc.settings.data.datasource.ChangeBusinessNameRemoteDataSource
import com.bukuwarung.edc.settings.data.model.BookValidationRequest
import javax.inject.Inject

class ChangeBusinessNameRepo @Inject constructor(
    private val remoteDataSource: ChangeBusinessNameRemoteDataSource
) {

    suspend fun validateBookName(requestBody: BookValidationRequest) =
        remoteDataSource.validateBookName(requestBody)
}
