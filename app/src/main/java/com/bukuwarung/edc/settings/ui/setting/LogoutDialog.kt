package com.bukuwarung.edc.settings.ui.setting

import android.content.Context
import android.os.Bundle
import androidx.appcompat.widget.AppCompatImageView
import com.bukuwarung.edc.R
import com.bukuwarung.edc.card.domain.usecase.ConfigureAidUseCase
import com.bukuwarung.edc.global.base.BaseDialog
import com.bukuwarung.edc.global.enums.BaseDialogType
import com.bukuwarung.edc.login.ui.LoginActivity
import com.bukuwarung.edc.util.EncryptedPreferencesHelper
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.put
import com.bukuwarung.edc.util.singleClick
import com.bukuwarung.network.NetworkConst.BUKUWARUNG_TOKEN
import com.google.android.gms.tasks.Task
import com.google.android.material.button.MaterialButton
import com.google.firebase.messaging.FirebaseMessaging

class LogoutDialog(context: Context) : BaseDialog(context, BaseDialogType.POPUP) {

    override val resId: Int
        get() = R.layout.dialog_logout

    override fun onCreate(savedInstanceState: Bundle?) {
        setUseFullWidth(false)
        setCancellable(true)
        val minWidth = (context.resources.displayMetrics.widthPixels * 0.90).toInt()
        setMinWidth(minWidth)
        super.onCreate(savedInstanceState)
        setUpView()
    }

    private fun setUpView() {
        findViewById<AppCompatImageView>(R.id.iv_close).singleClick {
            dismiss()
        }

        findViewById<MaterialButton>(R.id.btn_accept).singleClick {
            dismiss()
        }

        findViewById<MaterialButton>(R.id.btn_deny).singleClick {
            EncryptedPreferencesHelper.put("uuid", "")
            EncryptedPreferencesHelper.put(BUKUWARUNG_TOKEN, "")
            EncryptedPreferencesHelper.put("payment_account_id", "")
            Utils.sharedPreferences.put("store_name", "")
            Utils.sharedPreferences.put("t_id", "")
            Utils.sharedPreferences.put("user_id", "")
            Utils.sharedPreferences.put("show_komisi_agen_dialog", true)
            Utils.sharedPreferences.put(Utils.SETTLEMENT_BANK_ACCOUNT, "")
            Utils.sharedPreferences.put(Utils.CONFIRM_CASH_WITHDRAWAL_ACCOUNT, false)
            Utils.sharedPreferences.put(ConfigureAidUseCase.EMV_SHARED_PREF, false)
            Utils.isNoSakuDeviceRegisteredBTShown = false
            Utils.sharedPreferences.put(ConfigureAidUseCase.EMV_SHARED_PREF, false)
            Utils.setShouldRetryPostFcmToken(true)
            if (!Utils.isPaxVerifoneDevice()) {
                FirebaseMessaging.getInstance().deleteToken()
                    .addOnCompleteListener { _: Task<Void?> -> }
            }
            context.openActivity(LoginActivity::class.java)
            dismiss()
            Utils.clearDataAndLogout(false)
        }
    }
}
