package com.bukuwarung.edc.settings.ui.setting

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.TextView
import androidx.appcompat.widget.SwitchCompat
import com.bukuwarung.edc.R
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.put

class SoundSettingsAdapter(context: Context, private val soundSettingsList: MutableList<String>) :
    ArrayAdapter<String>(context, 0, soundSettingsList) {

    override fun getView(position: Int, view: View?, parent: ViewGroup): View {
        var convertView = view
        val item = soundSettingsList[position]
        if (convertView == null) {
            val layoutInflater = LayoutInflater.from(parent.context)
            convertView = layoutInflater.inflate(R.layout.item_label_switch, parent, false)
        }
        val switchButton = convertView?.findViewById<SwitchCompat>(R.id.switch_btn)
        val soundsSettingsTitle = convertView?.findViewById<TextView>(R.id.label)
        soundsSettingsTitle?.text = item
        switchButton?.setOnCheckedChangeListener(null)
        switchButton?.isChecked = loadSoundPreferences(item)
        switchButton?.setOnCheckedChangeListener { buttonView, isChecked ->
            savePreferences(item, isChecked)
        }
        convertView?.setOnClickListener {
            switchButton?.toggle()
        }
        return convertView!!
    }

    private fun loadSoundPreferences(setting: String): Boolean =
        Utils.sharedPreferences.getBoolean(setting, false)

    private fun savePreferences(data: String, isSelected: Boolean) {
        Utils.sharedPreferences.put(data, isSelected)
    }

    override fun getCount(): Int = soundSettingsList.size
}
