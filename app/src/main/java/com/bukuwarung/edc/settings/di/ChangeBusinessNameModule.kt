package com.bukuwarung.edc.settings.di

import com.bukuwarung.edc.settings.data.datasource.ChangeBusinessNameRemoteDataSource
import com.bukuwarung.edc.settings.repository.ChangeBusinessNameRepo
import com.bukuwarung.edc.settings.usecase.ChangeBusinessNameUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton
import retrofit2.Retrofit

@Module
@InstallIn(SingletonComponent::class)
class ChangeBusinessNameModule {

    @Provides
    @Singleton
    fun provideChangeBusinessDataSource(
        @Named("normal") retrofit: Retrofit
    ): ChangeBusinessNameRemoteDataSource =
        retrofit.create(ChangeBusinessNameRemoteDataSource::class.java)

    @Provides
    fun provideChangeBusinessNameRepo(
        api: ChangeBusinessNameRemoteDataSource
    ): ChangeBusinessNameRepo = ChangeBusinessNameRepo(api)

    @Provides
    fun provideChangeBusinessNameUseCase(repo: ChangeBusinessNameRepo): ChangeBusinessNameUseCase =
        ChangeBusinessNameUseCase(repo)
}
