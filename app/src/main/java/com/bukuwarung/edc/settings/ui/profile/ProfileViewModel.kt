package com.bukuwarung.edc.settings.ui.profile

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.edc.settings.data.model.BookValidationRequest
import com.bukuwarung.edc.settings.usecase.ChangeBusinessNameUseCase
import com.bukuwarung.edc.util.Utils
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.firebase.firestore.FirebaseFirestore
import com.bukuwarung.edc.app.config.VariantConfig
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import org.json.JSONObject

@HiltViewModel
class ProfileViewModel @Inject constructor(
    private val changeBusinessNameUseCase: ChangeBusinessNameUseCase,
    private val variantConfig: VariantConfig
) : ViewModel() {

    private val _state = MutableStateFlow(ProfileState())
    val state = _state.asStateFlow()

    sealed class ChangeBusinessNameIntent {
        data class OnBusinessNameChange(val businessName: String) : ChangeBusinessNameIntent()
        data class ShowBusinessName(val businessName: String) : ChangeBusinessNameIntent()
        data class ValidateName(val businessName: String) : ChangeBusinessNameIntent()
        data class ShowAddress(val address: String) : ChangeBusinessNameIntent()
    }

    data class ProfileState(
        val businessName: String = "",
        val newBusinessName: String = "",
        val address: String = "",
        val isError: Boolean = false,
        val isLoading: Boolean = false,
        val isButtonEnabled: Boolean = false,
        val errorMessage: String = "",
        val success: Boolean = false
    )

    fun handleIntent(intent: ChangeBusinessNameIntent) {
        when (intent) {
            is ChangeBusinessNameIntent.ShowBusinessName -> {
                _state.value = state.value.copy(
                    businessName = intent.businessName,
                    newBusinessName = intent.businessName,
                    isButtonEnabled = false,
                    isError = false,
                    errorMessage = ""
                )
            }

            is ChangeBusinessNameIntent.OnBusinessNameChange -> {
                val isButtonEnabled =
                    intent.businessName.isNotBlank() && state.value.newBusinessName.isNotBlank() &&
                        intent.businessName != state.value.businessName
                _state.value = state.value.copy(
                    newBusinessName = intent.businessName,
                    isButtonEnabled = isButtonEnabled,
                    isError = false,
                    errorMessage = ""
                )
            }

            is ChangeBusinessNameIntent.ValidateName -> {
                val bookId: String = Utils.getPaymentAccountId()
                Log.d("--->", "bookId: $bookId")
                validateBookName(intent.businessName, bookId)
            }

            is ChangeBusinessNameIntent.ShowAddress -> {
                _state.value = state.value.copy(address = intent.address)
            }
        }
    }

    private fun validateBookName(businessName: String, bookId: String) {
        if (variantConfig.isPartnershipForProfile) {
            updateBook(bookId)
        } else {
            try {
                _state.value =
                    state.value.copy(isLoading = true, isButtonEnabled = false, isError = false)
                viewModelScope.launch {
                    val response =
                        changeBusinessNameUseCase.validateBookName(
                            BookValidationRequest(
                                businessName
                            )
                        )
                    if (response.isSuccessful) {
                        updateBook(bookId)
                    } else {
                        val errorBody = JSONObject(response.errorBody()?.string())
                        var errorMessage = ""
                        if (errorBody.has("message")) {
                            val rawMessage = errorBody.getString("message")
                            errorMessage = parseErrorMessageFromMessage(rawMessage)
                        }
                        _state.value = state.value.copy(
                            isLoading = false,
                            isError = true,
                            isButtonEnabled = false,
                            errorMessage = errorMessage,
                            success = false
                        )
                    }
                }
            } catch (e: Exception) {
                Log.d("--->", "exception: ${e.message}")
                e.printStackTrace()
                FirebaseCrashlytics.getInstance().recordException(e)
            }
        }
    }

    private fun updateBook(bookId: String) {
        if (variantConfig.isPartnershipForProfile) {
            _state.value =
                state.value.copy(isLoading = false, isButtonEnabled = false, success = true)
        } else {
            val mFirestore = FirebaseFirestore.getInstance()
            try {
                val bookRef = mFirestore.collection("book_store")
                    .document(bookId)
                bookRef.update("businessName", state.value.newBusinessName)
                    .addOnSuccessListener {
                        _state.value = state.value.copy(
                            isLoading = false,
                            isButtonEnabled = false,
                            success = true
                        )
                    }
                    .addOnFailureListener {
                        _state.value = state.value.copy(
                            isLoading = false,
                            isButtonEnabled = false,
                            isError = true,
                            errorMessage = it.message.orEmpty()
                        )
                    }
            } catch (e: java.lang.Exception) {
                e.printStackTrace()
                FirebaseCrashlytics.getInstance().recordException(e)
            }
        }
    }

    private fun parseErrorMessageFromMessage(message: String): String = try {
        val innerMessage = JSONObject(message)
        innerMessage.optString(
            "error_message",
            "An unknown error occurred"
        ) // Safely get the error_message field
    } catch (ex: Exception) {
        "An unknown error occurred"
    }
}
