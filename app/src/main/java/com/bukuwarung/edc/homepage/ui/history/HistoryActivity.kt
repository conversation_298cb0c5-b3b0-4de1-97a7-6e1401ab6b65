package com.bukuwarung.edc.homepage.ui.history

import android.os.Build
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.edc.BuildConfig
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.ActivityHistoryBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.global.EdcApplication
import com.bukuwarung.edc.homepage.constant.HomePageAnalyticsConstants
import com.bukuwarung.edc.homepage.data.model.HomePageBody
import com.bukuwarung.edc.homepage.ui.history.HomeHistoryFragment.Companion.SHOW_RED_DOT
import com.bukuwarung.edc.homepage.ui.tile.model.TileData
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.singleClick
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class HistoryActivity :
    AppCompatActivity(),
    HistoryAdapter.Callback {

    companion object {
        private const val URL = "url"
        private const val TYPE = "type"
        private const val CONTENTS = "contents"
    }

    private lateinit var binding: ActivityHistoryBinding
    private var adapter: HistoryAdapter? = null
    private val versionCode by lazy { BuildConfig.VERSION_CODE }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityHistoryBinding.inflate(layoutInflater)
        setContentView(binding.root)
        val bodyContent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            intent?.getParcelableExtra(CONTENTS, HomePageBody::class.java)
        } else {
            intent?.getParcelableExtra(CONTENTS)
        }
        var historyContent = bodyContent?.bodyContent
        historyContent = historyContent?.filter {
            it?.isVisible!! && (
                it.endVersion >= versionCode ||
                    it.endVersion == -1
                ) && it.startVersion <= versionCode
        }?.sortedBy { it?.rank }
        historyContent = historyContent?.filter {
            (EdcApplication.isVerifoneDevice && it?.shouldShowOnVerifone.isTrue) ||
                (!EdcApplication.isVerifoneDevice && it?.shouldShowOnPax.isTrue)
        }
        val showRedDot = intent?.getBooleanExtra(SHOW_RED_DOT, false)

        val tiles: MutableList<TileData> = mutableListOf()

        if (historyContent != null) {
            for (tile in historyContent) {
                tiles.add(
                    TileData(
                        tile?.deeplinkType,
                        tile?.deeplinkUrl,
                        tile?.icon,
                        tile?.displayName,
                        tile?.analyticsName,
                        tile?.shouldShowOnVerifone,
                        tile?.shouldShowOnPax,
                        if (tile?.displayName == "Pembelian EDC") showRedDot else false
                    )
                )
            }
        }

        adapter = HistoryAdapter(tiles, this@HistoryActivity)
        with(binding) {
            rvHistory.layoutManager = LinearLayoutManager(this@HistoryActivity)
            rvHistory.adapter = adapter
            toolbar.btnBack.singleClick { onBackPressed() }
            toolbar.tvTitle.text = getString(R.string.history)
        }
    }

    override fun buttonClicked(tile: TileData) {
        val map = HashMap<String, String>()
        if (tile.analyticsName != null) {
            map[HomePageAnalyticsConstants.BUTTON_NAME] = tile.analyticsName
            Analytics.trackEvent(HomePageAnalyticsConstants.HOMEPAGE_BUTTON_CLICK, map)
        } else {
            Analytics.trackEvent(HomePageAnalyticsConstants.HOMEPAGE_BUTTON_CLICK)
        }
        openActivity(Utils.getRouterClass()) {
            putString(TYPE, tile.type)
            putString(URL, tile.deeplink)
        }
    }
}
