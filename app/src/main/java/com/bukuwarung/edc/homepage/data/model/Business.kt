package com.bukuwarung.edc.homepage.data.model

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class Business(
    @SerializedName("id")
    val id: String? = null,
    @SerializedName("businessId")
    val businessId: String? = null,
    @SerializedName("userId")
    val userId: String? = null,
    @SerializedName("name")
    val name: String? = null,
    @SerializedName("categoryId")
    val categoryId: String? = null,
    @SerializedName("ownerName")
    val ownerName: String? = null,
    @SerializedName("enablePayment")
    val enablePayment: Boolean? = null,
    @SerializedName("bnpl")
    val bnpl: Boolean? = null,
    @SerializedName("deleted")
    val deleted: Boolean? = null,
    @SerializedName("createdByDevice")
    val createdByDevice: String? = null,
    @SerializedName("modifiedByDevice")
    val modifiedByDevice: String? = null,
    @SerializedName("createdAt")
    val createdAt: String? = null,
    @SerializedName("lastModifiedAt")
    val lastModifiedAt: String? = null
)
