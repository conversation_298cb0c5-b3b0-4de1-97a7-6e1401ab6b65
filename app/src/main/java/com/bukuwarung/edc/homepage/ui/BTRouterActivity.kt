package com.bukuwarung.edc.homepage.ui

import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import com.bukuwarung.bluetooth_printer.BluetoothDevices
import com.bukuwarung.bluetooth_printer.activities.print.setup.SetupBluetoothDeviceActivity
import com.bukuwarung.edc.homepage.data.model.HomePageBody
import com.bukuwarung.edc.homepage.ui.home.HomePageActivity
import com.bukuwarung.edc.util.EncryptedPreferencesHelper
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.put
import com.bukuwarung.edc.webview.WebviewActivity
import com.bukuwarung.edc.webview.constant.ClassConstants
import com.google.firebase.crashlytics.FirebaseCrashlytics
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class BTRouterActivity : AppCompatActivity() {

    companion object {
        const val CONTENTS = "contents"
        const val TYPE = "type"
        const val URL = "url"
        const val MWEB = "mweb"
        const val APP = "app"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val type = intent?.getStringExtra(TYPE)
        val redirection = intent?.getStringExtra(URL)

        // type -> mweb or app

        when (type) {
            MWEB -> {
                openActivity(WebviewActivity::class.java) {
                    putString(ClassConstants.WEBVIEW_URL, redirection)
                    putBoolean(ClassConstants.HIDE_TOOLBAR, true)
                }
                finish()
            }

            APP -> {
                handleAppActivity(redirection)
            }
        }
    }

    private fun handleAppActivity(redirection: String?) {
        val bodyContents = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            intent?.getParcelableExtra(CONTENTS, HomePageBody::class.java)
        } else {
            intent?.getParcelableExtra(CONTENTS)
        }
        if (redirection.isNullOrEmpty()) {
            finish()
            return
        }
        val params = redirection.split("?")
        var className = params.get(0)

        val bundle = Bundle()
        if (params.size > 1) {
            for (i in 1 until params.size) {
                val args = params[i].split("=")
                bundle.putString(args[0], args[1])
            }
        }

        Utils.sharedPreferences.put("hasCheckedBT", false)
        Utils.sharedPreferences.put("edcConnected", false)
        if (Utils.isCardReader()) {
            className = className
                .replace(
                    "CardReaderInstructionActivity",
                    "BTCardReaderInstructionActivity"
                )
        }
        // Including HistoryActivity in the list as we don't need to connect Saku in case of multiple devices.
        val excludedClassNames = arrayListOf(
            "CardTransactionHistoryActivity",
            "SettingActivity",
            "DeviceListActivity",
            "HistoryActivity"
        )
        Log.d("--->classNameBT", className)
        Log.d("--->bundleBT", bundle.toString())
        Log.d("--->contentsBT", bodyContents.toString())
        val featureRequiresPaymentAccountId = !excludedClassNames.any { className.contains(it) }
        // Not activated edc saku for 1st time show activation bottom sheet and then redirect to SetupBluetoothDeviceActivity
        if (Utils.isDeviceRegisteredForFirstTime() && featureRequiresPaymentAccountId) {
            // if no device is registered, redirect user to setupBluetooth card reader device
            // open activation bottom sheet
            className =
                "com.bukuwarung.bluetooth_printer.activities.print.setup.SetupBluetoothDeviceActivity"
            val arguments = Bundle()
            arguments.apply {
                putAll(bundle)
                putString("className", className)
                putParcelable(CONTENTS, bodyContents)
                putBoolean("SHOW_RED_DOT", intent?.getBooleanExtra("SHOW_RED_DOT", false).isTrue)
            }
            val intent = Intent(this, HomePageActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP)
            intent.putExtra("IS_ACTIVATED", false)
            intent.putExtras(arguments)
            startActivity(intent)
            finish()
        } else {
            if (Utils.isMultipleCardReaderDeviceRegistered()) {
                // in case of multiple devices it's important for user to connect with bluetooth device at least once
                if (Utils.getPaymentAccountId().isNullOrEmpty()) {
                    // if class is not part of excludedClassNames it requires payment account id
                    excludedClassNames.add("BTCardReaderInstructionActivity")
                    val featureRequiresPaymentAccountId =
                        !excludedClassNames.any { className.contains(it) }
                    // if no pairedCard reader and feature requires payment accountId, redirect user to setupBluetooth card reader device
                    if (!BluetoothDevices.hasPairedCardReader() &&
                        featureRequiresPaymentAccountId
                    ) {
                        val device = Utils.getUserRegisteredDevices()
                        val deviceSerialNumberList = device.map { it.serialNumber }
                        bundle.putString(
                            SetupBluetoothDeviceActivity.DEVICE_TYPE,
                            SetupBluetoothDeviceActivity.CARD_READER
                        )
                        bundle.putStringArrayList(
                            SetupBluetoothDeviceActivity.DEVICE_SN_LIST,
                            ArrayList(deviceSerialNumberList)
                        )
                        className =
                            "com.bukuwarung.bluetooth_printer.activities.print.setup.SetupBluetoothDeviceActivity"
                    } else if (BluetoothDevices.hasPairedCardReader()) {
                        val connectedDeviceItem =
                            BluetoothDevices.getPairedCardReaderList()?.get(0)?.name?.let { it1 ->
                                Utils.findConnectedDeviceItem(
                                    it1,
                                    Utils.getUserRegisteredDevices()
                                )
                            }
                        Utils.sharedPreferences.apply {
                            if (connectedDeviceItem != null) {
                                put("t_id", connectedDeviceItem.terminalId)
                                put("serial_number", connectedDeviceItem.serialNumber)
                                put("hardware_serial_number", connectedDeviceItem.serialNumber)
                                EncryptedPreferencesHelper.put(
                                    "payment_account_id",
                                    connectedDeviceItem.paymentAccountId.orEmpty()
                                )
                            }
                        }
                    }
                }
                // in case of multiple card reader devices, history should show list of devices for selection before fetching transaction
                if (className.contains("CardTransactionHistoryActivity") &&
                    !params.contains("history_type=order")
                ) {
                    className = className.replace(
                        "com.bukuwarung.edc.card.cardhistory.ui.CardTransactionHistoryActivity",
                        "com.bukuwarung.edc.card.ui.edcdevices.ui.DeviceListActivity"
                    )
                }
            }
            try {
                openActivity(Class.forName(className)) {
                    putAll(bundle)
                    putParcelable(CONTENTS, bodyContents)
                    putBoolean(
                        "SHOW_RED_DOT",
                        intent?.getBooleanExtra("SHOW_RED_DOT", false).isTrue
                    )
                }
                finish()
            } catch (e: Exception) {
                FirebaseCrashlytics.getInstance().recordException(e)
            }
        }
    }
}
