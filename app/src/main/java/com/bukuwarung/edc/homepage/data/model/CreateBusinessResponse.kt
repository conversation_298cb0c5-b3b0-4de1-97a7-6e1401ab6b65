package com.bukuwarung.edc.homepage.data.model

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class CreateBusinessResponse(
    @SerializedName("result")
    val result: Boolean? = null,
    @SerializedName("data")
    val data: BusinessData? = null
)

@Keep
data class BusinessData(
    @SerializedName("business") val business: Business? = null,
    @SerializedName("businessCategory") val businessCategory: BusinessCategory? = null,
    @SerializedName("profileCompletionProgress") val profileCompletionProgress: Double? = null
)

@Keep
data class BusinessCategory(
    @SerializedName("id") val id: Int? = null,
    @SerializedName("categoryId") val categoryId: String? = null,
    @SerializedName("userId") val userId: String? = null,
    @SerializedName("name") val name: String? = null,
    @SerializedName("displayOrderNo") val displayOrderNo: Int? = null,
    @SerializedName("displayNameId") val displayNameId: String? = null,
    @SerializedName("imageUrl") val imageUrl: String? = null,
    @SerializedName("deleted") val deleted: Boolean? = null,
    @SerializedName("createdAt") val createdAt: String? = null,
    @SerializedName("lastModifiedAt") val lastModifiedAt: String? = null
)
