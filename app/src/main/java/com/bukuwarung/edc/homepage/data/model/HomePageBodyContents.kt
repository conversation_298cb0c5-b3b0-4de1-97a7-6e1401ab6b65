package com.bukuwarung.edc.homepage.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class HomePageBodyContents(
    @SerializedName("display_name")
    val displayName: String?,
    @SerializedName("analytics_name")
    val analyticsName: String?,
    @SerializedName("show_on_verifone")
    val shouldShowOnVerifone: Boolean = true,
    @SerializedName("show_on_pax")
    val shouldShowOnPax: Boolean = true,
    @SerializedName("icon")
    val icon: String?,
    @SerializedName("rank")
    val rank: Int = 1,
    @SerializedName("is_visible")
    val isVisible: Boolean = false,
    @SerializedName("start_version")
    val startVersion: Int = 0,
    @SerializedName("end_version")
    val endVersion: Int = -1,
    @SerializedName("deeplink_type")
    val deeplinkType: String = "app",
    @SerializedName("deeplink_url")
    val deeplinkUrl: String?,
    @SerializedName("is_new")
    val isNew: Boolean = false,
    @SerializedName("check_for_whitelisting")
    val checkForWhitelisting: Boolean = false,
    @SerializedName("check_if_registered")
    val checkIfRegistered: Boolean = false
) : Parcelable
