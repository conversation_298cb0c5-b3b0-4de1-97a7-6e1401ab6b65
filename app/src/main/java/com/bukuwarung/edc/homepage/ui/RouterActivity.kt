package com.bukuwarung.edc.homepage.ui

import android.os.Build
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.bukuwarung.edc.homepage.data.model.HomePageBody
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.webview.WebviewActivity
import com.bukuwarung.edc.webview.constant.ClassConstants
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class RouterActivity : AppCompatActivity() {

    companion object {
        private const val CONTENTS = "contents"
        const val TYPE = "type"
        const val URL = "url"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val type = intent?.getStringExtra("type")
        val redirection = intent?.getStringExtra("url")

        // type -> mweb or app

        when (type) {
            "mweb" -> {
                openActivity(WebviewActivity::class.java) {
                    putString(ClassConstants.WEBVIEW_URL, redirection)
                }
                finish()
            }

            "app" -> {
                handleAppActivity(redirection)
            }
        }
    }

    private fun handleAppActivity(redirection: String?) {
        val bodyContents = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            intent?.getParcelableExtra(CONTENTS, HomePageBody::class.java)
        } else {
            intent?.getParcelableExtra(CONTENTS)
        }
        if (redirection.isNullOrEmpty()) {
            finish()
            return
        }
        val params = redirection.split("?")
        var className = params.get(0)

        val bundle = Bundle()
        if (params.size > 1) {
            for (i in 1 until params.size) {
                val args = params[i].split("=")
                bundle.putString(args[0], args[1])
            }
        }

        if (Utils.isMultipleCardReaderDeviceRegistered()) {
            className = className
                .replace(
                    "com.bukuwarung.edc.card.cardhistory.ui.CardTransactionHistoryActivity",
                    "com.bukuwarung.edc.card.ui.edcdevices.ui.DeviceListActivity"
                )
        }

        openActivity(Class.forName(className)) {
            putAll(bundle)
            putParcelable(CONTENTS, bodyContents)
        }
        finish()
    }
}
