package com.bukuwarung.edc.homepage.data.model

import com.google.gson.annotations.SerializedName

data class EdcLeaderboardConfigContents(
    @SerializedName("campaign_name")
    val campaignName: String? = null,
    @SerializedName("banner_url")
    val bannerUrl: String? = null,
    @SerializedName("redirection_url")
    val redirectionUrl: String? = null,
    @SerializedName("is_validate_from_backend")
    val isValidateFromBackend: Boolean? = false,
    @SerializedName("hide_banner")
    val hideBanner: Boolean? = true
)
