package com.bukuwarung.edc.homepage.ui.history

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.edc.databinding.HistoryItemBinding
import com.bukuwarung.edc.homepage.ui.tile.model.TileData
import com.bukuwarung.edc.util.singleClick
import com.bumptech.glide.Glide

class HistoryAdapter(private val list: List<TileData>, private val callback: Callback) :
    RecyclerView.Adapter<HistoryAdapter.HistoryViewHolder>() {
    interface Callback {
        fun buttonClicked(tile: TileData)
    }

    inner class HistoryViewHolder(private val binding: HistoryItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(item: TileData) = with(binding) {
            val context = root.context
            Glide.with(context)
                .load(item.image)
                .into(ivHistoryTransactions)
            root.singleClick { callback.buttonClicked(item) }
            tvHistoryTransactions.text = item.text
            binding.viewRedDot.visibility = if (item.isNew == true) View.VISIBLE else View.GONE
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): HistoryViewHolder {
        val itemBinding =
            HistoryItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return HistoryViewHolder(itemBinding)
    }

    override fun getItemCount() = list.size

    override fun onBindViewHolder(holder: HistoryViewHolder, position: Int) {
        holder.bind(list[position])
    }
}
