package com.bukuwarung.edc.homepage.ui.saldo

import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.GridLayoutManager
import com.bukuwarung.edc.BuildConfig
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.FragmentSaldoHomepageBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.global.network.Status
import com.bukuwarung.edc.homepage.constant.HomePageAnalyticsConstants
import com.bukuwarung.edc.homepage.constant.HomePageRemoteConfig
import com.bukuwarung.edc.homepage.data.model.HomePageBody
import com.bukuwarung.edc.homepage.ui.home.HomePageViewModel
import com.bukuwarung.edc.homepage.ui.tile.model.TileData
import com.bukuwarung.edc.payments.ui.saldo.SaldoViewModel
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.isNotNullOrBlank
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.setDrawable
import com.bukuwarung.edc.util.setDrawableRightListener
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick
import com.bukuwarung.edc.webview.WebviewActivity
import com.bukuwarung.edc.webview.constant.ClassConstants
import com.bukuwarung.network.utils.ResourceState
import com.bumptech.glide.Glide
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class HomeSaldoFragment @Inject constructor() :
    Fragment(),
    HomePageSaldoAdapter.Callback {

    private var onItemClickListener: ((type: String, url: String) -> Unit)? = null

    companion object {
        private const val CONTENTS = "contents"
        private const val NO_OF_BUTTONS_PER_ROW = 2

        fun newInstance(
            bodyContents: HomePageBody?,
            onItemClickListener: ((type: String, url: String) -> Unit)?
        ): HomeSaldoFragment {
            val fragment = HomeSaldoFragment()
            fragment.arguments = Bundle().apply {
                putParcelable("contents", bodyContents)
            }
            fragment.onItemClickListener = onItemClickListener
            return fragment
        }
    }

    lateinit var binding: FragmentSaldoHomepageBinding
    private val versionCode by lazy { BuildConfig.VERSION_CODE }
    private var adapter: HomePageSaldoAdapter? = null

    private val viewModel: SaldoViewModel by activityViewModels()
    private val homepageViewModel: HomePageViewModel by activityViewModels()
    private var showPassword = false
    private var amount: Double? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentSaldoHomepageBinding.inflate(layoutInflater, container, false)

        observeData()
        initialSetup()
        return binding.root
    }

    private fun initialSetup() = with(binding) {
        tvSaldoError.setDrawableRightListener { viewModel.apply { checkSaldoBalance() } }
        tvSaldoTitle.setDrawableRightListener {
            tvSaldoTitle.setDrawable(
                right = if (showPassword) R.drawable.ic_eye_open else R.drawable.ic_eye_close,
                left = R.drawable.vector_saldo_icon
            )
            etSaldoAmount.setText(
                if (showPassword) {
                    Utils.formatAmountMasked(amount)
                } else {
                    Utils.formatAmount(amount)
                }
            )
            showPassword = !showPassword
        }

        val bodyContent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            arguments?.getParcelable(CONTENTS, HomePageBody::class.java)
        } else {
            arguments?.getParcelable(CONTENTS)
        }
        tvTitle.text = bodyContent?.bodyTitle
        tvTitle.visibility = bodyContent?.bodyTitle.isNotNullOrBlank().asVisibility()
        rvItems.layoutManager =
            GridLayoutManager(requireContext(), bodyContent?.noOfColumns ?: NO_OF_BUTTONS_PER_ROW)
        var saldoContent = bodyContent?.bodyContent
        saldoContent = saldoContent?.filter {
            it?.isVisible!! && (
                it.endVersion >= versionCode ||
                    it.endVersion == -1
                ) && it.startVersion <= versionCode
        }?.sortedBy { it?.rank }

        val tiles: MutableList<TileData> = mutableListOf()

        if (saldoContent != null) {
            for (tile in saldoContent) {
                tiles.add(
                    TileData(
                        tile?.deeplinkType,
                        tile?.deeplinkUrl,
                        tile?.icon,
                        tile?.displayName,
                        tile?.analyticsName
                    )
                )
            }
        }
        adapter = HomePageSaldoAdapter(tiles, this@HomeSaldoFragment)
        rvItems.adapter = adapter
    }

    override fun onResume() {
        super.onResume()
        viewModel.init()
    }

    private fun observeData() {
        homepageViewModel.userWhitelistedForLeaderboard.observe(viewLifecycleOwner) {
            when {
                it.status == Status.SUCCESS && it.data?.data?.user?.isWhitelisted.isTrue -> {
                    with(binding.ivLeaderboard) {
                        showView()
                        Glide.with(context)
                            .load(HomePageRemoteConfig.getLeaderboardInfo().bannerUrl)
                            .into(this)
                        singleClick {
                            context.openActivity(WebviewActivity::class.java) {
                                putString(
                                    ClassConstants.WEBVIEW_URL,
                                    HomePageRemoteConfig.getLeaderboardInfo().redirectionUrl
                                )
                            }
                        }
                    }
                }

                else -> {
                    binding.ivLeaderboard.hideView()
                }
            }
        }
        viewModel.saldo.observe(viewLifecycleOwner) {
            when (it) {
                is ResourceState.Loading -> {
                }

                is ResourceState.Success -> {
                    it.data.let { saldo ->
                        if (saldo.limit != null) {
                            amount = saldo.amount
                            binding.etSaldoAmount.setText(Utils.formatAmountMasked(saldo.amount))
                            showErrorView(false)
                        } else {
                            showErrorView(true)
                        }
                    }
                }

                is ResourceState.Failure -> {
                    showErrorView(true)
                }
            }
        }
    }

    private fun showErrorView(isError: Boolean) = with(binding) {
        tvSaldoError.visibility = isError.asVisibility()
        etSaldoAmount.visibility = (!isError).asVisibility()
    }

    override fun buttonClicked(tile: TileData) {
        val map = HashMap<String, String>()
        if (tile.analyticsName != null) {
            map[HomePageAnalyticsConstants.BUTTON_NAME] = tile.analyticsName
            Analytics.trackEvent(HomePageAnalyticsConstants.HOMEPAGE_BUTTON_CLICK, map)
        } else {
            Analytics.trackEvent(HomePageAnalyticsConstants.HOMEPAGE_BUTTON_CLICK)
        }
        onItemClickListener?.invoke(tile.type.orEmpty(), tile.deeplink.orEmpty())
    }
}
