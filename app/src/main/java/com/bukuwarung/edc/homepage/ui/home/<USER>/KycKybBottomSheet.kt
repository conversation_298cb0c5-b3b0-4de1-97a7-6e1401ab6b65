package com.bukuwarung.edc.homepage.ui.home.bottomsheet

import android.content.res.Resources
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.KycKybBottomSheetBinding
import com.bukuwarung.edc.payments.constant.PaymentRemoteConfig
import com.bukuwarung.edc.util.getDrawableCompat
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.singleClick
import com.bukuwarung.edc.webview.WebviewActivity
import com.bukuwarung.edc.webview.constant.ClassConstants
import com.bukuwarung.lib.webview.bottomsheet.BaseBottomSheetDialogFragment
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class KycKybBottomSheet : BaseBottomSheetDialogFragment() {

    companion object {

        fun createInstance() = KycKybBottomSheet().apply {
            val bundle = Bundle().apply {}
            arguments = bundle
        }
    }

    private var _binding: KycKybBottomSheetBinding? = null
    private val binding get() = _binding!!

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(DialogFragment.STYLE_NO_FRAME, R.style.BottomSheetDialogTheme)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = KycKybBottomSheetBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        dialog?.setOnShowListener {
            val dialog = it as BottomSheetDialog
            val bottomSheet =
                dialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
            bottomSheet?.let { _ ->
                dialog.behavior.state = BottomSheetBehavior.STATE_EXPANDED
                dialog.behavior.peekHeight = Resources.getSystem().displayMetrics.heightPixels - 100
            }
        }

        with(binding) {
            layoutSaku.ivImage.setImageDrawable(
                requireContext().getDrawableCompat(R.drawable.ic_saku_verify)
            )
            layoutSaku.tvTitle.text = getString(R.string.edc_android_saku)

            layoutPayment.ivImage.setImageDrawable(
                requireContext().getDrawableCompat(R.drawable.ic_payment_verify)
            )
            layoutPayment.tvTitle.text = getString(R.string.edc_payment)

            layoutSaldo.ivImage.setImageDrawable(
                requireContext().getDrawableCompat(R.drawable.ic_saldo_verify)
            )
            layoutSaldo.tvTitle.text = getString(R.string.edc_saldo)

            layoutPpob.ivImage.setImageDrawable(
                requireContext().getDrawableCompat(R.drawable.ic_ppob_verify)
            )
            layoutPpob.tvTitle.text = getString(R.string.edc_ppob)

            ivClose.singleClick {
                dismiss()
            }

            btnVerify.singleClick {
                dismiss()
                requireContext().openActivity(WebviewActivity::class.java) {
                    putString(
                        ClassConstants.WEBVIEW_URL,
                        PaymentRemoteConfig.getPaymentConfigs().kycKybVerificationUrl
                    )
                    putBoolean(ClassConstants.HIDE_TOOLBAR, true)
                }
            }
        }
    }
}
