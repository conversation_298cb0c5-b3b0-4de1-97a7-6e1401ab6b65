package com.bukuwarung.edc.homepage.data.repository

import com.bukuwarung.edc.homepage.data.datasource.HomePageDataSource
import com.bukuwarung.edc.homepage.data.model.BusinessData
import com.bukuwarung.edc.homepage.data.model.LogonDataRequest
import com.bukuwarung.edc.login.data.model.UserDetail
import com.bukuwarung.network.utils.ResourceState
import com.bukuwarung.network.utils.safeApiCall
import com.bukuwarung.network.utils.toResourceState
import javax.inject.Inject

class HomePageRepository @Inject constructor(private val homePageDataSource: HomePageDataSource) {

    suspend fun createBusiness(request: BusinessData?) =
        safeApiCall { homePageDataSource.createBusiness(request) }

    suspend fun getBookList() = safeApiCall { homePageDataSource.getBookList() }

    suspend fun fetchUserDetails(serialNumber: String?): ResourceState<UserDetail> =
        homePageDataSource.fetchUserDetails(serialNumber).toResourceState()

    suspend fun fetchLogonData(
        serialNumber: String,
        accountId: String,
        logonDataRequest: LogonDataRequest
    ) = safeApiCall { homePageDataSource.fetchLogonData(serialNumber, accountId, logonDataRequest) }

    suspend fun userWhitelistedForLeaderboard(campaign: String, checkWhitelist: Boolean) =
        safeApiCall { homePageDataSource.userWhitelistedForLeaderboard(campaign, checkWhitelist) }

    suspend fun getDevices(devicePlan: String) =
        safeApiCall { homePageDataSource.getDeviceList(devicePlan) }
}
