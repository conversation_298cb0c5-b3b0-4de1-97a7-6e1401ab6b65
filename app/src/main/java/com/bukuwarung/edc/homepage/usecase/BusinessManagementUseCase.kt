package com.bukuwarung.edc.homepage.usecase

import com.bukuwarung.analytic.BtAnalytics.bwLog
import com.bukuwarung.edc.homepage.data.model.Business
import com.bukuwarung.edc.homepage.data.model.BusinessCategory
import com.bukuwarung.edc.homepage.data.model.BusinessData
import com.bukuwarung.edc.login.usecase.EnablePaymentUseCase
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.put
import com.bukuwarung.network.utils.ResourceState
import javax.inject.Inject

class BusinessManagementUseCase @Inject constructor(
    private val homePageUseCase: HomePageUseCase,
    private val enablePaymentUseCase: EnablePaymentUseCase
) {

    suspend fun fetchExistingBusiness(): ResourceState<String> {
        try {
            homePageUseCase.getBookList().let {
                when (it) {
                    is ResourceState.Loading -> {
                        return ResourceState.Loading()
                    }

                    is ResourceState.Failure -> {
                        val errorMessage = "Error-fetchExistingBusiness(): ${it.message}"
                        bwLog(errorMessage)
                        return ResourceState.Failure(
                            it.throwable,
                            it.responseCode,
                            it.message,
                            it.errorJsonObj
                        )
                    }

                    is ResourceState.Success -> {
                        if (it.data.result.isTrue) {
                            if (it.data.data?.isEmpty() == true) {
                                return createAccount()
                            } else {
                                val business = it.data.data?.get(0)?.business
                                Utils.setPaymentAccountId(business?.businessId.orEmpty())
                                Utils.sharedPreferences.put("store_name", business?.name.orEmpty())
                                return ResourceState.Success(
                                    "Business found and configured successfully"
                                )
                            }
                        } else {
                            return createAccount()
                        }
                    }
                }
            }
        } catch (e: Exception) {
            val errorMessage = "Exception in fetchExistingBusiness: ${e.localizedMessage}"
            bwLog(e = e)
            return ResourceState.Failure(e, 0, errorMessage, null)
        }
    }

    private fun createMiniAtmProBusinessCategory(): BusinessCategory =
        BusinessCategory(categoryId = "36", userId = Utils.getPhoneNumber(), name = "Lainnya")

    private suspend fun createAccount(): ResourceState<String> {
        try {
            val business = Business(
                name = "MINIATM PRO EDC",
                ownerName = "MINIATMPRO",
                categoryId = "36",
                businessId = null,
                userId = Utils.getPhoneNumber()
            )
            val businessCategory = createMiniAtmProBusinessCategory()
            val request = BusinessData(business = business, businessCategory = businessCategory)
            homePageUseCase.createBusiness(request).let {
                when (it) {
                    is ResourceState.Loading -> {
                        return ResourceState.Loading()
                    }

                    is ResourceState.Failure -> {
                        val errorMessage = "Error-createAccount(): ${it.message}"
                        bwLog(errorMessage)
                        return ResourceState.Failure(
                            it.throwable,
                            it.responseCode,
                            it.message,
                            it.errorJsonObj
                        )
                    }

                    is ResourceState.Success -> {
                        if (it.data.result.isTrue) {
                            val businessId = it.data.data?.business?.businessId.orEmpty()
                            Utils.setPaymentAccountId(businessId)
                            Utils.sharedPreferences.put("store_name", "MINIATM PRO EDC")
                            enablePaymentUseCase.enablePayment(businessId, "MINIATM PRO EDC")
                            return ResourceState.Success("Business account created successfully")
                        } else {
                            return ResourceState.Failure(
                                null,
                                0,
                                "Failed to create business account",
                                null
                            )
                        }
                    }
                }
            }
        } catch (e: Exception) {
            val errorMessage = "Exception in createAccount: ${e.localizedMessage}"
            bwLog(e = e)
            return ResourceState.Failure(e, 0, errorMessage, null)
        }
    }
}
