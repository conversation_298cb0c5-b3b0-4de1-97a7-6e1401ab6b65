package com.bukuwarung.edc.homepage.ui.order

import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.bukuwarung.edc.databinding.FragmentEdcOrderBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.homepage.data.model.HomePageBody
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.singleClick
import com.bukuwarung.edc.webview.WebviewActivity
import com.bukuwarung.edc.webview.constant.ClassConstants.HIDE_TOOLBAR
import com.bukuwarung.edc.webview.constant.ClassConstants.WEBVIEW_URL

class EdcOrderFragment : Fragment() {

    companion object {
        private const val CONTENTS = "contents"
    }

    private lateinit var binding: FragmentEdcOrderBinding
    private var homePageBody: HomePageBody? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentEdcOrderBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        homePageBody = if (Build.VERSION.SDK_INT >= 33) {
            arguments?.getParcelable(CONTENTS, HomePageBody::class.java)
        } else {
            arguments?.getParcelable(CONTENTS)
        }
        binding.tvTitle.text = homePageBody?.bodyTitle
        binding.tvDescription.text = homePageBody?.bodySubtitle
        binding.root.singleClick {
            requireActivity().openActivity(WebviewActivity::class.java) {
                putString(
                    WEBVIEW_URL,
                    homePageBody?.deepLinkUrl
                )
                putBoolean(HIDE_TOOLBAR, true)
            }
            Analytics.trackTikTokStandardEvents("ViewContent")
        }
    }
}
