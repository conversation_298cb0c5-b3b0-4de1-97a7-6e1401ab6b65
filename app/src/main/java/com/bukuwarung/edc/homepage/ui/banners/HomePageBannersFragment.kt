package com.bukuwarung.edc.homepage.ui.banners

import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.viewpager.widget.ViewPager
import androidx.viewpager2.widget.ViewPager2
import com.bukuwarung.edc.BuildConfig
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.LayoutHomePageBannersBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.homepage.constant.HomePageAnalyticsConstants.BANNER_NAME
import com.bukuwarung.edc.homepage.constant.HomePageAnalyticsConstants.BANNER_ORDER
import com.bukuwarung.edc.homepage.constant.HomePageAnalyticsConstants.EVENT_HOMEPAGE_BANNER_CLICK
import com.bukuwarung.edc.homepage.data.model.HomePageBody
import com.bukuwarung.edc.homepage.data.model.HomePageBodyContents
import com.bukuwarung.edc.homepage.ui.RouterActivity
import com.bukuwarung.edc.homepage.ui.tile.model.TileData
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.getScreenWidth
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.isNotNullOrBlank
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.orNil
import com.google.android.material.tabs.TabLayoutMediator
import java.util.*

class HomePageBannersFragment : Fragment() {

    companion object {
        private const val CONTENTS = "contents"
    }

    private lateinit var binding: LayoutHomePageBannersBinding
    val handler = Handler()
    var runnable: Runnable? = null
    private var scrollStarted = false
    private var checkDirection: Boolean = false
    private var tiles: List<HomePageBodyContents?>? = null
    private var homePageBannersAdapter: HomePageBannersAdapter? = null
    private var homePageBody: HomePageBody? = null
    private val versionCode = BuildConfig.VERSION_CODE
    private var isManualClick: Boolean = false

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding =
            LayoutHomePageBannersBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        homePageBody = if (Build.VERSION.SDK_INT >= 33) {
            arguments?.getParcelable(CONTENTS, HomePageBody::class.java)
        } else {
            arguments?.getParcelable(CONTENTS)
        }
        with(binding) {
            tvTitle.text = homePageBody?.bodyTitle.orEmpty()
            tvTitle.visibility = homePageBody?.bodyTitle.isNotNullOrBlank().asVisibility()
            tvSubtitle.text = homePageBody?.bodySubtitle.orEmpty()
            tvSubtitle.visibility = homePageBody?.bodySubtitle.isNotNullOrBlank().asVisibility()
        }
        tiles = homePageBody?.bodyContent

        tiles = tiles?.filter {
            it?.isVisible.isTrue && (
                it?.endVersion.orNil >= versionCode ||
                    it?.endVersion.orNil == -1
                ) && it?.startVersion.orNil <= versionCode
        }?.sortedBy { it?.rank }
        prepareBanners(tiles)
    }

    private fun prepareBanners(listOfTiles: List<HomePageBodyContents?>?) = with(binding) {
        val tiles: MutableList<TileData> = mutableListOf()
        if (listOfTiles != null) {
            for (tile in listOfTiles) {
                tiles.add(
                    TileData(
                        type = tile?.deeplinkType,
                        deeplink = tile?.deeplinkUrl,
                        image = tile?.icon,
                        text = tile?.displayName,
                        analyticsName = tile?.analyticsName
                    )
                )
            }
        }

        if (tiles.isNotEmpty()) {
            vpPaymentBanner.isNestedScrollingEnabled = false
            vpPaymentBanner.apply {
                homePageBannersAdapter = HomePageBannersAdapter(
                    tiles,
                    requireContext()
                ) { tileData, position ->
                    run {
                        handleAnalytics(tileData, position)
                        requireContext().openActivity(Utils.getRouterClass()) {
                            putString(RouterActivity.TYPE, tileData?.type)
                            putString(RouterActivity.URL, tileData?.deeplink)
                        }
                    }
                }
                vpPaymentBanner.adapter = homePageBannersAdapter
            }
            TabLayoutMediator(
                binding.tbPaymentBanner,
                binding.vpPaymentBanner
            ) { tab, position ->
            }.attach()
        } else {
            tbPaymentBanner.hideView()
            vpPaymentBanner.hideView()
        }
        vpPaymentBanner.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                runnable = Runnable {
                    binding.vpPaymentBanner.currentItem =
                        (position + 1) % homePageBannersAdapter?.itemCount.orNil
                }
                if (homePageBody?.autoRotateBanners.isTrue) {
                    runnable?.let {
                        handler.postDelayed(it, homePageBody?.bannerDuration.orNil)
                    }
                }
            }

            override fun onPageScrollStateChanged(state: Int) {
                super.onPageScrollStateChanged(state)
                if (state == ViewPager2.SCROLL_STATE_DRAGGING) {
                    handler.removeMessages(0)
                }
                if (!scrollStarted && state == ViewPager.SCROLL_STATE_DRAGGING) {
                    scrollStarted = true
                    checkDirection = true
                } else {
                    scrollStarted = false
                }
            }

            override fun onPageScrolled(
                position: Int,
                positionOffset: Float,
                positionOffsetPixels: Int
            ) {
                if (checkDirection) {
                    isManualClick = true
                    checkDirection = false
                } else {
                    isManualClick = false
                }
            }
        })

        binding.vpPaymentBanner.offscreenPageLimit = 1
        val nextItemVisiblePx = resources.getDimension(R.dimen._8dp)
        val currentItemHorizontalMarginPx = resources.getDimension(R.dimen._26dp)
        val pageTranslationX = nextItemVisiblePx + currentItemHorizontalMarginPx
        val pageTransformer = ViewPager2.PageTransformer { page, position ->
            page.translationX = -pageTranslationX * position
        }
        binding.vpPaymentBanner.setPageTransformer(pageTransformer)
        context?.let {
            val itemDecoration = HorizontalMarginItemDecoration(
                it,
                R.dimen._6dp
            )
            binding.vpPaymentBanner.addItemDecoration(itemDecoration)
        }
        binding.vpPaymentBanner.apply {
            layoutParams.height = ((activity?.let { getScreenWidth(it) } ?: 0) * 0.22).toInt()
        }
    }

    override fun onDestroyView() {
        runnable?.let(handler::removeCallbacks)
        runnable = null
        super.onDestroyView()
    }

    private fun handleAnalytics(fragmentBody: TileData?, position: Int) {
        val map = HashMap<String, String>()
        map[BANNER_NAME] = fragmentBody?.analyticsName.orEmpty()
        map[BANNER_ORDER] = (position + 1).toString()
        map["banner_shown_automatically"] = isManualClick.toString()
        Analytics.trackEvent(EVENT_HOMEPAGE_BANNER_CLICK, map)
    }
}
