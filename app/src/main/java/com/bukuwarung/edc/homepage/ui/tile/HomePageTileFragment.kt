package com.bukuwarung.edc.homepage.ui.tile

import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.GridLayoutManager
import com.bukuwarung.bluetooth_printer.BluetoothDevices
import com.bukuwarung.cardreader.CardReaderHelper
import com.bukuwarung.edc.BuildConfig
import com.bukuwarung.edc.R
import com.bukuwarung.edc.app.config.AppConfig
import com.bukuwarung.edc.app.config.VariantConfig
import com.bukuwarung.edc.card.activation.constant.CardActivationConstants
import com.bukuwarung.edc.card.cardhistory.ui.CardTransactionHistoryActivity
import com.bukuwarung.edc.card.cardhistory.ui.CardTransactionHistoryActivity.Companion.HISTORY_TYPE
import com.bukuwarung.edc.databinding.FragmentTileHomepageBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.global.network.Status
import com.bukuwarung.edc.homepage.constant.HomePageAnalyticsConstants
import com.bukuwarung.edc.homepage.constant.HomePageRemoteConfig
import com.bukuwarung.edc.homepage.data.model.HomePageBody
import com.bukuwarung.edc.homepage.data.model.HomePageBodyContents
import com.bukuwarung.edc.homepage.ui.home.HomePageActivity
import com.bukuwarung.edc.homepage.ui.home.HomePageActivity.Companion.EDC_MINIATMPRO_HOMEPAGE_CARD
import com.bukuwarung.edc.homepage.ui.home.HomePageActivity.Companion.EDC_SAKU_HOMEPAGE_CARD
import com.bukuwarung.edc.homepage.ui.home.HomePageViewModel
import com.bukuwarung.edc.homepage.ui.tile.model.TileData
import com.bukuwarung.edc.order.orderdetail.ui.EdcOrderDetailsActivity
import com.bukuwarung.edc.order.orderhistory.enums.EdcOrderStatus
import com.bukuwarung.edc.order.orderhistory.enums.HistoryType
import com.bukuwarung.edc.payments.constant.PaymentConst.STATUS_PAID
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.getDrawableCompat
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.isFalseOrNull
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick
import com.google.firebase.crashlytics.FirebaseCrashlytics
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class HomePageTileFragment @Inject constructor() : Fragment() {

    private val pageRequiresTerminal = "com.bukuwarung.edc.card.ui.CardReaderInstructionActivity"
    lateinit var binding: FragmentTileHomepageBinding
    private val versionCode by lazy { BuildConfig.VERSION_CODE }
    private val tileAdapter by lazy { HomePageTileAdapter() }
    private val homepageViewModel: HomePageViewModel by activityViewModels()
    @Inject
    lateinit var variantConfig: VariantConfig
    private var showLeaderboardIcon = false
    private var tiles: List<HomePageBodyContents?>? = null
    private var bodyContent: HomePageBody? = null
    private var bodyName: String? = null
    private var onItemClickListener: (
        (bodyName: String?, type: String, url: String, checkIfRegistered: Boolean) -> Unit
    )? =
        null

    companion object {
        fun newInstance(
            bodyName: String?,
            bodyContents: HomePageBody?,
            onItemClickListener: (
                bodyName: String?,
                type: String,
                url: String,
                checkIfRegistered: Boolean
            ) -> Unit
        ): HomePageTileFragment {
            val fragment = HomePageTileFragment()
            fragment.bodyName = bodyName
            fragment.arguments = Bundle().apply {
                putParcelable("contents", bodyContents)
            }
            fragment.onItemClickListener = onItemClickListener
            return fragment
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentTileHomepageBinding.inflate(layoutInflater, container, false)

        bodyContent = if (Build.VERSION.SDK_INT >= 33) {
            arguments?.getParcelable("contents", HomePageBody::class.java)
        } else {
            arguments?.getParcelable("contents")
        }

        setValues(bodyContent)
        observeDate(bodyContent)

        return binding.root
    }

    private fun observeDate(homePageBody: HomePageBody?) {
        homepageViewModel.userWhitelistedForLeaderboard.observe(viewLifecycleOwner) {
            when {
                it.status == Status.SUCCESS && it.data?.data?.user?.isWhitelisted.isTrue -> {
                    showLeaderboardIcon = true
                    prepareAdapter(tiles)
                }

                else -> {}
            }
        }
        // get orders for which KYC,KYB is not done.
        homepageViewModel.orderDetails.observe(viewLifecycleOwner) {
            if (variantConfig.variantIdentifier == "MINIATMPRO") {
                val status = it?.data?.status
                val isCardSection = homePageBody?.bodyName.equals(
                    EDC_MINIATMPRO_HOMEPAGE_CARD,
                    ignoreCase = true
                ) || homePageBody?.bodyName.equals(EDC_MINIATMPRO_HOMEPAGE_CARD, ignoreCase = true)
                if (isCardSection && status?.equals(
                        EdcOrderStatus.WAITING_FOR_ADDITIONAL_DOC.name,
                        true
                    ).isTrue
                ) {
                    binding.cvPendingEdcOrder.showView()
                    binding.cvPendingEdcOrder.singleClick {
                        requireContext().openActivity(EdcOrderDetailsActivity::class.java) {
                            putString(
                                EdcOrderDetailsActivity.ORDER_ID,
                                it?.data?.orderId.orEmpty()
                            )
                        }
                    }
                } else if (isCardSection && status?.equals(
                        EdcOrderStatus.WAITING_FOR_OPS.name,
                        true
                    ).isTrue
                ) {
                    binding.tvTitleDraftEdcOrder.text =
                        getString(R.string.come_on_activate_the_device)
                    binding.cvPendingEdcOrder.showView()
                    binding.cvPendingEdcOrder.singleClick {
                        requireContext().openActivity(CardTransactionHistoryActivity::class.java) {
                            putString(HISTORY_TYPE, HistoryType.ORDER.name)
                        }
                    }
                } else {
                    binding.cvPendingEdcOrder.hideView()
                }
            } else {
                val isPaid = it?.data?.paymentDetails?.status?.equals(STATUS_PAID, true).isTrue
                val isKycDone = it?.data?.checks?.kybDone.isTrue
                val isKybDone = it?.data?.checks?.kybDone.isTrue
                val isBankAdded = it?.data?.checks?.bankAccountAdded.isTrue
                val isWaitingForOps = it?.data?.status?.equals("Waiting_for_ops", true).isTrue
                val isEdcActivationPending =
                    isPaid && isKycDone && isKybDone && isBankAdded && isWaitingForOps
                if (homePageBody?.bodyName.equals(
                        EDC_SAKU_HOMEPAGE_CARD,
                        true
                    ) && it?.result.isTrue && isPaid && (!isKybDone || !isKycDone || !isBankAdded)
                ) {
                    binding.cvPendingEdcOrder.showView()
                    binding.cvPendingEdcOrder.singleClick {
                        requireContext().openActivity(EdcOrderDetailsActivity::class.java) {
                            putString(
                                EdcOrderDetailsActivity.ORDER_ID,
                                it?.data?.orderId.orEmpty()
                            )
                        }
                    }
                } else if (homePageBody?.bodyName.equals(
                        EDC_SAKU_HOMEPAGE_CARD,
                        ignoreCase = true
                    ) && isEdcActivationPending
                ) {
                    binding.tvTitleDraftEdcOrder.text =
                        getString(R.string.come_on_activate_the_device)
                    binding.cvPendingEdcOrder.showView()
                    binding.cvPendingEdcOrder.singleClick {
                        Analytics.trackEvent(
                            CardActivationConstants.EVENT_HOMEPAGE_ACTIVATION_CTA_CLICKED
                        )
                        requireContext().openActivity(CardTransactionHistoryActivity::class.java) {
                            putString(HISTORY_TYPE, HistoryType.ORDER.name)
                        }
                    }
                } else {
                    binding.cvPendingEdcOrder.hideView()
                }
            }
        }
    }

    private fun setValues(homePageBody: HomePageBody?) {
        binding.apply {
            tvTitle.text = homePageBody?.bodyTitle
            if (!homePageBody?.bodySubtitle.isNullOrEmpty()) {
                tvSubtitle.showView()
                tvSubtitle.text = homePageBody?.bodySubtitle
            } else {
                tvSubtitle.hideView()
            }

            rvTileItems.layoutManager = GridLayoutManager(
                requireContext(),
                homePageBody?.noOfColumns ?: HomePageRemoteConfig.getNumberOfColumns().toInt()
            )

            if (Utils.isCardReader()) {
                ivPrinterState.setImageDrawable(
                    context?.getDrawableCompat(R.drawable.ic_printer_selector)
                )
                ivPrinterState.isEnabled = Utils.hasPairedPrinter()

                ivBluetoothState.setImageDrawable(
                    context?.getDrawableCompat(R.drawable.ic_bluetooth_selector)
                )
                ivBluetoothState.isEnabled = isCardReaderConnected()

                ivPrinterState.visibility =
                    connectivityCheck(homePageBody).asVisibility()
                ivBluetoothState.visibility =
                    connectivityCheck(homePageBody).asVisibility()
            } else {
                ivPrinterState.hideView()
                ivBluetoothState.hideView()
            }
        }

        tiles = homePageBody?.bodyContent

        tiles = tiles?.filter {
            it?.isVisible!! && (
                it.endVersion >= versionCode ||
                    it.endVersion == -1
                ) && it.startVersion <= versionCode
        }?.sortedBy { it?.rank }
        prepareAdapter(tiles)
    }

    private fun prepareAdapter(listOfTiles: List<HomePageBodyContents?>?) {
        val tiles: MutableList<TileData> = mutableListOf()

        if (listOfTiles != null) {
            for (tile in listOfTiles) {
                if (tile?.checkForWhitelisting.isFalseOrNull ||
                    (tile?.checkForWhitelisting.isTrue && showLeaderboardIcon)
                ) {
                    tiles.add(
                        TileData(
                            type = tile?.deeplinkType,
                            deeplink = tile?.deeplinkUrl,
                            image = tile?.icon,
                            text = tile?.displayName,
                            analyticsName = tile?.analyticsName,
                            checkIfRegistered = tile?.checkIfRegistered.isTrue
                        )
                    )
                }
            }
        }

        binding.rvTileItems.apply {
            adapter = tileAdapter
            tileAdapter.populateTiles(tiles)
        }

        tileAdapter.itemClickListener = { type, url, analyticsName, checkIfRegistered ->
            val map = HashMap<String, String>()
            if (analyticsName != null) {
                map[HomePageAnalyticsConstants.BUTTON_NAME] = analyticsName

                Analytics.trackEvent(HomePageAnalyticsConstants.HOMEPAGE_BUTTON_CLICK, map)
            } else {
                Analytics.trackEvent(HomePageAnalyticsConstants.HOMEPAGE_BUTTON_CLICK)
            }

            if (isUserAllowedOpenPage(url)) {
                onItemClickListener?.invoke(
                    bodyName,
                    type.orEmpty(),
                    url.orEmpty(),
                    checkIfRegistered
                )
            }
        }
    }

    private fun isUserAllowedOpenPage(url: String?): Boolean {
        try {
            if (url?.startsWith(pageRequiresTerminal, true) == false) return true
            if (activity is HomePageActivity) {
                val homePageActivity = activity as HomePageActivity
                if (homePageActivity.isTerminalActive) {
                    return true
                } else {
                    if (Utils.isFixedTerminal()) {
                        homePageActivity.showTerminalActivationError()
                        return false
                    } else {
                        return true
                    }
                }
            }
        } catch (e: Exception) {
            FirebaseCrashlytics.getInstance().recordException(e)
        }
        return true
    }

    private fun isCardReaderConnected(): Boolean = context?.let {
        (
            BluetoothDevices.hasPairedCardReader() ||
                CardReaderHelper.getInstance().isCardReaderConnected()
            )
    } ?: false

    private fun connectivityCheck(homePageBody: HomePageBody?): Boolean {
        Log.d("--->bodyName", homePageBody?.bodyName.orEmpty())
        return when (variantConfig.variantIdentifier) {
            "MINIATMPRO" -> homePageBody?.bodyName == "edc_miniatmpro_homepage_card"
            else -> if (Utils.isCardReader()) {
                homePageBody?.bodyName == "edc_saku_homepage_card"
            } else {
                homePageBody?.bodyName == "edc_homepage_card"
            }
        }
    }
}
