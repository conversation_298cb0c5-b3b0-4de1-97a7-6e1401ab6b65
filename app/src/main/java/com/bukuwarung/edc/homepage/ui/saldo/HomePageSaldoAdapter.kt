package com.bukuwarung.edc.homepage.ui.saldo

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.edc.databinding.SaldoHomepageItemBinding
import com.bukuwarung.edc.homepage.ui.tile.model.TileData
import com.bukuwarung.edc.util.singleClick
import com.bumptech.glide.Glide

class HomePageSaldoAdapter(private val list: List<TileData>, private val callBack: Callback) :
    RecyclerView.Adapter<HomePageSaldoAdapter.HomePageSaldoViewHolder>() {

    interface Callback {
        fun buttonClicked(tile: TileData)
    }

    inner class HomePageSaldoViewHolder(private val binding: SaldoHomepageItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(item: TileData) = with(binding) {
            val context = root.context
            Glide.with(context)
                .load(item.image)
                .into(ivImage)
            tvName.text = item.text
            root.singleClick { callBack.buttonClicked(item) }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): HomePageSaldoViewHolder {
        val itemBinding =
            SaldoHomepageItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return HomePageSaldoViewHolder(itemBinding)
    }

    override fun getItemCount() = list.size

    override fun onBindViewHolder(holder: HomePageSaldoViewHolder, position: Int) {
        holder.bind(list[position])
    }
}
