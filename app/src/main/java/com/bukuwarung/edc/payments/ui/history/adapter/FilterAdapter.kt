package com.bukuwarung.edc.payments.ui.history.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.edc.databinding.ItemFilterBinding
import com.bukuwarung.edc.payments.data.model.Filter
import javax.inject.Inject

class FilterAdapter @Inject constructor(val filters: ArrayList<Filter>, val callback: Callback) :
    RecyclerView.Adapter<FilterAdapter.FilterViewHolder>() {

    interface Callback {
        fun isAnythingChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FilterViewHolder {
        val itemBinding =
            ItemFilterBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return FilterViewHolder(itemBinding)
    }

    override fun onBindViewHolder(holder: FilterViewHolder, position: Int) {
        holder.bind(filters[position])
    }

    override fun getItemCount() = filters.size

    inner class FilterViewHolder(val binding: ItemFilterBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(filter: Filter) {
            with(binding) {
                cbFilter.text = filter.label
                cbFilter.isChecked = filter.isChecked
                cbFilter.setOnCheckedChangeListener { compoundButton, state ->
                    when (state) {
                        false -> filter.isChecked = false
                        true -> filter.isChecked = true
                    }
                    callback.isAnythingChanged()
                }
            }
        }
    }
}
