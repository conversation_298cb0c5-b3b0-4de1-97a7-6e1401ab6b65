package com.bukuwarung.edc.payments.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class Bank(
    @SerializedName("bank_code")
    val bankCode: String,
    @SerializedName("name")
    val bankName: String,
    @SerializedName("logo")
    val logo: String? = null,
    @SerializedName("minimum_limit")
    val minimumLimit: Double? = null,
    @SerializedName("maximum_limit")
    val maximumLimit: Double? = null,
    @SerializedName("is_disabled")
    val isDisabled: Boolean? = null,
    @SerializedName("message")
    val message: String? = null,
    @SerializedName("bank_swift_code")
    val bankSCode: String? = null,
    @SerializedName("id")
    val bankId: String? = null,

    @field:SerializedName("status")
    val status: String? = null,

    @field:SerializedName("payment_bank_code")
    val paymentBankCode: String? = null,

    @SerializedName("metadata")
    val metadata: Metadata? = null
) : Parcelable {
    companion object {
        val BANKS: ArrayList<Bank> = ArrayList<Bank>().also {
            it.add(
                Bank(
                    "BCA",
                    "Bank Central Asia (BCA)",
                    "https://bw-assets.s3-ap-southeast-1.amazonaws.com/payments/bank-logos/bank-bca.png"
                )
            )
            it.add(
                Bank(
                    "MANDIRI",
                    "Bank Mandiri",
                    "https://bw-assets.s3-ap-southeast-1.amazonaws.com/payments/bank-logos/bank-mandiri.png"
                )
            )
            it.add(
                Bank(
                    "BNI",
                    "Bank Negara Indonesia (BNI)",
                    "https://bw-assets.s3-ap-southeast-1.amazonaws.com/payments/bank-logos/bank-bni.png"
                )
            )
            it.add(
                Bank(
                    "BRI",
                    "Bank Rakyat Indonesia (BRI)",
                    "https://bw-assets.s3-ap-southeast-1.amazonaws.com/payments/bank-logos/bank-bri.png"
                )
            )
            it.add(
                Bank(
                    "PERMATA",
                    "Bank Permata",
                    "https://bw-assets.s3-ap-southeast-1.amazonaws.com/payments/bank-logos/bank-permata.png"
                )
            )
            it.add(
                Bank(
                    "CIMB",
                    "Bank CIMB Niaga",
                    "https://bw-assets.s3-ap-southeast-1.amazonaws.com/payments/bank-logos/bank-cimb.png"
                )
            )
            it.add(
                Bank(
                    "TABUNGAN_PENSIUNAN_NASIONAL",
                    "Bank Tabungan Pensiunan Nasional",
                    "https://bw-assets.s3-ap-southeast-1.amazonaws.com/payments/bank-logos/bank-btpn.png"
                )
            )
            it.add(
                Bank(
                    "DANAMON",
                    "Bank Danamon",
                    "https://bw-assets.s3-ap-southeast-1.amazonaws.com/payments/bank-logos/bank-danamon.png"
                )
            )
            it.add(
                Bank(
                    "MEGA",
                    "Bank Mega",
                    "https://bw-assets.s3-ap-southeast-1.amazonaws.com/payments/bank-logos/bank-mega.png"
                )
            )
            it.add(
                Bank(
                    "BTN",
                    "Bank Tabungan Negara (BTN)",
                    "https://bw-assets.s3-ap-southeast-1.amazonaws.com/payments/bank-logos/bank-btn.png"
                )
            )
        }
    }

    override fun equals(other: Any?): Boolean =
        other is Bank && other.bankCode.isNullOrBlank().not() && other.bankCode == bankCode

    @Parcelize
    data class Metadata(
        @SerializedName("message")
        val message: MessageMetadata? = null
    ) : Parcelable

    @Parcelize
    data class MessageMetadata(
        @SerializedName("message_id")
        val messageId: String? = null
    ) : Parcelable
}
