package com.bukuwarung.edc.payments.data.model

import com.google.gson.annotations.SerializedName

data class VirtualAccount(
    @SerializedName("bank_code")
    val bankCode: String,
    @SerializedName("account_number")
    val accountNumber: String?,
    @SerializedName("name")
    val name: String,
    @SerializedName("logo")
    val logo: String,
    @SerializedName("expiry_time")
    val expiryTime: String?
)
