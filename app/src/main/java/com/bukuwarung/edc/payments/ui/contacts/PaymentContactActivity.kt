package com.bukuwarung.edc.payments.ui.contacts

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.ActivityContactPaymentBinding
import com.bukuwarung.edc.payments.data.model.Contact
import com.bukuwarung.edc.payments.data.model.request.FavouriteRequest
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class PaymentContactActivity :
    AppCompatActivity(),
    ContactSearchResultsFragment.OnCustomerSelectedCallback,
    UserContactFragment.OnSaveButtonCallback {

    private lateinit var binding: ActivityContactPaymentBinding

    private var userContactFragment: UserContactFragment? = null

    companion object {
        private const val ORDER_ID = "orderId"
        private const val ENTRY_POINT = "entryPoint"

        fun createIntent(context: Context, orderId: String?, entryPoint: String): Intent {
            val intent = Intent(context, PaymentContactActivity::class.java)
            intent.putExtra(ORDER_ID, orderId)
            intent.putExtra(ENTRY_POINT, entryPoint)
            return intent
        }
    }

    private val viewModel: PaymentContactViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityContactPaymentBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setupView()
        subscribeState()
    }

    private fun setupView() {
        userContactFragment = UserContactFragment.getInstance(
            getString(R.string.favourite_title),
            UserContactFragment.TRANSACTION_TYPE_FAVOURITE,
            CustomerSearchUseCase.FAVORITE,
            intent?.getStringExtra(ORDER_ID).orEmpty()
        ).also {
            supportFragmentManager.beginTransaction()
                .add(
                    binding.contactFragmentContainer.id,
                    it
                ).commit()
        }
    }

    private fun subscribeState() {
        viewModel.observeEvent.observe(this) {
            when (it) {
                is PaymentContactViewModel.Event.OnAddFavourite -> {
                    if (it.favouriteDetail == null) {
                        userContactFragment?.showSameNameError(it.message)
                    } else {
                        val intent = Intent()
                        intent.putExtra("message", it.message)
                        intent.putExtra("favourite_detail", it.favouriteDetail)
                        setResult(RESULT_OK, intent)
                        finish()
                    }
                }
            }
        }
    }

    override fun onCustomerSelected(contact: Contact?, contactSource: String) {
        viewModel.addFavourite(
            FavouriteRequest(
                contact?.name,
                contact?.mobile,
                intent?.getStringExtra(ORDER_ID)
            ),
            intent?.getStringExtra(ENTRY_POINT),
            this@PaymentContactActivity
        )
    }

    override fun onSave(name: String) {
        viewModel.addFavourite(
            FavouriteRequest(name, null, intent?.getStringExtra(ORDER_ID)),
            intent?.getStringExtra(
                ENTRY_POINT
            ),
            this@PaymentContactActivity
        )
    }
}
