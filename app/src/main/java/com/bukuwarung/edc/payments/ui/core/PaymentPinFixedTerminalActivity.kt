package com.bukuwarung.edc.payments.ui.core

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.ActivityPaymentPinFixedTerminalBinding
import com.bukuwarung.edc.util.singleClick
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class PaymentPinFixedTerminalActivity : AppCompatActivity() {

    companion object {
        const val CREATE_PIN = "create_pin"
        const val UPDATE_PIN = "update_pin"
        const val ENTRY_POINT = "ENTRY_POINT"
    }

    private lateinit var binding: ActivityPaymentPinFixedTerminalBinding
    private val entryPoint by lazy { intent?.getStringExtra(ENTRY_POINT).orEmpty() }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPaymentPinFixedTerminalBinding.inflate(layoutInflater)
        setContentView(binding.root)
        binding.apply {
            toolbar.navigationIcon = ContextCompat.getDrawable(
                this@PaymentPinFixedTerminalActivity,
                R.drawable.vector_arrow_back
            )
            toolbar.setNavigationOnClickListener { finish() }
            btnBack.singleClick { finish() }
            tvTitle.text =
                if (entryPoint.equals(CREATE_PIN, true)) {
                    getString(R.string.create_pin_on_bukuagen)
                } else {
                    getString(R.string.update_pin_on_bukuagen)
                }
            toolbar.title =
                if (entryPoint.equals(CREATE_PIN, true)) {
                    getString(R.string.set_saldo_pin)
                } else {
                    getString(R.string.change_saldo_pin)
                }
        }
    }
}
