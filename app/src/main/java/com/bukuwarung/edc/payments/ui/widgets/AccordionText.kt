package com.bukuwarung.edc.payments.ui.widgets

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.LayoutAccordionTextBinding
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.getDrawableCompat
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.showView

class AccordionText(context: Context, attrs: AttributeSet) : ConstraintLayout(context, attrs) {

    private val binding: LayoutAccordionTextBinding =
        LayoutAccordionTextBinding.inflate(LayoutInflater.from(context), this, true)

    private var stateData: AccordionTextData = AccordionTextData(
        expandedStateLabel = context.getString(R.string.click_details),
        collapsedStateLabel = context.getString(R.string.label_close),
        loading = false,
        expandedState = false
    )

    data class AccordionTextData(
        var expandedStateLabel: String,
        var collapsedStateLabel: String,
        var loading: Boolean = false,
        var expandedState: Boolean = false
    )

    fun setView(data: AccordionTextData) {
        this.stateData = data
        with(binding) {
            pbStatefulLoading.visibility = stateData.loading.asVisibility()
            ivStatefulIcon.visibility = (!stateData.loading).asVisibility()
            if (stateData.expandedState) {
                ivStatefulIcon.setImageDrawable(
                    context.getDrawableCompat(R.drawable.vector_chevron_down)
                )
                tvStatefulLabel.text = stateData.expandedStateLabel
            } else {
                ivStatefulIcon.setImageDrawable(
                    context.getDrawableCompat(R.drawable.vector_chevron_down)
                )
                tvStatefulLabel.text = stateData.collapsedStateLabel
            }
            tvStatefulLabel.requestLayout()
        }
    }

    fun switchToCollapseView() {
        with(binding) {
            pbStatefulLoading.hideView()
            ivStatefulIcon.apply {
                setImageDrawable(
                    context.getDrawableCompat(R.drawable.vector_chevron_down)
                )
                showView()
            }
            tvStatefulLabel.apply {
                text = stateData.collapsedStateLabel
                requestLayout()
            }
        }
    }

    fun switchToExpandedView() {
        with(binding) {
            pbStatefulLoading.hideView()
            ivStatefulIcon.apply {
                setImageDrawable(
                    context.getDrawableCompat(R.drawable.vector_chevron_down)
                )
                showView()
            }
            tvStatefulLabel.apply {
                text = stateData.expandedStateLabel
                requestLayout()
            }
        }
    }

    fun switchToLoadingView() {
        with(binding) {
            pbStatefulLoading.showView()
            ivStatefulIcon.hideView()
        }
    }
}
