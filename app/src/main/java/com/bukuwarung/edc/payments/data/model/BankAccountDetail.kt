package com.bukuwarung.edc.payments.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

data class AddBankAccountDetail(
    val success: Boolean? = null,
    val account: BankAccountDetail? = null
)

data class BankAccountDetail(
    @SerializedName("account_id")
    var accountId: String? = null,
    @SerializedName("bank_detail")
    var bankDetail: BankDetail? = null,
    @SerializedName("customer_id")
    var customerId: String? = null,
    @SerializedName("account_detail")
    var accountDetail: AccountDetail? = null,
    var flags: Flags? = null
)

data class BankDetail(var code: String? = null, var name: String? = null, var icon: String? = null)

data class AccountDetail(
    @SerializedName("minimum_disbursement_amount")
    var minimumDisbursementAmount: Double? = null,
    @SerializedName("masked_number")
    var maskedNumber: String? = null,
    @SerializedName("holder_name")
    var holderName: String? = null,
    var id: String? = null,
    var status: String? = null,
    var relation: String? = null
)

@Parcelize
data class Flags(
    var active: Boolean? = null,
    var refundable: Boolean? = null,
    var favourite: Boolean? = null,
    var qris: Boolean? = null
) : Parcelable
