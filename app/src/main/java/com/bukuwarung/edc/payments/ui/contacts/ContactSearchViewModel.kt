package com.bukuwarung.edc.payments.ui.contacts

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.edc.payments.data.model.Contact
import com.bukuwarung.edc.payments.data.model.RowHolder
import com.bukuwarung.edc.payments.usecase.FinproUseCase
import com.bukuwarung.edc.ppob.constants.PpobAnalytics
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.edc.ppob.recentsandfavourites.model.ProfilesItem
import com.bukuwarung.edc.util.RemoteConfigUtils
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.orNil
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

@HiltViewModel
class ContactSearchViewModel @Inject constructor(private val finproUseCase: FinproUseCase) :
    ViewModel() {
    private var contactList: ArrayList<ContactListDto> = arrayListOf()
    private var favoriteContacts: List<Contact> = listOf()
    private var recommendationContacts: List<ContactListDto> = listOf()
    private var contactRowHolderList: ArrayList<RowHolder> = arrayListOf()

    private val contactsMutableObserver = MutableLiveData<ContactEvent>()
    val contactsObserver: LiveData<ContactEvent> = contactsMutableObserver
    private val recommendationPageCount = PpobConst.RECOMMENDATION_CONTACTS_COUNT
    private var recommendationsPageNumber = 0

    private val inputContactNameMutableObserver: MutableLiveData<OnContactNameInput> =
        MutableLiveData()
    val inputContactNameObserver: LiveData<OnContactNameInput> = inputContactNameMutableObserver

    private val businessId: String = Utils.getPaymentAccountId()
    private var query: String = "&"

    private val favoriteCustomerConfig = RemoteConfigUtils.FavoriteCustomer
    private val isFavoriteCustomerEnabled = favoriteCustomerConfig.isEnabled()
    private var showFavoriteContact: Boolean = false
    private var orderId = ""

    fun init(orderId: String = "") {
        if (this.orderId.isEmpty()) {
            this.orderId = orderId
        }
    }

    fun onSearchTextChange(queryStr: String) {
        // when user starts to type the query, we should be change the value of page number to 0
        recommendationsPageNumber = 0
        recommendationContacts = emptyList() // to fetch new results assigning it to empty list
        if (queryStr.trim() == query) {
            return
        }
        query = queryStr.trim()
        if (query.isNotEmpty()) {
            inputContactNameMutableObserver.value = OnContactNameInput.UpdateInputName(query)
        } else {
        }
        viewModelScope.launch {
            contactList.clear()
            contactRowHolderList.clear()

            if (showFavoriteContact && isFavoriteCustomerEnabled) {
//                mostTransactingContact(query)
                if (favoriteContacts.isNotEmpty()) {
                    contactList.add(
                        ContactListDto(
                            Contact(
                                "",
                                "",
                                "",
                                ContactRepository.DUMMY_FAVORITE_TITLE_SEPARATOR
                            ),
                            PpobAnalytics.PHONEBOOK_FAV_CUSTOMER_SECTION
                        )
                    )
                }
                contactList.addAll(
                    favoriteContacts.map {
                        ContactListDto(
                            it,
                            PpobAnalytics.PHONEBOOK_FAV_CUSTOMER_SECTION
                        )
                    }
                )
                if (contactList.isNotEmpty()) {
                    contactList = contactList.distinct() as ArrayList<ContactListDto>
                }
            }
            if (orderId.isNotBlank()) {
                // api call to fetch the recommendations
                fetchRecommendations()
            }
        }
    }

    fun fetchRecommendations(incrementPageNumber: Boolean = false) =
        viewModelScope.launch(Dispatchers.IO) {
            if (incrementPageNumber) {
                contactList.clear()
                contactRowHolderList.clear()
                recommendationsPageNumber += 1
                setContactEvent(ContactEvent.ShowLoaderOnSearchResultsFragment(true))
            }
            try {
                val response = finproUseCase.getRecommendations(
                    Utils.getPaymentAccountId(),
                    orderId,
                    query,
                    recommendationsPageNumber,
                    recommendationPageCount
                )
                if (response.isSuccessful) {
                    recommendationContacts = convertListOfProfileItemsToContact(
                        response.body()?.profiles ?: emptyList()
                    )
                    if (recommendationContacts.isNotEmpty()) {
                        contactList.add(
                            ContactListDto(
                                Contact(
                                    "",
                                    "",
                                    null,
                                    ContactRepository.DUMMY_RECOMMENDATION_TITLE_SEPARATOR
                                ),
                                PpobAnalytics.PHONEBOOK_RECOMMENDATION_CUSTOMER_SECTION
                            )
                        )
                    }
                    contactList.addAll(recommendationContacts)
                    if (contactList.isNotEmpty()) {
                        contactList = contactList.distinct() as ArrayList<ContactListDto>
                    }
                    // checking whether to show the load more button or not by checking if any more elements are present to be loaded, if true then adding it.
                    // so this condition can be checked by subtracting total recommendations shown from total recommendations present
                    // so total recommendations present = response.body.metadata?.totalCount?:0
                    // total recommendations already shown on screen = ((recommendationsPageNumber)*recommendationPageCount) - (response.body.profiles?.size?:0)
                    // if the resultant value > 0, means there are more contacts which can be loaded.
                    val flag: Boolean =
                        response.body()?.metadata?.totalCount.orNil -
                            (recommendationsPageNumber * recommendationPageCount) -
                            response.body()?.profiles?.size.orNil >
                            0
                    if (flag) {
                        contactList.add(
                            ContactListDto(
                                Contact(
                                    "",
                                    "",
                                    "",
                                    ContactRepository.DUMMY_RECOMMENDATION_BUTTON_SEPARATOR
                                ),
                                PpobAnalytics.LOAD_MORE_RECOMMENDATION_CUSTOMER_SECTION
                            )
                        )
                    }
                    setContactEvent(ContactEvent.ShowLoaderOnSearchResultsFragment(false))
                    setContactEvent(
                        ContactEvent.DisplayContacts(
                            contactsToViewHolderList(
                                contactRowHolderList,
                                contactList
                            ),
                            query
                        )
                    )
                } else {
                    setContactEvent(ContactEvent.ShowLoaderOnSearchResultsFragment(false))
                    setContactEvent(
                        ContactEvent.DisplayContacts(
                            contactsToViewHolderList(
                                contactRowHolderList,
                                contactList
                            ),
                            query
                        )
                    )
                }
            } catch (_: Exception) {
            }
        }

    private fun convertListOfProfileItemsToContact(
        profileList: List<ProfilesItem>
    ): List<ContactListDto> {
        val contactList: ArrayList<ContactListDto> = ArrayList(recommendationContacts)
        contactList.addAll(
            profileList.map {
                ContactListDto(
                    Contact(
                        it.alias,
                        // we are using : to highlight additional count with different color
                        // compared to the favourite text on Contact Adapter file. example
                        "",
                        null,
                        ""
                    ),
                    PpobAnalytics.PHONEBOOK_RECOMMENDATION_CUSTOMER_SECTION,
                    it.favouritesText.orEmpty(),
                    it.additionalCount.orEmpty()
                )
            }
        )
        return contactList.toList()
    }

    private fun contactsToViewHolderList(
        resultList: ArrayList<RowHolder>,
        contactList: ArrayList<ContactListDto>
    ): ArrayList<RowHolder> {
        if (contactList.isEmpty().not()) {
            val size = contactList.size
            for (i in 0 until size) {
                val contact = contactList[i].contact
                when {
                    contact.customerId != null &&
                        contact.customerId == ContactRepository.DUMMY_SEPARATOR -> {
                        resultList.add(RowHolder.SeparatorHolder())
                    }

                    contact.customerId != null &&
                        contact.customerId == ContactRepository.DUMMY_TITLE_SEPARATOR -> {
                        resultList.add(RowHolder.CustomerSeparatorHolder())
                    }

                    contact.customerId != null &&
                        contact.customerId == ContactRepository.DUMMY_FAVORITE_TITLE_SEPARATOR -> {
                        resultList.add(RowHolder.FavoriteCustomerSeparatorHolder())
                    }

                    contact.customerId != null &&
                        contact.customerId ==
                        ContactRepository.DUMMY_RECOMMENDATION_TITLE_SEPARATOR -> {
                        resultList.add(RowHolder.RecommendationCustomerSeparatorHolder())
                    }

                    contact.customerId != null &&
                        contact.customerId ==
                        ContactRepository.DUMMY_RECOMMENDATION_BUTTON_SEPARATOR -> {
                        resultList.add(RowHolder.LoadMoreRecommendationSeparatorHolder())
                    }

                    else -> {
                        val contactSource = contactList[i].source
                        resultList.add(
                            RowHolder.ContactRowHolder(
                                contact,
                                contactSource,
                                contactList[i].favouriteText,
                                contactList[i].favouriteCount
                            )
                        )
                    }
                }
            }
        }
        return resultList
    }

    fun onContactSelected(pos: Int, contactSource: String) = viewModelScope.launch {
        when (val holder = contactRowHolderList[pos]) {
            is RowHolder.ContactRowHolder -> {
                setContactEvent(ContactEvent.UpdateSelectedCustomer(holder.contact, contactSource))
            }
        }
    }

    private suspend fun setContactEvent(contactEvent: ContactEvent) =
        withContext(Dispatchers.Main) {
            contactsMutableObserver.value = contactEvent
        }

    fun setShowFavoriteContacts(show: Boolean) {
        showFavoriteContact = show
    }

    sealed class ContactEvent {
        data class ShowLoaderOnSearchResultsFragment(val showLoader: Boolean) : ContactEvent()
        data class DisplayContacts(val contactRowHolder: List<RowHolder>, val query: String) :
            ContactEvent()

        data class UpdateSelectedCustomer(val contact: Contact, val contactSource: String) :
            ContactEvent()
    }

    sealed class OnContactNameInput {
        data class UpdateInputName(val inputName: String) : OnContactNameInput()
    }
}

data class ContactListDto(
    val contact: Contact,
    // used for analytics
    val source: String,
    // used in case of favourite recommendation contacts
    val favouriteText: String = "",
    // used in case of favourite recommendation contacts
    val favouriteCount: String = ""
)
