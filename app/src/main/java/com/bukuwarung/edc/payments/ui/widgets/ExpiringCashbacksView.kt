package com.bukuwarung.edc.payments.ui.widgets

import android.content.Context
import android.text.method.LinkMovementMethod
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.ExpiringCashbackViewBinding
import com.bukuwarung.edc.payments.data.model.ExpiringCashBackInfo
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.getColorCompat
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick

class ExpiringCashbacksView(context: Context, attrs: AttributeSet) :
    ConstraintLayout(context, attrs) {

    private val binding = ExpiringCashbackViewBinding.inflate(
        LayoutInflater.from(context),
        this,
        true
    )

    interface Callback {
        fun openCashbackInfo()
        fun spendCashbacks()
    }

    fun setView(cashbackSummary: ExpiringCashBackInfo, callback: Callback) {
        binding.tvExpiringCbInfo.apply {
            val amount = Utils.formatAmount(cashbackSummary.amount)
            val expiryDate = cashbackSummary.expiryDate.orEmpty()
            text = Utils.decorateTextString(
                context.getString(
                    R.string.expiring_cashback_info,
                    amount,
                    expiryDate
                ),
                hashMapOf(
                    amount to Utils.TextDecorations(
                        bold = true,
                        textColor = context.getColorCompat(R.color.black_80)
                    ),
                    expiryDate to Utils.TextDecorations(
                        bold = true,
                        textColor = context.getColorCompat(R.color.black_80)
                    ),
                    context.getString(
                        R.string.expiring_cashback_info_clickable
                    ) to Utils.TextDecorations(
                        bold = false,
                        underline = true,
                        textColor = context.getColorCompat(R.color.colorPrimary),
                        onClick = { callback.openCashbackInfo() }
                    )
                )
            )
            movementMethod = LinkMovementMethod.getInstance()
        }
        binding.btnSpendCashback.singleClick {
            callback.spendCashbacks()
        }
        showView()
    }
}
