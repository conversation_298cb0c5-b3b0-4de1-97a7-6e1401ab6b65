package com.bukuwarung.edc.payments.ui.saldo

import android.graphics.Typeface
import android.os.Bundle
import android.view.MenuItem
import android.widget.TextView
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.ActivitySaldoTopupBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.global.network.Status
import com.bukuwarung.edc.payments.constant.PaymentAnalyticsConst
import com.bukuwarung.edc.payments.constant.PaymentRemoteConfig
import com.bukuwarung.edc.payments.data.model.FlagKey
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.afterTextChanged
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.getColorCompat
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.isNotNullOrBlank
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.orNil
import com.bukuwarung.edc.util.setDrawableRightListener
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.webview.WebviewActivity
import com.bukuwarung.network.utils.ResourceState
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class SaldoTopupActivity : AppCompatActivity() {

    private val saldoViewModel: SaldoViewModel by viewModels()

    private lateinit var binding: ActivitySaldoTopupBinding
    private var chosenValue: TextView? = null
    private var selectedAmount: Float? = null
    private var showSaldoTopupAdminFeesInfo = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivitySaldoTopupBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        showSaldoTopupAdminFeesInfo =
            PaymentRemoteConfig.getAppText().saldoTopupAdminFeesInfo.isNotNullOrBlank()
        with(binding) {
            topupFeesInfo.apply {
                visibility = showSaldoTopupAdminFeesInfo.asVisibility()
                setDrawableRightListener { topupFeesInfo.hideView() }
                text = PaymentRemoteConfig.getAppText().saldoTopupAdminFeesInfo
            }
            tv50kAmount.setOnClickListener {
                handleAmountSelection(tv50kAmount, 50000.0)
            }
            tv100kAmount.setOnClickListener {
                handleAmountSelection(tv100kAmount, 100000.0)
            }
            tv250kAmount.setOnClickListener {
                handleAmountSelection(tv250kAmount, 250000.0)
            }
            tv500kAmount.setOnClickListener {
                handleAmountSelection(tv500kAmount, 500000.0)
            }
            tv750kAmount.setOnClickListener {
                handleAmountSelection(tv750kAmount, 750000.0)
            }
            tv1000kAmount.setOnClickListener {
                handleAmountSelection(tv1000kAmount, 1000000.0)
            }
            cetAmountInput.afterTextChanged {
                chosenValue?.let {
                    resetTextView(it)
                    chosenValue = null
                }
                selectedAmount = cetAmountInput.getNumberValue().toFloat()
                saldoViewModel.onAmountChanged(cetAmountInput.getNumberValue().toDouble())
                performAmountChecks()
            }
            binding.btnSubmit.setOnClickListener {
                saldoViewModel.createSaldoTopup()
            }
            if (showSaldoTopupAdminFeesInfo) setSaldoTopupFeesInfo() else setMinimumAmountInfo()
            cetAmountInput.requestFocus()
        }

        saldoViewModel.saldo.observe(this) {
            when (it) {
                is ResourceState.Success -> {
                    it.data.let {
                        if (it.limit != null) {
                            binding.clLimit.showView()
                            binding.tvSaldoLimit.text =
                                getString(R.string.saldo_balance_x, Utils.formatAmount(it.amount))
                            val remainingAmount = if (it.limit == 0.0) {
                                0
                            } else {
                                Utils.formatAmount(it.limit - it.amount.orNil)
                            }
                            binding.tvSaldoLimitLeft.text = getString(
                                R.string.saldo_limit_available,
                                remainingAmount
                            )
                        }
                    }
                }

                else -> {
                }
            }
        }

        saldoViewModel.existingTopup.observe(this) {
            when (it.status) {
                Status.SUCCESS -> {
                    it.data?.let {
                        if (it.isNotEmpty()) {
                            binding.grExistingTopupInfo.showView()
                            binding.tvAbortMessage.text =
                                getString(R.string.continue_pay_and_abort_previous)
                        } else {
                            binding.grExistingTopupInfo.hideView()
                        }
                    }
                }

                else -> {}
            }
        }

        saldoViewModel.saldoAdminFee.observe(this) {
            when (it) {
                is ResourceState.Success -> {
                    binding.pbSaldo.hideView()
                    performAmountChecks()
                }

                is ResourceState.Failure -> {
                    binding.pbSaldo.hideView()
                    binding.btnSubmit.isEnabled = false
                    Toast.makeText(this, it.message, Toast.LENGTH_SHORT).show()
                }

                is ResourceState.Loading -> {
                    binding.pbSaldo.showView()
                    binding.btnSubmit.isEnabled = false
                }
            }
        }

        saldoViewModel.topupRequest.observe(this) {
            when (it.status) {
                Status.SUCCESS -> {
                    Analytics.trackEvent(PaymentAnalyticsConst.WALLET_TOP_UP_SUCCESS)
                    binding.pbSaldo.hideView()
                    it.data?.payments?.firstOrNull()?.paymentUrl?.let {
                        navigateWebView("$it?type=edc")
                    }
                }

                Status.ERROR -> {
                    binding.pbSaldo.hideView()
                    binding.btnSubmit.isEnabled = true
                    Toast.makeText(this, it.message, Toast.LENGTH_SHORT).show()
                }

                Status.LOADING -> {
                    binding.pbSaldo.showView()
                    binding.btnSubmit.isEnabled = false
                }

                Status.NO_INTERNET -> {
                    binding.pbSaldo.hideView()
                }
            }
        }

        saldoViewModel.init()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> onBackPressedDispatcher.onBackPressed()
        }
        return super.onOptionsItemSelected(item)
    }

    override fun onResume() {
        super.onResume()
        saldoViewModel.init(false)
    }

    private fun performAmountChecks() {
        if (saldoViewModel.saldoAdminFee.value == null) {
            return
        }
        when {
            saldoViewModel.isBelowMinRequired() -> {
                binding.btnSubmit.isEnabled = false
                // Show min error only if user has entered any amount
                if (!binding.cetAmountInput.isInputEmpty()) {
                    setMinimumAmountInfo()
                }
            }

            saldoViewModel.isAboveMaxLimits() -> {
                binding.btnSubmit.isEnabled = false
                setMaximumAmountInfo(
                    (saldoViewModel.saldo.value as? ResourceState.Success)?.data?.limit,
                    (saldoViewModel.saldo.value as? ResourceState.Success)?.data?.amount
                )
            }

            else -> {
                with(binding) {
                    grSaldoSummary.showView()
                    (saldoViewModel.saldoAdminFee.value as? ResourceState.Success)?.data?.let {
                        if (it.flags?.get(FlagKey.HIDE_ADMIN_FEE.name).isTrue) {
                            grSaldoFee.hideView()
                        } else {
                            if (it.adminFee == 0.0) {
                                tvBwAdminFeeValue.apply {
                                    text = getString(R.string.free).uppercase()
                                    setTextColor(getColorCompat(R.color.green_80))
                                }
                            } else {
                                tvBwAdminFeeValue.apply {
                                    text = Utils.formatAmount(it.adminFee)
                                    setTextColor(getColorCompat(R.color.black_80))
                                }
                            }
                            grSaldoFee.showView()
                        }
                        tvSaldoValue.text =
                            Utils.formatAmount(saldoViewModel.topupAmount - it.adminFee.orNil)
                    }
                    btnSubmit.isEnabled = true
                }
                if (showSaldoTopupAdminFeesInfo) setSaldoTopupFeesInfo() else setMinimumAmountInfo()
            }
        }
    }

    private fun handleAmountSelection(selected: TextView, amount: Double) {
        if (chosenValue == selected) return
        setTVBackgrounds(selected)
        binding.cetAmountInput.setText(Utils.formatAmount(amount))
        binding.cetAmountInput.setSelection(binding.cetAmountInput.text?.length ?: 0)
        chosenValue = selected
        selectedAmount = amount.toFloat()
    }

    private fun navigateWebView(url: String) {
        Analytics.trackEvent(PaymentAnalyticsConst.WALLET_AMOUNT_SELECTED)
        openActivity(WebviewActivity::class.java) {
            putString(
                com.bukuwarung.edc.webview.constant.ClassConstants.WEBVIEW_URL,
                url
            )
        }
    }

    private fun setSaldoTopupFeesInfo() = with(binding) {
        tvAmountLimitError.text = PaymentRemoteConfig.getAppText().saldoTopupFeesInfo
        tvAmountLimitError.setTextColor(getColorCompat(R.color.black_60))
        vwBottomBorder.setBackgroundColor(getColorCompat(R.color.black_10))
        cetAmountInput.setTextColor(getColorCompat(R.color.colorPrimary))
    }

    private fun setMinimumAmountInfo() = with(binding) {
        tvAmountLimitError.text = getString(
            R.string.min_saldo_topup_x,
            Utils.formatAmount(PaymentRemoteConfig.getMinimumTopupSaldoAmount())
        )
        tvAmountLimitError.setTextColor(
            getColorCompat(if (showSaldoTopupAdminFeesInfo) R.color.red_80 else R.color.black_60)
        )
        cetAmountInput.setTextColor(
            getColorCompat(
                if (showSaldoTopupAdminFeesInfo) R.color.red_80 else R.color.colorPrimary
            )
        )
        vwBottomBorder.setBackgroundColor(
            getColorCompat(if (showSaldoTopupAdminFeesInfo) R.color.red_80 else R.color.black_10)
        )
    }

    private fun setMaximumAmountInfo(maxTopupAmount: Double?, currentBalance: Double?) =
        with(binding) {
            tvAmountLimitError.text = getString(
                R.string.error_saldo_topup_limit,
                Utils.formatAmount(maxTopupAmount.orNil - currentBalance.orNil)
            )
            cetAmountInput.setTextColor(getColorCompat(R.color.red_80))
            tvAmountLimitError.setTextColor(getColorCompat(R.color.red_80))
            vwBottomBorder.setBackgroundColor(getColorCompat(R.color.red_80))
        }

    private fun setTVBackgrounds(selected: TextView) {
        // Clear all backgrounds first, then set selected one
        with(binding) {
            resetTextView(tv50kAmount)
            resetTextView(tv100kAmount)
            resetTextView(tv250kAmount)
            resetTextView(tv500kAmount)
            resetTextView(tv750kAmount)
            resetTextView(tv1000kAmount)
            selected.apply {
                setBackgroundResource(R.drawable.bg_solid_blue_solitude_stroke_blue60_corner_4dp)
                setTextColor(getColorCompat(R.color.colorPrimary))
                typeface = Typeface.DEFAULT_BOLD
            }
        }
    }

    private fun resetTextView(textView: TextView) {
        textView.apply {
            setBackgroundResource(R.drawable.bg_stroke_black10_corner_4dp)
            setTextColor(getColorCompat(R.color.black_80))
            typeface = Typeface.DEFAULT
        }
    }
}
