package com.bukuwarung.edc.payments.data.repository

import com.bukuwarung.edc.payments.data.datasource.OrderDataSource
import com.bukuwarung.edc.payments.data.datasource.PaymentDataSource
import com.bukuwarung.edc.payments.data.model.EnableCustomerResponse
import com.bukuwarung.edc.payments.data.model.PaymentHistory
import com.bukuwarung.edc.payments.data.model.TransactionServiceFeatureFlag
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.get
import javax.inject.Inject
import retrofit2.Response

class OrderRepository @Inject constructor(
    private val orderDataSource: OrderDataSource,
    private val paymentDataSource: PaymentDataSource
) {

    suspend fun getOrders(
        accountId: String,
        customerId: String? = null,
        startDate: String? = null,
        endDate: String? = null,
        type: List<String>? = null,
        status: List<String>? = null,
        page: Int? = null,
        limit: Int? = null,
        billerCode: String? = null,
        sorting: String? = null,
        searchQuery: String? = null,
        bukuOrigin: String? = null,
        serialNumberSelected: String? = null
    ): Response<List<PaymentHistory>> = callWithEnablePaymentCheck {
        val searchQueryString = if (searchQuery.isNullOrEmpty()) null else searchQuery
        orderDataSource.getOrders(
            accountId,
            customerId,
            startDate,
            endDate,
            type,
            status,
            page,
            limit,
            billerCode,
            sorting,
            searchQueryString,
            bukuOrigin,
            serialNumberSelected
        )
    }

    suspend fun getFeatureFlag(): Response<TransactionServiceFeatureFlag> =
        callWithEnablePaymentCheck {
            orderDataSource.getFeatureFlag()
        }

    suspend fun getLinkedOrders(orderId: String): Response<List<PaymentHistory>> =
        orderDataSource.getLinkedOrders(orderId)

    // Auxiliary calls
    private suspend fun <T : Any> callWithEnablePaymentCheck(
        requestFunc: suspend () -> Response<T>
    ): Response<T> {
        val result = requestFunc.invoke()
        if (result.isSuccessful && result.code() == 403) {
            val enableResult = enableMerchantPayments(
                Utils.sharedPreferences.get("business_id", ""),
                Utils.sharedPreferences.get("book_name", "")
            )
            if (enableResult.isSuccessful) {
                return requestFunc.invoke()
            }
        }
        return result
    }

    private suspend fun enableMerchantPayments(
        accountId: String,
        bookName: String
    ): Response<EnableCustomerResponse> = paymentDataSource.enabledMerchantPayments(
        accountId,
        mapOf(Pair("business_name", bookName))
    )
}
