package com.bukuwarung.edc.payments.data.model.request

import com.google.gson.annotations.SerializedName

data class BankValidationRequest(
    @SerializedName("bank_code")
    val bankCode: String,
    @SerializedName("account_number")
    val accountNumber: String,
    @SerializedName("account_owner")
    val accountOwner: String? = null,
    @SerializedName("is_payment_in")
    val isPaymentIn: Boolean? = null
)
