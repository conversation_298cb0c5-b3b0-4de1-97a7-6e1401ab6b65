package com.bukuwarung.edc.payments.data.model.request

import com.google.gson.annotations.SerializedName
import java.math.BigDecimal

data class DisbursementOverviewRequest(
    val amount: BigDecimal,
    val description: String,
    val accountId: String,
    val customerId: String,
    @SerializedName("customer_name")
    val customerName: String,
    val paymentCategoryId: String? = null,
    @SerializedName("customer_bank_account_id")
    val customerBankAccountId: String?,
    @SerializedName("va_bank_code")
    val vaBankCode: String?
)
