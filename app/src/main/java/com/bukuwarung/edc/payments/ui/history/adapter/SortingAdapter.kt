package com.bukuwarung.edc.payments.ui.history.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.edc.databinding.ItemSortOptionBinding
import com.bukuwarung.edc.payments.data.model.SortOption
import javax.inject.Inject

class SortingAdapter @Inject constructor(
    val sortingOptions: ArrayList<SortOption>,
    val callback: Callback
) : RecyclerView.Adapter<SortingAdapter.SectionViewHolder>() {

    interface Callback {
        fun onOptionSelected(sortOption: String)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SectionViewHolder {
        val itemBinding =
            ItemSortOptionBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return SectionViewHolder(itemBinding)
    }

    override fun onBindViewHolder(holder: <PERSON><PERSON><PERSON>wHold<PERSON>, position: Int) {
        holder.bind(sortingOptions[position])
    }

    override fun getItemCount() = sortingOptions.size

    inner class SectionViewHolder(val binding: ItemSortOptionBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(option: SortOption) {
            with(binding) {
                tvSortOption.text = option.label
                tvSortOption.setOnClickListener {
                    sortingOptions.map { it.isChecked = false }
                    option.isChecked = true
                    callback.onOptionSelected(option.key)
                }
            }
        }
    }
}
