package com.bukuwarung.edc.payments.data.repository

import com.bukuwarung.edc.payments.data.datasource.FinproDataSource
import com.bukuwarung.edc.payments.data.model.ExistingTopupSaldoResponse
import com.bukuwarung.edc.payments.data.model.PaymentHealthCheckRequest
import com.bukuwarung.edc.payments.data.model.SaldoAdminFeeResponse
import com.bukuwarung.edc.payments.data.model.SaldoResponse
import com.bukuwarung.edc.payments.data.model.TopupSaldoRequest
import com.bukuwarung.edc.payments.data.model.request.FavouriteRequest
import com.bukuwarung.edc.ppob.common.model.Biller
import com.bukuwarung.edc.ppob.common.model.FinproAddCartRequest
import com.bukuwarung.edc.ppob.common.model.PpobProductsWithBillerDetails
import com.bukuwarung.edc.ppob.recentsandfavourites.model.DeleteFavouriteResponse
import com.bukuwarung.edc.ppob.recentsandfavourites.model.FavouriteResponse
import com.bukuwarung.edc.ppob.train.model.TrainEnrollmentDetailResponse
import com.bukuwarung.edc.ppob.train.model.TrainEnrollmentRequest
import com.bukuwarung.network.utils.ResourceState
import com.bukuwarung.network.utils.safeApiCall
import com.bukuwarung.payments.data.model.FinproCheckoutOrderRequest
import javax.inject.Inject
import retrofit2.Response
import retrofit2.http.Body

class FinproRepository @Inject constructor(private val finproDataSource: FinproDataSource) {

    suspend fun getSaldo(): ResourceState<SaldoResponse> = safeApiCall {
        finproDataSource.getSaldo()
    }

    suspend fun getSaldoAdminFee(): ResourceState<SaldoAdminFeeResponse> = safeApiCall {
        finproDataSource.getSaldoAdminFee()
    }

    suspend fun getExistingSaldoTopup(): ResourceState<List<ExistingTopupSaldoResponse>> =
        safeApiCall {
            finproDataSource.getExistingSaldoTopup("CREATED")
        }

    suspend fun createSaldoTopup(request: TopupSaldoRequest) =
        finproDataSource.createSaldoTopup(request)

    suspend fun getPaymentOutStatus(accountId: String, disbursementId: String) =
        finproDataSource.getPaymentOutStatus(accountId, disbursementId)

    suspend fun getOrderDetail(accountId: String, orderId: String, ledgerAccountId: String?) =
        finproDataSource.getOrderDetail(accountId, orderId, ledgerAccountId)

    suspend fun getPpobProductsWithBillerDetails(
        category: String,
        map: Map<String, String>,
        isSpecialProduct: Boolean?
    ): Response<PpobProductsWithBillerDetails> {
        val sb = StringBuilder()
        for ((key, value) in map) {
            sb.append("$key:$value,")
        }
        sb.removeSuffix(",")
        return finproDataSource.getPpobProductsWithBillerDetails(
            category,
            sb.toString(),
            isSpecialProduct
        )
    }

    suspend fun addItemToCart(bookId: String, @Body request: FinproAddCartRequest) =
        finproDataSource.addItemToCart(bookId, request)

    suspend fun getTrainTicketUrl(accountId: String, request: TrainEnrollmentRequest) =
        finproDataSource.getTrainTicketUrl(accountId, request)

    suspend fun getTrainEnrollmentDetail(
        accountId: String,
        enrollmentId: String
    ): Response<TrainEnrollmentDetailResponse> =
        finproDataSource.getTrainEnrollmentDetail(accountId, enrollmentId)

    suspend fun cancelOrder(accountId: String, orderId: String) =
        finproDataSource.cancelOrder(accountId, orderId)

    suspend fun deleteFavourite(
        bookId: String,
        favouriteId: String
    ): Response<DeleteFavouriteResponse> = finproDataSource.deleteFavourite(bookId, favouriteId)

    suspend fun getBillers(category: String?): Response<List<Biller>> =
        finproDataSource.getBillers(category)

    suspend fun getFavourites(
        bookId: String,
        category: String,
        page: Int,
        count: Int
    ): Response<FavouriteResponse> = finproDataSource.getFavourites(bookId, category, page, count)

    suspend fun getRecentTransactions(
        bookId: String,
        category: String,
        page: Int,
        limit: Int
    ): Response<FavouriteResponse> =
        finproDataSource.getRecentTransactions(bookId, category, page, limit)

    suspend fun getPpobStatus(accountId: String, orderId: String) =
        finproDataSource.getPpobStatus(accountId, orderId)

    suspend fun getPaymentMethodsV2(amount: Double, category: String, productCode: String) =
        finproDataSource.getPaymentMethodsV2(amount, category, productCode)

    suspend fun doHealthCheck(paymentHealthCheckRequest: PaymentHealthCheckRequest) =
        finproDataSource.doHealthCheck(paymentHealthCheckRequest)

    suspend fun checkoutOrder(
        bookId: String,
        orderId: String,
        bankAccount: FinproCheckoutOrderRequest,
        checkoutToken: String
    ) = finproDataSource.checkoutOrder(bookId, orderId, bankAccount, checkoutToken)

    suspend fun addFavourite(bookId: String, favouriteRequest: FavouriteRequest) =
        finproDataSource.addFavourite(bookId, favouriteRequest)

    suspend fun getRecommendations(
        bookId: String,
        orderId: String,
        query: String,
        page: Int,
        count: Int
    ) = finproDataSource.getRecommendations(bookId, orderId, query, page, count)
}
