package com.bukuwarung.edc.payments.ui.history.bottomsheet

import android.content.res.Resources
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.edc.databinding.BottomsheetOrderSortingBinding
import com.bukuwarung.edc.global.base.BaseBottomSheetDialogFragment
import com.bukuwarung.edc.payments.ui.history.OrderHistoryViewModel
import com.bukuwarung.edc.payments.ui.history.adapter.SortingAdapter
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.getClassTag
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class SortingBottomSheet :
    BaseBottomSheetDialogFragment(),
    SortingAdapter.Callback {

    companion object {
        fun createInstance(fr: FragmentManager) = SortingBottomSheet().show(fr, getClassTag())
    }

    private val viewModel: OrderHistoryViewModel by activityViewModels()
    private lateinit var binding: BottomsheetOrderSortingBinding

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = BottomsheetOrderSortingBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        dialog?.setOnShowListener {
            val dialog = it as BottomSheetDialog
            val bottomSheet =
                dialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
            bottomSheet?.let { _ ->
                dialog.behavior.state = BottomSheetBehavior.STATE_EXPANDED
                dialog.behavior.peekHeight = Resources.getSystem().displayMetrics.heightPixels - 100
            }
        }

        binding.ivClose.setOnClickListener { dismiss() }

        viewModel.filtersState.let {
            val sortingValues = viewModel.getApplicableFilters()?.sort
            val appliedSorting = it[viewModel.activeTab]?.sorting
            Utils.safeLet(
                sortingValues,
                appliedSorting
            ) { sortingOptions, sortBy ->
                // Update sortingValues from the appliedSorting
                sortingOptions.map { sortingOption ->
                    sortingOption.isChecked = sortingOption.key == sortBy
                }
                binding.rvSortingOptions.apply {
                    layoutManager = LinearLayoutManager(requireContext())
                    adapter = SortingAdapter(sortingOptions, this@SortingBottomSheet)
                }
            }
        }
    }

    override fun onOptionSelected(sortOption: String) {
        viewModel.applySorting()
        dismiss()
    }
}
