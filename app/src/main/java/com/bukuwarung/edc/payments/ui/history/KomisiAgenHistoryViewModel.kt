package com.bukuwarung.edc.payments.ui.history

import OrdersPagingSource
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import androidx.paging.cachedIn
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.payments.constant.PaymentRemoteConfig
import com.bukuwarung.edc.payments.data.datasource.OrdersPagedDataSource
import com.bukuwarung.edc.payments.data.model.DateFilter
import com.bukuwarung.edc.payments.data.model.DeviceDetails
import com.bukuwarung.edc.payments.data.model.OrderHistoryData
import com.bukuwarung.edc.payments.data.model.PagingStatus
import com.bukuwarung.edc.payments.data.model.PaymentHistory
import com.bukuwarung.edc.payments.usecase.OrderUseCase
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.orDefault
import com.bukuwarung.edc.util.orNil
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject

private const val ITEM_PER_PAGE = 10

@HiltViewModel
class KomisiAgenHistoryViewModel @Inject constructor(private val ordersUseCase: OrderUseCase) :
    ViewModel() {

    data class ViewState(
        val loading: Boolean = true,
        val errorMessage: String? = null,
        val cashbackLoading: Boolean = true,
        val cashbackError: String? = null
    )

    var defaultOrSelectedDateFilter: DateFilter = DateFilter(
        label = "Hari ini",
        presetValue = PaymentConst.DatePreset.TODAY
    )

    val viewState: MutableLiveData<ViewState> = MutableLiveData(ViewState())

    val ordersHistory = ArrayList<OrderHistoryData>()
    val orderHistoryLive: MutableLiveData<ArrayList<OrderHistoryData>> =
        MutableLiveData(ordersHistory)

    var startDate: String? = null
    var endDate: String? = null
    var selectedDevice: DeviceDetails? = null

    val ordersPagedData: LiveData<PagingData<OrderHistoryData>> get() = _ordersPagedData
    private val _ordersPagedData = MutableLiveData<PagingData<OrderHistoryData>>()

    private val orderDatesMap = hashMapOf<PaymentConst.HistoryTabs, ArrayList<String>>()
    private val _pagingStatus = MutableLiveData<PagingStatus>()
    val pagingStatus: LiveData<PagingStatus> = _pagingStatus
    private var queryParams =
        OrdersPagedDataSource.QueryParams(accountId = Utils.getPaymentAccountId())

    fun fetchKomisiAgenHistory() {
        queryParams = OrdersPagedDataSource.QueryParams(
            accountId = Utils.getPaymentAccountId(),
            startDate = startDate,
            type = listOf(PaymentHistory.TYPE_SALDO_REDEMPTION),
            endDate = endDate,
            limit = PaymentRemoteConfig.getPaymentConfigs().paginationConfig?.limitPerPage
                ?.orDefault(
                    ITEM_PER_PAGE
                ),
            serialNumberSelected = selectedDevice?.serialNumber
        )

        val pager = Pager(
            config = PagingConfig(
                pageSize = queryParams.limit.orNil,
                prefetchDistance = 2
            ),
            pagingSourceFactory = {
                OrdersPagingSource(
                    ordersUseCase = ordersUseCase,
                    pagingStatusLiveData = _pagingStatus,
                    activeTab = PaymentConst.HistoryTabs.SALDOBONUS,
                    orderDates = orderDatesMap,
                    queryParams = queryParams
                )
            }
        )

        viewModelScope.launch {
            pager.flow.cachedIn(viewModelScope).collectLatest {
                _ordersPagedData.value = it
            }
        }
    }
}
