package com.bukuwarung.edc.payments.ui.core

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.ActivityPaymentConfirmationBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.global.network.Status
import com.bukuwarung.edc.homepage.ui.home.HomePageActivity
import com.bukuwarung.edc.payments.constant.PaymentAnalyticsConst
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.payments.constant.PinType
import com.bukuwarung.edc.payments.data.model.BankAccount
import com.bukuwarung.edc.payments.data.model.SaldoResponse
import com.bukuwarung.edc.payments.data.model.response.DisbursementOverviewResponse
import com.bukuwarung.edc.payments.ui.core.PaymentStatusActivity.Companion.DISBURSEMENT_ID
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.afterTextChanged
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.ifNull
import com.bukuwarung.edc.util.isNotNullOrBlank
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.openActivityForResult
import com.bukuwarung.edc.util.orNil
import com.bukuwarung.edc.util.setDrawable
import com.bukuwarung.edc.util.showView
import com.bukuwarung.network.utils.ResourceState
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class PaymentConfirmationActivity : AppCompatActivity() {

    companion object {
        const val AMOUNT = "amount"
        const val BANK_ACCOUNT = "bank_account"
        const val PAYMENT_CATEGORY_ID = "payment_category_id"
        private const val DISCOUNT_FEE = "{discount_fee}"
    }

    private lateinit var binding: ActivityPaymentConfirmationBinding

    private val paymentViewModel: PaymentViewModel by viewModels()
    private val amount by lazy { intent?.getLongExtra(AMOUNT, 0L) }
    private val bankAccount by lazy { intent?.getParcelableExtra(BANK_ACCOUNT) as? BankAccount }
    private val paymentCategoryId by lazy { intent?.getStringExtra(PAYMENT_CATEGORY_ID) }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityPaymentConfirmationBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        paymentViewModel.setPaymentData(bankAccount, amount.orNil, paymentCategoryId.orEmpty())

        binding.pdvView.setView(
            amount?.toDouble(),
            bankAccount
        )
        with(binding.includePaymentMethod) {
            progressBar.hideView()
            Analytics.trackEvent(PaymentAnalyticsConst.PAYMENT_METHOD_SELECTION_PAGE_VISITED)
            with(btnCompletePayment) {
                isEnabled = false
                setOnClickListener {
                    startPinVerification()
                    Analytics.trackEvent(PaymentAnalyticsConst.PAYMENT_SEND_CREATED)
                }
            }
        }
        binding.etInputNote.afterTextChanged {
            paymentViewModel.onNoteChanged(it)
        }
        binding.includePaymentLoading.ivClose.setOnClickListener { launchSingleTopHome() }

        paymentViewModel.getSaldo()

        paymentViewModel.saldo.observe(this) {
            when (it) {
                is ResourceState.Loading -> {
                }

                is ResourceState.Success -> {
                    setSaldoAsPayment(it.data)
                }

                is ResourceState.Failure -> {
                }
            }
        }

        paymentViewModel.disbursementOverview.observe(this) {
            when (it.status) {
                Status.SUCCESS -> {
                    binding.includePaymentMethod.progressBar.hideView()
                    setPaymentOverview(it.data)
                }

                Status.LOADING -> {
                    binding.includePaymentMethod.progressBar.showView()
                    binding.includePaymentMethod.btnCompletePayment.isEnabled = false
                }

                Status.ERROR -> {
                    binding.includePaymentMethod.progressBar.hideView()
                    binding.includePaymentMethod.btnCompletePayment.isEnabled = true
                }

                Status.NO_INTERNET -> {
                    binding.includePaymentMethod.progressBar.hideView()
                }
            }
        }

        paymentViewModel.addCustomerBank.observe(this) {
            when (it.status) {
                Status.ERROR -> {
                    binding.includePaymentMethod.btnCompletePayment.isEnabled = false
                    Toast.makeText(this, it.message, Toast.LENGTH_SHORT).show()
                }

                else -> {}
            }
        }

        paymentViewModel.healthStatus.observe(this) { healthState ->
            with(binding) {
                when (healthState?.status) {
                    PaymentViewModel.HEALTH_OK -> {
                        tvWarning.hideView()
                        includePaymentMethod.btnCompletePayment.isEnabled = true
                    }

                    PaymentViewModel.HEALTH_WARNING -> {
                        with(tvWarning) {
                            text = getString(R.string.payment_outside_operational_msg)
                            showView()
                        }
                        includePaymentMethod.btnCompletePayment.isEnabled = true
                    }

                    PaymentViewModel.HEALTH_ERROR -> {
                        with(tvWarning) {
                            text = healthState.message
                            showView()
                        }
                        includePaymentMethod.btnCompletePayment.isEnabled = false
                    }
                }
            }
        }

        paymentViewModel.disbursementRequest.observe(this) {
            when (it.status) {
                Status.SUCCESS -> {
                    finish()
                    openActivity(PaymentStatusActivity::class.java) {
                        putString(DISBURSEMENT_ID, it.data?.disbursementId)
                        putParcelable(BANK_ACCOUNT, bankAccount)
                    }
                }

                Status.LOADING -> {
                    with(binding.includePaymentMethod) {
                        progressBar.showView()
                        btnCompletePayment.isEnabled = false
                        // Show full screen loading
                        binding.includePaymentLoading.root.showView()
                        onBackPressedDispatcher.addCallback(
                            this@PaymentConfirmationActivity,
                            object : OnBackPressedCallback(true) {
                                override fun handleOnBackPressed() {
                                    launchSingleTopHome()
                                    finish()
                                }
                            }
                        )
                    }
                }

                Status.ERROR -> {
                    binding.includePaymentMethod.btnCompletePayment.isEnabled = true
                    Toast.makeText(this, it.message, Toast.LENGTH_SHORT).show()
                }

                Status.NO_INTERNET -> {
                    binding.includePaymentMethod.progressBar.hideView()
                }
            }
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> onBackPressedDispatcher.onBackPressed()
        }
        return super.onOptionsItemSelected(item)
    }

    private fun launchSingleTopHome() {
        // TODO: Should redirect to payment history
        val intent = Intent(this, HomePageActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP)
        startActivity(intent)
    }

    private val startPaymentPinActivityForResult =
        registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result: ActivityResult ->
            if (result.resultCode == Activity.RESULT_OK) {
                paymentViewModel.createPaymentOut()
            }
        }

    private fun startPinVerification() {
        openActivityForResult(NewPaymentPinActivity::class.java, startPaymentPinActivityForResult) {
            putString(NewPaymentPinActivity.USECASE, PinType.PIN_CONFIRM.toString())
        }
    }

    private fun setSaldoAsPayment(data: SaldoResponse?) {
        val balance = data?.subBalance?.saldo.ifNull { data?.amount }
        binding.includePaymentMethod.tvPaymentAmount.text = " - ${Utils.formatAmount(balance)}"
        bankAccount?.let { paymentViewModel.validateAndAddCustomerBankAccount(it) }
    }

    private fun setPaymentOverview(data: DisbursementOverviewResponse?) {
        data?.let {
            val destinationBankInformation = data.destinationBankInformation
            binding.pdvView.onPaymentMethodSelected(data)
            showDiscountIfApplicable(it, it.discountFeeText.orEmpty())
            if (destinationBankInformation?.flag == PaymentConst.BANK_DOWN_TIME &&
                destinationBankInformation.message.isNotNullOrBlank()
            ) {
                setDownTimeBank(destinationBankInformation.message.orEmpty())
            }
            val loyaltyDiscount = data.loyaltyDiscount
            val discountedFee = data.fee.orNil - data.discountFee.orNil
            val totalAmount =
                data.amount?.toDouble().orNil + discountedFee - loyaltyDiscount?.tierDiscount.orNil

            val enablePaymentBtn =
                paymentViewModel.isMoreThanSaldoBalance(totalAmount.toLong()).not()
            binding.includePaymentMethod.btnCompletePayment.isEnabled = enablePaymentBtn
            Analytics.trackEvent(PaymentAnalyticsConst.PAYMENT_SEND_PAYMENT_METHOD_SELECTED)
        }
    }

    private fun setDownTimeBank(infoText: String) {
        with(binding) {
            tvWarning.text = infoText
            tvWarning.showView()
        }
    }

    private fun showDiscountIfApplicable(
        overview: DisbursementOverviewResponse,
        discountFeeText: String
    ) {
        val discountValue =
            overview.discountFee.orNil + overview.loyaltyDiscount?.tierDiscount.orNil
        with(binding.includePaymentMethod) {
            if (discountValue > 0.0 && discountFeeText.isNotEmpty()) {
                tvCashbackAmount.showView()
                tvCashbackAmount.setDrawable(0)
                clCashBack.showView()
                tvCashbackAmount.text = discountFeeText.replace(
                    DISCOUNT_FEE,
                    Utils.formatAmount(discountValue)
                )
            } else {
                clCashBack.hideView()
            }
        }
    }
}
