package com.bukuwarung.edc.payments.ui.core

import android.content.DialogInterface
import android.os.Bundle
import android.view.Gravity
import android.view.MenuItem
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.bukuwarung.edc.R
import com.bukuwarung.edc.ZohoChat
import com.bukuwarung.edc.card.ui.receipt.CardReceiptViewModel
import com.bukuwarung.edc.databinding.ActivityOrderDetailBinding
import com.bukuwarung.edc.global.Constant.TAG_PRINT
import com.bukuwarung.edc.global.network.Status
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.payments.constant.PaymentRemoteConfig
import com.bukuwarung.edc.payments.data.model.OrderResponse
import com.bukuwarung.edc.payments.data.model.PaymentHistory.Companion.STATUS_PAID
import com.bukuwarung.edc.payments.util.EditDialogListener
import com.bukuwarung.edc.payments.util.PaymentAuxilliary
import com.bukuwarung.edc.util.ToastUtil.setToast
import com.bukuwarung.edc.util.Utils.bwLog
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick
import com.bukuwarung.edc.webview.WebviewActivity
import com.bukuwarung.edc.webview.constant.ClassConstants
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class OrderDetailActivity :
    AppCompatActivity(),
    EditDialogListener,
    PaymentPendingInfoBottomsheet.ICommunicator {

    companion object {
        const val ORDER_ID = "order_id"
        const val PAYMENT_TYPE = "payment_type"
        const val LEDGER_ACCOUNT_ID = "ledger_account_id"
    }

    private lateinit var binding: ActivityOrderDetailBinding
    private val paymentViewModel: PaymentViewModel by viewModels()

    private val orderId by lazy { intent?.getStringExtra(ORDER_ID) }
    private val paymentType by lazy { intent?.getStringExtra(PAYMENT_TYPE) }
    private val ledgerAccountId by lazy { intent?.getStringExtra(LEDGER_ACCOUNT_ID) }
    private var order: OrderResponse? = null
    private var previousStatus: String = ""
    private var isPendingTrxTimeExceed = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityOrderDetailBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setToolBarView()
        fetchOrderDetails()
        setObservers()
    }

    private fun setObservers() {
        paymentViewModel.observeDetail.observe(this) {
            when (it) {
                is PaymentViewModel.DetailEvent.UpdateServiceFee -> {
                    val makeText = Toast.makeText(this, it.toastText, Toast.LENGTH_SHORT)
                    makeText.setGravity(Gravity.TOP, 0, 100)
                    makeText.show()
                }

                is PaymentViewModel.DetailEvent.ShowPendingTrxHelpOptionInRed -> {
                    isPendingTrxTimeExceed = true
                    binding.orderInfoMessageView.setView(
                        order,
                        paymentType,
                        { refreshScreen() },
                        true
                    )
                }

                is PaymentViewModel.DetailEvent.RefreshScreen -> refreshScreen()
            }
        }
        paymentViewModel.redirectToDetail.observe(this) {
            if (it.orderId.isNotBlank() && it.type.isNotBlank()) {
                openActivity(OrderDetailActivity::class.java) {
                    putString(ORDER_ID, it.orderId)
                    putString(PAYMENT_TYPE, it.type)
                    putString(LEDGER_ACCOUNT_ID, null)
                }
            }
        }
        paymentViewModel.orderDetail.observe(this) {
            when (it.status) {
                Status.SUCCESS -> {
                    binding.pbProgress.hideView()
                    it.data?.let { setOrderData(it) }
                }

                Status.ERROR -> {
                    binding.pbProgress.hideView()
                    Toast.makeText(this, it.message, Toast.LENGTH_SHORT).show()
                }

                Status.LOADING -> {
                    binding.pbProgress.showView()
                }

                Status.NO_INTERNET -> {
                    binding.pbProgress.hideView()
                }
            }
        }

        paymentViewModel.printState.observe(this) {
            when (it) {
                is CardReceiptViewModel.State.SetPrintError -> {
                    bwLog(
                        TAG_PRINT,
                        "Print error [${it.errorStatus.errorCode}]: ${it.errorStatus.msg}"
                    )
                    setToast(
                        this,
                        it.errorStatus.errorLevel,
                        it.errorStatus.msg,
                        binding.root
                    )
                }

                else -> {
                }
            }
        }
    }

    private fun setToolBarView() {
        with(binding.includeToolBar) {
            toolBarLabel.text = getString(R.string.payment_detail)
            tbPpob.navigationIcon =
                ContextCompat.getDrawable(this@OrderDetailActivity, R.drawable.ic_arrow_back)
            tbPpob.setNavigationOnClickListener {
                onBackPressed()
            }
            ivHelp.singleClick {
                openActivity(WebviewActivity::class.java) {
                    putString(ClassConstants.WEBVIEW_TITLE, getString(R.string.help))
                    putString(
                        ClassConstants.WEBVIEW_URL,
                        PaymentRemoteConfig.getPaymentConfigs().supportUrls.payments
                    )
                }
            }
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> onBackPressedDispatcher.onBackPressed()
        }
        return super.onOptionsItemSelected(item)
    }

    private fun setOrderData(data: OrderResponse) {
        order = data
        if (previousStatus.isNotBlank()) checkStatusChange()
        with(binding) {
            orderStatusView.setView(data)
            orderInfoView.setView(data, paymentType, {
                paymentViewModel.getOrderDetail(data.transactionId.orEmpty(), null, true)
            })
            orderInfoMessageView.setView(data, paymentType, { refreshScreen() })
            orderInvoiceView.setView(data, paymentType, supportFragmentManager, paymentViewModel)
            orderPaymentInfoView.setView(data, paymentType)
            orderPpobInfoView.setView(data, paymentType)
            orderHelpView.setView(data, paymentType)
            orderStatusInfoView.setView(
                data,
                paymentType,
                { refreshScreen() },
                {
                    orderInvoiceView.setView(
                        data,
                        paymentType,
                        supportFragmentManager,
                        paymentViewModel,
                        it
                    )
                }
            )
            poweredByFooter.showView()

            if (PaymentAuxilliary.isSaldoIn(paymentType) &&
                data.status == PaymentConst.STATUS_PENDING
            ) {
                clButtons.showView()
                btnCompletePayment.setOnClickListener {
                    paymentViewModel.orderDetail.value?.data?.payments?.firstOrNull()?.let {
                        val checkoutUrl = it.checkoutUrl ?: it.paymentUrl
                        openActivity(WebviewActivity::class.java) {
                            putString(ClassConstants.WEBVIEW_URL, checkoutUrl)
                        }
                    }
                }
            } else {
                clButtons.hideView()
            }
        }
    }

    override fun handleDialogClose(dialog: DialogInterface?) {
        binding.pbProgress.showView()
        orderId?.let {
            paymentViewModel.getOrderDetail(it, ledgerAccountId)
            with(binding) {
                orderStatusView.hideView()
                orderInfoView.hideView()
                orderInfoMessageView.hideView()
                orderInvoiceView.hideView()
                orderPaymentInfoView.hideView()
                orderPpobInfoView.hideView()
                orderHelpView.hideView()
                orderStatusInfoView.hideView()
                poweredByFooter.hideView()
            }
        }
    }

    private fun fetchOrderDetails() {
        orderId?.let {
            paymentViewModel.getOrderDetail(it, ledgerAccountId)
        }
    }

    private fun refreshScreen() {
        previousStatus = order?.status.orEmpty()
        fetchOrderDetails()
    }

    private fun checkStatusChange() {
        if (order?.status?.equals(previousStatus).isTrue && previousStatus.equals(STATUS_PAID)) {
            PaymentPendingInfoBottomsheet.createInstance(isPendingTrxTimeExceed)
                .show(supportFragmentManager, "payment_pending_trx_info")
        } else {
            Toast.makeText(this, getString(R.string.status_updated_message), Toast.LENGTH_SHORT)
                .show()
        }
    }

    override fun showAssistScreen() {
        ZohoChat.openZohoChat("order_detail_activity")
    }

    override fun onDestroy() {
        bwLog(e = Exception("OrderDetailActivity-onDestroy"))
        super.onDestroy()
    }
}
