package com.bukuwarung.edc.payments.util

import androidx.core.util.Pair
import com.google.android.material.datepicker.CalendarConstraints
import com.google.android.material.datepicker.MaterialDatePicker
import java.util.concurrent.TimeUnit
import kotlinx.parcelize.Parcelize

@Parcelize
class RangeDateValidator(private val numberOfDays: Int) : CalendarConstraints.DateValidator {
    private var rangePicker: MaterialDatePicker<Pair<Long, Long>>? = null

    fun setDatePicker(rangePicker: MaterialDatePicker<Pair<Long, Long>>) {
        this.rangePicker = rangePicker
    }

    override fun isValid(date: Long): Boolean {
        val selection = rangePicker?.selection
        if (selection != null) {
            val startDate = selection.first
            val endDate = selection.second
            if (startDate != null) {
                if (endDate != null && endDate != date) {
                    return true
                }
                val days = (numberOfDays - 1) * TimeUnit.DAYS.toMillis(1)
                if (date > startDate + days) {
                    return false
                }
            }
        }
        return true
    }
}
