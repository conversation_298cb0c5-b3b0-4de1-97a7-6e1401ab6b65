package com.bukuwarung.edc.payments.ui.widgets

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.OrderInfoViewBinding
import com.bukuwarung.edc.payments.data.model.OrderResponse
import com.bukuwarung.edc.payments.data.model.PaymentHistory
import com.bukuwarung.edc.payments.util.PaymentAuxilliary
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.getColorCompat
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.orDash
import com.bukuwarung.edc.util.orNil
import com.bukuwarung.edc.util.setDrawable
import com.bukuwarung.edc.util.setDrawableRightListener
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick

class OrderInfoView(context: Context, attrs: AttributeSet) : ConstraintLayout(context, attrs) {

    private val binding: OrderInfoViewBinding =
        OrderInfoViewBinding.inflate(LayoutInflater.from(context), this, true)

    fun setView(data: OrderResponse, paymentType: String?, action: () -> Unit) {
        val item = data.items?.firstOrNull()
        with(binding) {
            tvPaymentAmountValue.text = Utils.formatAmount(data.amount)
            tvTransactionCodeValue.text = data.transactionId

            when {
                PaymentAuxilliary.isPaymentOut(paymentType) -> {
                    tvTrxTypeInfoValue.text = data.customer?.customerName.orEmpty()
                    tvCustomerAccountValue.text =
                        "${item?.beneficiary?.code}-${item?.beneficiary?.accountNumber}"
                    grpCustomerAccount.showView()
                }

                PaymentAuxilliary.isSaldoIn(paymentType) -> {
                    tvPaymentAmount.text = context.getString(R.string.saldo_in)
                    tvTrxTypeInfo.text = context.getString(R.string.product)
                    tvTrxTypeInfoValue.text = context.getString(R.string.saldo_top_up)
                }

                PaymentAuxilliary.isSaldoOut(paymentType) -> {
                    tvPaymentAmount.text = context.getString(R.string.saldo_out)
                    tvTrxTypeInfo.hideView()
                    tvTrxTypeInfoValue.hideView()
                }

                PaymentAuxilliary.isSaldoRefund(paymentType) -> {
                    tvPaymentAmount.text = context.getString(R.string.saldo_in_refund)
                    tvTrxTypeInfo.text = context.getString(R.string.product)
                    tvTrxTypeInfoValue.text = context.getString(R.string.saldo_top_up)
                }

                PaymentAuxilliary.isPaymentSaldoCashback(paymentType) -> {
                    tvPaymentAmount.text = context.getString(R.string.saldo_in_refund)
                    tvTrxTypeInfo.text = context.getString(R.string.product)
                    tvTrxTypeInfoValue.text = context.getString(R.string.pay)
                    when {
                        PaymentAuxilliary.isCashbackIn(data) -> {
                            tvPaymentAmount.text = context.getString(R.string.saldo_bonus_in)
                            tvTransactionCodeValue.text = data.metadata?.transactionCode
                        }

                        PaymentAuxilliary.isCashbackOut(data) -> {
                            tvPaymentAmount.text = context.getString(R.string.saldo_bonus_out)
                            tvTransactionCodeValue.text = data.metadata?.transactionCode
                            data.metadata?.categoryDisplayName?.let {
                                if (it.isNotBlank()) {
                                    tvTrxTypeInfoValue.text = it
                                    tvTrxTypeInfoValue.setDrawable(
                                        0,
                                        R.drawable.ic_external_link,
                                        0,
                                        0
                                    )
                                    tvTrxTypeInfoValue.singleClick { action() }
                                }
                            }
                        }

                        else -> {
                            tvPaymentAmount.text = context.getString(R.string.cashback)
                            tvTransactionCode.text = context.getString(R.string.conversion_id)
                        }
                    }
                }

                PaymentAuxilliary.isPpob(paymentType) -> {
                    setStatusIcon(data)
                    tvPaymentAmount.text =
                        context.getString(PpobConst.CATEGORY_NAME_MAP[paymentType].orNil)
                    tvTrxTypeInfo.text = context.getString(R.string.product)
                    grpCustomerAccount.showView()
                    when (paymentType) {
                        PpobConst.CATEGORY_PULSA -> {
                            tvTrxTypeInfoValue.text = item?.name.orDash
                            tvCustomerAccount.text = context.getString(R.string.customer_phone)
                            tvCustomerAccountValue.text = item?.beneficiary?.phoneNumber.orDash
                            grpPpob1.hideView()
                            grpPpob2.hideView()
                        }

                        PpobConst.CATEGORY_LISTRIK -> {
                            tvTrxTypeInfoValue.text = item?.name.orDash
                            tvCustomerAccount.text = context.getString(R.string.customer_id_message)
                            tvCustomerAccountValue.text = item?.beneficiary?.accountNumber.orDash
                            tvPpob1Name.text = context.getString(R.string.nama_pelanggan)
                            tvPpob1Value.text = item?.details?.customerName.orDash
                            tvPpob2Name.text = context.getString(R.string.customer_phone)
                            tvPpob2Value.text = item?.beneficiary?.phoneNumber.orDash
                            grpPpob1.showView()
                            grpPpob2.showView()
                        }

                        PpobConst.CATEGORY_EWALLET -> {
                            tvTrxTypeInfoValue.text = item?.name.orDash
                            tvCustomerAccount.text = context.getString(R.string.nama_pelanggan)
                            tvCustomerAccountValue.text = item?.details?.customerName.orDash
                            tvPpob1Name.text = context.getString(R.string.customer_phone)
                            tvPpob1Value.text = item?.beneficiary?.phoneNumber.orDash
                            grpPpob1.showView()
                            grpPpob2.hideView()
                        }

                        PpobConst.CATEGORY_PAKET_DATA -> {
                            tvTrxTypeInfoValue.text = item?.name.orDash
                            tvCustomerAccount.text = context.getString(R.string.customer_phone)
                            tvCustomerAccountValue.text = item?.beneficiary?.phoneNumber.orDash
                            grpPpob1.hideView()
                            grpPpob2.hideView()
                        }

                        PpobConst.CATEGORY_PULSA_POSTPAID -> {
                            tvTrxTypeInfoValue.text = item?.details?.productName.orDash
                            tvCustomerAccount.text = context.getString(R.string.customer_phone)
                            tvCustomerAccountValue.text = item?.beneficiary?.phoneNumber.orDash
                            tvPpob1Name.text = context.getString(R.string.period)
                            tvPpob1Value.text = item?.details?.periode.orDash
                            grpPpob1.showView()
                            grpPpob2.hideView()
                        }

                        PpobConst.CATEGORY_LISTRIK_POSTPAID, PpobConst.CATEGORY_PLN_POSTPAID -> {
                            tvTrxTypeInfoValue.text = item?.beneficiary?.accountNumber.orDash
                            tvCustomerAccount.text = context.getString(R.string.nama_pelanggan)
                            tvCustomerAccountValue.text = item?.details?.customerName.orDash
                            tvPpob1Name.text = context.getString(R.string.period)
                            tvPpob1Value.text = item?.details?.periode.orDash
                            grpPpob1.showView()
                            grpPpob2.hideView()
                        }

                        PpobConst.CATEGORY_BPJS -> {
                            tvTrxTypeInfo.text = context.getString(R.string.card_number)
                            tvTrxTypeInfoValue.text = item?.details?.customerNumber.orDash
                            tvCustomerAccount.text = context.getString(R.string.nama_pelanggan)
                            tvCustomerAccountValue.text = item?.details?.customerName.orDash
                            tvPpob1Name.text = context.getString(R.string.period)
                            tvPpob1Value.text = item?.details?.periode.orDash
                            grpPpob1.showView()
                            grpPpob2.hideView()
                        }

                        PpobConst.CATEGORY_PDAM -> {
                            tvTrxTypeInfo.text = context.getString(R.string.pdam_area)
                            tvTrxTypeInfoValue.text = data.metadata?.billerName.orDash
                            tvCustomerAccount.text = context.getString(R.string.nama_pelanggan)
                            tvCustomerAccountValue.text = item?.details?.customerName.orDash
                            tvPpob1Name.text = context.getString(R.string.customer_number)
                            tvPpob1Value.text = item?.details?.customerNumber.orDash
                            tvPpob2Name.text = context.getString(R.string.period)
                            tvPpob2Value.text = item?.details?.periode.orDash
                            grpPpob1.showView()
                            grpPpob2.showView()
                        }

                        PpobConst.CATEGORY_INTERNET_DAN_TV_CABLE -> {
                            tvTrxTypeInfo.text = context.getString(R.string.provider)
                            tvTrxTypeInfoValue.text = data.metadata?.billerName.orDash
                            tvCustomerAccount.text = context.getString(R.string.nama_pelanggan)
                            tvCustomerAccountValue.text = item?.details?.customerName.orDash
                            tvPpob1Name.text = context.getString(R.string.customer_number)
                            tvPpob1Value.text = item?.details?.customerNumber.orDash
                            grpPpob1.showView()
                            grpPpob2.hideView()
                        }

                        PpobConst.CATEGORY_MULTIFINANCE -> {
                            tvTrxTypeInfo.text = context.getString(R.string.loan_provider)
                            tvTrxTypeInfoValue.text = item?.name.orDash
                            tvCustomerAccount.text = context.getString(R.string.contract_number)
                            tvCustomerAccountValue.text = item?.details?.customerNumber.orDash
                            tvPpob1Name.text = context.getString(R.string.nama_pelanggan)
                            tvPpob1Value.text = item?.details?.customerName.orDash
                            tvPpob2Name.text = context.getString(R.string.period)
                            tvPpob2Value.text = item?.details?.periode.orDash
                            grpPpob1.showView()
                            grpPpob2.showView()
                        }

                        PpobConst.CATEGORY_VEHICLE_TAX -> {
                            tvTrxTypeInfoValue.text = item?.name.orDash
                            tvCustomerAccount.text = context.getString(R.string.nama_pelanggan)
                            tvCustomerAccountValue.text = item?.details?.customerNumber.orDash
                            tvPpob1Name.text = context.getString(R.string.nama_pelanggan)
                            tvPpob1Value.text = item?.details?.customerName.orDash
                            tvPpob2Name.text = context.getString(R.string.policy_number)
                            tvPpob2Value.text = item?.details?.policyNumber.orDash
                            grpPpob1.showView()
                            grpPpob2.showView()
                        }
                    }
                }
            }

            tvTransactionCodeValue.setDrawableRightListener {
                Utils.copyToClipboard(
                    data.transactionId,
                    context,
                    context.getString(R.string.trx_no_copied)
                )
            }

            <EMAIL>()
        }
    }

    private fun setStatusIcon(data: OrderResponse) {
        when (data.status) {
            PaymentHistory.STATUS_COMPLETED -> {
                binding.tvPaymentAmountValue.setCompoundDrawablesRelativeWithIntrinsicBounds(
                    0,
                    0,
                    R.drawable.ic_progress_icon_active,
                    0
                )
                binding.tvPaymentAmountValue.setTextColor(context.getColorCompat(R.color.green_80))
            }

            PaymentHistory.STATUS_CANCELLED,
            PaymentHistory.STATUS_REJECTED,
            PaymentHistory.STATUS_FAILED,
            PaymentHistory.STATUS_EXPIRED -> {
                binding.tvPaymentAmountValue.setCompoundDrawablesRelativeWithIntrinsicBounds(
                    0,
                    0,
                    R.drawable.ic_progress_icon_failed,
                    0
                )
            }

            else -> {
                binding.tvPaymentAmountValue.setCompoundDrawablesRelativeWithIntrinsicBounds(
                    0,
                    0,
                    0,
                    0
                )
            }
        }
    }
}
