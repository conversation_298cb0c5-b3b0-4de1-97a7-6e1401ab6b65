package com.bukuwarung.edc.payments.util

import android.content.Context
import android.content.res.Resources
import android.graphics.Typeface
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.res.ResourcesCompat
import androidx.core.widget.TextViewCompat
import com.bukuwarung.edc.R
import com.bukuwarung.edc.card.data.model.CardReceiptResponse
import com.bukuwarung.edc.card.data.model.EndUserStatusValues
import com.bukuwarung.edc.card.domain.model.consts.Constants
import com.bukuwarung.edc.databinding.ItemDetailCenterInvoiceBinding
import com.bukuwarung.edc.databinding.ItemInvoiceBinding
import com.bukuwarung.edc.databinding.ItemInvoiceCenterBinding
import com.bukuwarung.edc.databinding.ItemInvoiceCodeBinding
import com.bukuwarung.edc.databinding.ItemInvoiceDividerBinding
import com.bukuwarung.edc.databinding.ItemInvoiceNotesBinding
import com.bukuwarung.edc.databinding.ItemInvoicePaymentCodeBinding
import com.bukuwarung.edc.databinding.LayoutTransactionStatusBinding
import com.bukuwarung.edc.global.enums.TransactionType
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.payments.data.model.OrderResponse
import com.bukuwarung.edc.payments.data.model.getCompletedStatusDate
import com.bukuwarung.edc.util.DateTimeUtils
import com.bukuwarung.edc.util.TextUtils
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.getColorCompat
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.isNotNullOrBlank
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.orDash
import com.bukuwarung.edc.util.orNil
import com.bukuwarung.edc.util.showView
import com.google.firebase.crashlytics.FirebaseCrashlytics
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.Locale

object OrderInvoiceUtils {

    private const val TYPE_PAYMENT_IN = "IN"
    private const val TYPE_PAYMENT_OUT = "OUT"
    private const val STATUS_COMPLETED = "COMPLETED"
    private const val CATEGORY_PULSA = "PULSA"
    private const val CATEGORY_LISTRIK = "LISTRIK" // To be used for category for Prepaid listrik
    private const val CATEGORY_PLN_POSTPAID = "PLN_POSTPAID"
    private const val CATEGORY_EWALLET = "EMONEY"
    private const val CATEGORY_PAKET_DATA = "PAKET_DATA"
    private const val CATEGORY_PULSA_POSTPAID = "PASCABAYAR"
    private const val CATEGORY_VOUCHER_GAME = "VOUCHER_GAME"
    private const val CATEGORY_BPJS = "BPJS"
    private const val CATEGORY_PDAM = "PDAM"
    private const val CATEGORY_MULTIFINANCE = "ANGSURAN"
    private const val CATEGORY_INTERNET_DAN_TV_CABLE = "INTERNET_DAN_TV_KABEL"
    private const val CATEGORY_VEHICLE_TAX = "VEHICLE_TAX"
    private const val CATEGORY_TRAIN_TICKET = "TRAIN_TICKET"
    private const val ADULT_TYPE = "A"
    private const val INFANT_TYPE = "I"

    private const val VOUCHER_TYPE_TOPUP = "TOPUP"

    fun addCardInvoiceDetailsToLayout(
        context: Context,
        cardReceiptResponse: CardReceiptResponse,
        viewGroupToAddViews: ViewGroup,
        parent: ViewGroup,
        pan: String,
        accountType: String,
        notes: String,
        cardType: Int = Constants.CARD_ENTRY_MODE_IC
    ) {
        viewGroupToAddViews.removeAllViews()
        val invoiceItems = getInvoiceItemsForCardReceipt(
            context = context,
            cardReceiptResponse = cardReceiptResponse,
            pan = pan,
            accountType = accountType,
            notes = notes,
            cardType = cardType
        )
        setLayoutUI(context, invoiceItems, viewGroupToAddViews, parent, true)
    }

    fun addInvoiceDetailToLayout(
        context: Context,
        order: OrderResponse,
        viewGroupToAddViews: ViewGroup,
        parent: ViewGroup,
        paymentType: String?
    ) {
        viewGroupToAddViews.removeAllViews()
        val invoiceItems = getInvoiceItemsForCategory(
            context = context,
            order = order,
            paymentType = paymentType
        )
        setLayoutUI(context, invoiceItems, viewGroupToAddViews, parent, false)
    }

    private fun setLayoutUI(
        context: Context,
        invoiceItems: ArrayList<InvoiceItem>,
        viewGroupToAddViews: ViewGroup,
        parent: ViewGroup,
        isCardLayout: Boolean
    ) {
        invoiceItems.forEach {
            // If hideIfMissing is true, we don't add it to the view
            if ((it.value.isNullOrBlank() || it.value == "-") && it.hideIfMissing.isTrue) {
                return@forEach
            }
            when (it.type) {
                InvoiceItemType.ITEM_TITLE -> {
                    val itemDetailView = ItemInvoiceBinding.inflate(
                        LayoutInflater.from(context),
                        parent,
                        false
                    ).apply {
                        tvKey.text = it.label
                        tvValue.text = it.value.orDash
                        if (it.value.isNotNullOrBlank()) {
                            guideline.setGuidelinePercent(1F)
                        }
                    }
                    decorItemDetailView(itemDetailView, it)
                    itemDetailView.root.visibility = it.viewVisibility
                    viewGroupToAddViews.addView(itemDetailView.root)
                }

                InvoiceItemType.ITEM_CENTER -> {
                    val itemDetailView = ItemInvoiceCenterBinding.inflate(
                        LayoutInflater.from(context),
                        parent,
                        false
                    ).apply {
                        tvValue.text = it.value
                    }
                    itemDetailView.root.visibility = it.viewVisibility
                    viewGroupToAddViews.addView(itemDetailView.root)
                }

                InvoiceItemType.ITEM_DETAIL_CENTER -> {
                    val itemDetailView = ItemDetailCenterInvoiceBinding.inflate(
                        LayoutInflater.from(context),
                        parent,
                        false
                    ).apply {
                        tvTitle.text = it.label
                        tvValue.text = it.value
                    }
                    itemDetailView.root.visibility = it.viewVisibility
                    viewGroupToAddViews.addView(itemDetailView.root)
                }

                InvoiceItemType.ITEM_DETAIL -> {
                    val itemDetailView = ItemInvoiceBinding.inflate(
                        LayoutInflater.from(context),
                        parent,
                        false
                    ).apply {
                        tvKey.text = it.label
                        tvValue.text = it.value.orDash
                        if (isCardLayout) {
                            tvValue.gravity = Gravity.START
                            guideline.setGuidelinePercent(0.4F)
                        }
                    }
                    decorItemDetailView(itemDetailView, it)
                    it.viewTag?.let { viewTag ->
                        itemDetailView.tvValue.tag = viewTag
                        itemDetailView.root.tag = "${viewTag}_parent"
                        if (viewTag == PaymentConst.TRX_CODE_VIEW_TAG) {
                            TextViewCompat.setAutoSizeTextTypeWithDefaults(
                                itemDetailView.tvValue,
                                TextViewCompat.AUTO_SIZE_TEXT_TYPE_UNIFORM
                            )
                            itemDetailView.tvValue.maxLines = 1
                        }
                    }
                    itemDetailView.root.visibility = it.viewVisibility
                    viewGroupToAddViews.addView(itemDetailView.root)
                }

                InvoiceItemType.DIVIDER -> {
                    val dividerView = ItemInvoiceDividerBinding.inflate(
                        LayoutInflater.from(context),
                        parent,
                        false
                    )
                    decorDividerView(dividerView, it)
                    it.viewTag?.let { viewTag ->
                        dividerView.root.tag = viewTag
                    }
                    dividerView.root.visibility = it.viewVisibility
                    viewGroupToAddViews.addView(dividerView.root)
                }

                InvoiceItemType.CODE_INFO -> {
                    val codeInfoView = ItemInvoiceCodeBinding.inflate(
                        LayoutInflater.from(context),
                        parent,
                        false
                    ).apply {
                        tvCodeLabel.text = it.label
                        tvCodeValue.text = it.value.orDash
                        tvCodeCopy.visibility = (it.value.isNotNullOrBlank()).asVisibility()
                        tvCodeCopy.setOnClickListener { _ ->
                            Utils.copyToClipboard(it.value, context, "")
                        }
                    }
                    decorCodeInfoView(codeInfoView, it)
                    viewGroupToAddViews.addView(codeInfoView.root)
                }

                InvoiceItemType.PAYMENT_CODE_INFO -> {
                    val paymentCodeInfoView = ItemInvoicePaymentCodeBinding.inflate(
                        LayoutInflater.from(context),
                        parent,
                        false
                    ).apply {
                        tvCodeLabel.text = it.heading
                        tvAccountName.apply {
                            if (it.label.isNotNullOrBlank()) {
                                text = it.label
                            } else {
                                hideView()
                            }
                        }
                        tvAccountNumber.text = it.value.orDash
                        it.statusIcon?.let { statusIcon -> ivStatus.setImageResource(statusIcon) }
                        it.statusWording?.let { statusWording ->
                            tvStatus.text = statusWording
                            grpStatus.showView()
                        }
                        if (it.isCardTransaction) {
                            tvMount.text = Utils.formatAmount(it.amount)
                            tvMount.showView()
                        }
                        tvPendingInfoGuarantee.hideView()
                        it.guaranteeMessage?.let { guaranteeMessage ->
                            tvPendingInfoGuarantee.text = guaranteeMessage
                            tvPendingInfoGuarantee.showView()
                        }
                    }
                    decorPaymentCodeInfoView(paymentCodeInfoView, it)
                    viewGroupToAddViews.addView(paymentCodeInfoView.root)
                }

                InvoiceItemType.PAYMENT_CODE_TRANSFER -> {
                    val showDestination =
                        listOf("TRANSFER_POSTING", "POSTING").contains(it.transactionType)
                    val paymentCodeInfoView = LayoutTransactionStatusBinding.inflate(
                        LayoutInflater.from(context),
                        parent,
                        false
                    ).apply {
                        tvCodeLabel.text = it.heading
                        tvAccountName.apply {
                            if (it.label.isNotNullOrBlank()) {
                                text = it.label
                            } else {
                                hideView()
                            }
                        }
                        tvAccountNumber.text = it.value.orDash
                        it.statusIcon?.let { statusIcon -> ivStatus.setImageResource(statusIcon) }
                        it.statusWording?.let { statusWording ->
                            tvStatus.text = statusWording
                            grpStatus.showView()
                        }
                        if (it.isCardTransaction) {
                            tvMount.text = Utils.formatAmount(it.amount)
                            tvMount.showView()
                        }
                        tvPendingInfoGuarantee.hideView()
                        it.guaranteeMessage?.let { guaranteeMessage ->
                            tvPendingInfoGuarantee.text = guaranteeMessage
                            tvPendingInfoGuarantee.showView()
                        }
                        clInvoiceCode.visibility = showDestination.asVisibility()
                    }
                    decorPaymentCodeTransferView(paymentCodeInfoView, it)
                    viewGroupToAddViews.addView(paymentCodeInfoView.root)
                }

                InvoiceItemType.NOTES -> {
                    val notesView = ItemInvoiceNotesBinding.inflate(
                        LayoutInflater.from(context),
                        parent,
                        false
                    )
                    decorNotesView(notesView, it, context)
                    viewGroupToAddViews.addView(notesView.root)
                }

                null -> {}
            }
        }
    }

    private val Int.dp: Int
        get() = (this * Resources.getSystem().displayMetrics.density + 0.5f).toInt()

    private fun decorItemDetailView(itemView: ItemInvoiceBinding, item: InvoiceItem) {
        item.margins.let {
            (itemView.root.layoutParams as? LinearLayout.LayoutParams)?.let { layoutParams ->
                layoutParams.setMargins(
                    it.left.orNil.dp,
                    it.top.orNil.dp,
                    it.right.orNil.dp,
                    it.bottom.orNil.dp
                )
                itemView.root.layoutParams = layoutParams
            }
        }
        item.labelTextDecor?.let {
            setTextStyling(itemView.tvKey, it)
        }
        item.valueTextDecor?.let {
            setTextStyling(itemView.tvValue, it)
        }
    }

    private fun decorDividerView(itemView: ItemInvoiceDividerBinding, item: InvoiceItem) {
        item.margins.let {
            (itemView.root.layoutParams as? LinearLayout.LayoutParams)?.let { layoutParams ->
                layoutParams.setMargins(
                    it.left.orNil.dp,
                    it.top.orNil.dp,
                    it.right.orNil.dp,
                    it.bottom.orNil.dp
                )
                itemView.root.layoutParams = layoutParams
            }
        }
    }

    private fun decorNotesView(
        itemView: ItemInvoiceNotesBinding,
        item: InvoiceItem,
        context: Context
    ) {
        item.margins.let {
            (itemView.root.layoutParams as? LinearLayout.LayoutParams)?.let { layoutParams ->
                layoutParams.setMargins(
                    it.left.orNil.dp,
                    it.top.orNil.dp,
                    it.right.orNil.dp,
                    it.bottom.orNil.dp
                )
                itemView.root.layoutParams = layoutParams
            }
        }
        itemView.tvValue.text = if (item.heading != null) {
            TextUtils.decorateTextString(
                "${item.heading}\n${item.value}",
                hashMapOf(
                    "${item.heading}\n" to TextUtils.TextDecorations(
                        textColor = context.getColorCompat(R.color.black_40)
                    ),
                    item.value.orDash to TextUtils.TextDecorations(
                        textColor = context.getColorCompat(R.color.black_60)
                    )
                )
            )
        } else {
            item.value.orDash
        }
    }

    private fun decorCodeInfoView(itemView: ItemInvoiceCodeBinding, item: InvoiceItem) {
        item.margins.let {
            (itemView.root.layoutParams as? LinearLayout.LayoutParams)?.let { layoutParams ->
                layoutParams.setMargins(
                    it.left.orNil.dp,
                    it.top.orNil.dp,
                    it.right.orNil.dp,
                    it.bottom.orNil.dp
                )
                itemView.root.layoutParams = layoutParams
            }
        }
        item.labelTextDecor?.let {
            setTextStyling(itemView.tvCodeLabel, it)
        }
        item.valueTextDecor?.let {
            setTextStyling(itemView.tvCodeValue, it)
        }
    }

    private fun decorPaymentCodeInfoView(
        itemView: ItemInvoicePaymentCodeBinding,
        item: InvoiceItem
    ) {
        item.margins.let {
            (itemView.root.layoutParams as? LinearLayout.LayoutParams)?.let { layoutParams ->
                layoutParams.setMargins(
                    it.left.orNil.dp,
                    it.top.orNil.dp,
                    it.right.orNil.dp,
                    it.bottom.orNil.dp
                )
                itemView.root.layoutParams = layoutParams
            }
        }
        item.labelTextDecor?.let {
            setTextStyling(itemView.tvAccountName, it)
        }
        item.valueTextDecor?.let {
            setTextStyling(itemView.tvAccountNumber, it)
        }
    }

    private fun decorPaymentCodeTransferView(
        itemView: LayoutTransactionStatusBinding,
        item: InvoiceItem
    ) {
        item.margins.let {
            (itemView.root.layoutParams as? LinearLayout.LayoutParams)?.let { layoutParams ->
                layoutParams.setMargins(
                    it.left.orNil.dp,
                    it.top.orNil.dp,
                    it.right.orNil.dp,
                    it.bottom.orNil.dp
                )
                itemView.root.layoutParams = layoutParams
            }
        }
        item.labelTextDecor?.let {
            setTextStyling(itemView.tvAccountName, it)
        }
        item.valueTextDecor?.let {
            setTextStyling(itemView.tvAccountNumber, it)
        }
    }

    private fun setTextStyling(textView: TextView, textDecor: TextDecor) {
        textDecor.textColor?.let { textColor ->
            textView.setTextColor(textColor)
        }
        textDecor.fontSize?.let { fontSize ->
            textView.textSize = fontSize.toFloat()
        }
        textDecor.textStyle?.let { textStyle ->
            when (textStyle) {
                TextStyle.BOLD ->
                    textView.typeface =
                        ResourcesCompat.getFont(textView.context, R.font.roboto_bold)

                TextStyle.ITALIC -> textView.setTypeface(null, Typeface.ITALIC)
            }
        }
    }

    inline fun <T1 : Any, T2 : Any, R : Any> safeLet(p1: T1?, p2: T2?, block: (T1, T2) -> R?): R? =
        if (p1 != null && p2 != null) block(p1, p2) else null

    private fun beautifyPhoneNumber(phone: String): String? {
        var phoneNumber = phone
        return try {
            if (phoneNumber.isEmpty()) return ""
            if (phoneNumber.startsWith("0") && phoneNumber.length > 8) {
                return phoneNumber
            } else if (phoneNumber.startsWith("+62")) {
                phoneNumber = phoneNumber.replace("+62", "0")
            } else if (phoneNumber.length > 8) {
                phoneNumber = "0$phoneNumber"
            }
            phoneNumber
        } catch (ex: Exception) {
            ex.printStackTrace()
            FirebaseCrashlytics.getInstance().log(ex.message!!)
            phoneNumber
        }
    }

    private fun formatReceiptDateFromHistory(str: String?, withTime: Boolean): String? {
        if (str == null || str === "-") {
            return str
        }
        val simpleDateFormat = SimpleDateFormat("dd MMMM yyyy, HH:mm", Locale.getDefault())
        return try {
            if (withTime) {
                simpleDateFormat.parse(str)?.let {
                    SimpleDateFormat(
                        "dd MMM yyyy HH:mm",
                        Locale.getDefault()
                    ).format(it)
                }
            } else {
                simpleDateFormat.parse(
                    str
                )?.let {
                    SimpleDateFormat("dd MMM yyyy", Locale.getDefault()).format(
                        it
                    )
                }
            }
        } catch (e: ParseException) {
            FirebaseCrashlytics.getInstance().recordException(e)
            str
        }
    }

    private fun getInvoiceItemsForCardReceipt(
        context: Context,
        cardReceiptResponse: CardReceiptResponse,
        pan: String,
        accountType: String,
        notes: String,
        cardType: Int = Constants.CARD_ENTRY_MODE_IC
    ): ArrayList<InvoiceItem> {
        val accountTypeTranslation = when (accountType) {
            PaymentConst.TYPE_SAVINGS -> context.getString(R.string.savings_debt)
            PaymentConst.TYPE_CHECKING -> context.getString(R.string.checking_debt)
            else -> ""
        }
        val invoiceItems = arrayListOf<InvoiceItem>()

        var date = cardReceiptResponse.transactionDate
        if (date != null && date.contains("/")) {
            date = date.replace("/", "-")
        }
        invoiceItems.add(
            InvoiceItem(
                label = context.getString(R.string.time),
                value = DateTimeUtils.getFormattedLocalDateTime(
                    date.orEmpty(),
                    DateTimeUtils.DD_MMM_YYYY_HH_MM
                )
            )
        )

        invoiceItems.add(
            InvoiceItem(
                label = context.getString(R.string.terminal_id),
                value = cardReceiptResponse.terminalId
            )
        )
        invoiceItems.add(
            InvoiceItem(
                label = context.getString(R.string.merchant_id),
                value = cardReceiptResponse.merchantId
            )
        )

        val responseCode = cardReceiptResponse.responseCode
        invoiceItems.add(
            InvoiceItem(
                label = "Trace/RC",
                value = "${cardReceiptResponse.systemTraceAuditNumber}/$responseCode"
            )
        )

        invoiceItems.add(
            InvoiceItem(
                label = context.getString(R.string.reference_number),
                value = cardReceiptResponse.rrn
            )
        )

        invoiceItems.add(InvoiceItem(type = InvoiceItemType.DIVIDER))

        invoiceItems.add(
            InvoiceItem(
                label = context.getString(R.string.card_type),
                value = accountTypeTranslation
            )
        )

        val cardEntry = if (cardType == Constants.CARD_ENTRY_MODE_MAG) {
            "Magnetic"
        } else {
            "Chip"
        }
        invoiceItems.add(
            InvoiceItem(
                label = cardEntry,
                value = pan
            )
        )

        if (cardReceiptResponse.sourceDetails == null) {
            invoiceItems.add(
                InvoiceItem(
                    type = InvoiceItemType.ITEM_DETAIL_CENTER,
                    label = context.getString(R.string.filter_history_saldo),
                    value = Utils.formatAmount(
                        cardReceiptResponse.balanceInformation?.balance?.toDouble()
                    )
                )
            )
        } else {
            invoiceItems.add(
                InvoiceItem(
                    label = context.getString(R.string.bank_source),
                    value = "${cardReceiptResponse.sourceDetails?.bankName}-${
                        Utils.maskSensitiveInfo(
                            cardReceiptResponse.sourceDetails?.name.orEmpty()
                        )
                    }-${
                        Utils.maskSensitiveInfo(
                            cardReceiptResponse.sourceDetails?.accountNumber.orEmpty()
                        )
                    }"
                )
            )
            var statusWording: String? = null
            var statusIcon: Int? = null
            var guaranteeMessage: String? = null
            when (cardReceiptResponse.endUserStatus) {
                EndUserStatusValues.SUCCESS -> {
                    statusWording = "Transfer Berhasil"
                    statusIcon = R.drawable.ic_check_black_bg
                }

                EndUserStatusValues.PENDING_SETTLEMENT -> {
                    statusWording = "Pencairan Diproses"
                    statusIcon = R.drawable.ic_hourglass_black
                    guaranteeMessage = context.getString(R.string.disbursement_pending_message)
                }

                EndUserStatusValues.PENDING_REFRESH -> {
                    statusWording = "Transaksi Pending"
                    statusIcon = R.drawable.ic_hourglass_black
                    guaranteeMessage = context.getString(R.string.transaction_pending_message)
                }

                EndUserStatusValues.PENDING -> {
                    statusWording =
                        if (cardReceiptResponse.transactionType ==
                            TransactionType.TRANSFER_POSTING.type ||
                            cardReceiptResponse.transactionType == "POSTING"
                        ) {
                            "Transaksi Pending"
                        } else {
                            "Pending"
                        }
                    statusIcon = R.drawable.ic_hourglass_black
                    guaranteeMessage = context.getString(R.string.transaction_pending_message)
                }
            }
            val isCardTransaction = listOf(
                "BALANCE_INQUIRY",
                "CASH_WITHDRAWAL",
                "CASH_WITHDRAWAL_POSTING",
                "TRANSFER_POSTING",
                "POSTING"
            ).contains(cardReceiptResponse.transactionType)
            val details = cardReceiptResponse.destDetails
            invoiceItems.add(
                InvoiceItem(
                    type = InvoiceItemType.PAYMENT_CODE_TRANSFER,
                    label = details?.name,
                    value = "${details?.bankName.orEmpty()} - ${details?.accountNumber.orEmpty()}",
                    heading = context.getString(R.string.bank_destination),
                    statusWording = statusWording,
                    statusIcon = statusIcon,
                    responseCode = responseCode,
                    amount = cardReceiptResponse.amount,
                    transactionType = cardReceiptResponse.transactionType,
                    guaranteeMessage = guaranteeMessage,
                    isCardTransaction = isCardTransaction
                )
            )
        }
        if (notes.isNotNullOrBlank()) {
            invoiceItems.add(InvoiceItem(type = InvoiceItemType.DIVIDER))
            invoiceItems.add(
                InvoiceItem(
                    label = context.getString(R.string.news),
                    value = notes
                )
            )
        }

        return invoiceItems
    }

    private fun getInvoiceItemsForCategory(
        context: Context,
        order: OrderResponse,
        paymentType: String?
    ): ArrayList<InvoiceItem> {
        val item = order.items?.firstOrNull()
        val details = item?.details
        val customerName = details?.customerName
        val customerPhone = details?.customerNumber
        val invoiceItems = arrayListOf<InvoiceItem>()
        var customer = customerName
        safeLet(customerName, customerPhone) { name, phone ->
            customer = "${name.ifEmpty { "-" }}\n$phone"
        }
        val customerLabel: String
        val customerValue: String
        val businessPhone = Utils.getPhoneNumber()
        val businessName = Utils.getBusinessName()
        when (paymentType) {
            TYPE_PAYMENT_IN -> {
                customerLabel = context.getString(R.string.name_of_recepient)
                customerValue = if (businessName.isNotNullOrBlank()) {
                    "${businessName}\n${beautifyPhoneNumber(businessPhone)}"
                } else {
                    "${beautifyPhoneNumber(businessPhone)}"
                }
            }

            TYPE_PAYMENT_OUT -> {
                customerLabel = context.getString(R.string.name_of_sender)
                customerValue = businessName
            }

            else -> {
                customerLabel = context.getString(R.string.label_customer)
                customerValue = customer.orDash
            }
        }
        // Adding common fields for all invoices
        invoiceItems.add(
            InvoiceItem(
                label = context.getString(R.string.date),
                value = formatReceiptDateFromHistory(
                    order.getCompletedStatusDate(),
                    true
                )
            )
        )
        invoiceItems.add(
            InvoiceItem(
                label = context.getString(R.string.payment_code),
                value = order.transactionId,
                viewTag = PaymentConst.TRX_CODE_VIEW_TAG
            )
        )
        invoiceItems.add(
            InvoiceItem(
                label = customerLabel,
                value = customerValue
            )
        )
        invoiceItems.add(InvoiceItem(type = InvoiceItemType.DIVIDER))

        val code = item?.beneficiary?.code
        val accountNumber = item?.beneficiary?.accountNumber
        when (item?.beneficiary?.category.orEmpty()) {
            CATEGORY_LISTRIK -> {
                if (code == CATEGORY_PLN_POSTPAID) {
                    // Listrik Postpaid
                    invoiceItems.add(
                        InvoiceItem(
                            label = context.getString(R.string.customer_id_message),
                            value = item.beneficiary.accountNumber.orDash,
                            margins = Margin(top = 8)
                        )
                    )
                    invoiceItems.add(
                        InvoiceItem(
                            label = context.getString(R.string.period),
                            value = item.details.periode.orDash
                        )
                    )
                    invoiceItems.add(
                        InvoiceItem(
                            label = context.getString(R.string.total_billing),
                            value = item.details.totalLembarTagihan.orDash
                        )
                    )
                    invoiceItems.add(
                        InvoiceItem(
                            label = context.getString(R.string.tarif),
                            value = item.details.tarif.orDash
                        )
                    )
                    invoiceItems.add(
                        InvoiceItem(
                            label = context.getString(R.string.total_bill),
                            value = Utils.formatAmount(order.amount)
                        )
                    )
                } else {
                    // Token Listrik
                    invoiceItems.add(
                        InvoiceItem(
                            label = context.getString(R.string.token_listrik),
                            value = item?.name.orDash,
                            margins = Margin(top = 8)
                        )
                    )
                    invoiceItems.add(
                        InvoiceItem(
                            label = context.getString(R.string.customer_id_message),
                            value = accountNumber.orDash
                        )
                    )
                    invoiceItems.add(
                        InvoiceItem(
                            label = context.getString(R.string.nama_pelanggan),
                            value = details?.customerName.orDash
                        )
                    )
                    invoiceItems.add(
                        InvoiceItem(
                            label = context.getString(R.string.tarif),
                            value = details?.tarif.orDash
                        )
                    )
                    invoiceItems.add(
                        InvoiceItem(
                            label = context.getString(R.string.total_kwh),
                            value = details?.totalKwh.orDash
                        )
                    )
                    invoiceItems.add(
                        InvoiceItem(
                            type = InvoiceItemType.CODE_INFO,
                            label = context.getString(R.string.token_code),
                            value = details?.token.orDash
                        )
                    )
                }
            }

            CATEGORY_PULSA -> {
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.nomor_hp),
                        value = item?.beneficiary?.phoneNumber.orDash,
                        margins = Margin(top = 8)
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.pulsa),
                        value = item?.name.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.serial_number),
                        value = details?.serialNumber.orDash
                    )
                )
            }

            CATEGORY_EWALLET -> {
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.nomor_hp),
                        value = accountNumber.orDash,
                        margins = Margin(top = 8)
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.nama_pelanggan),
                        value = details?.customerName.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.provider),
                        value = details?.billerName.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.nominal_topup),
                        value = item?.name.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.serial_number),
                        value = details?.serialNumber.orDash
                    )
                )
            }

            CATEGORY_PULSA_POSTPAID -> {
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.operator),
                        value = details?.productName.orDash,
                        margins = Margin(top = 8)
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.nomor_hp),
                        value = item?.beneficiary?.phoneNumber.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.nama_pelanggan),
                        value = details?.customerName.orDash
                    )
                )
            }

            CATEGORY_PAKET_DATA -> {
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.nomor_hp),
                        value = item?.beneficiary?.phoneNumber.orDash,
                        margins = Margin(top = 8)
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.packet_data),
                        value = item?.name.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.serial_number),
                        value = details?.serialNumber.orDash
                    )
                )
            }

            CATEGORY_PDAM -> {
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.title_pdam),
                        value = order.metadata?.billerName.orDash,
                        margins = Margin(top = 8)
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.nomor_hp),
                        value = details?.customerNumber.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.nama_pelanggan),
                        value = details?.customerName.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.period),
                        value = details?.period.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.total_bill),
                        value = Utils.formatAmount(order.amount)
                    )
                )
            }

            CATEGORY_BPJS -> {
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.card_number),
                        value = details?.customerNumber.orDash,
                        margins = Margin(top = 8)
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.nama_pelanggan),
                        value = details?.customerName.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.no_of_family),
                        value = details?.memberCount.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.period),
                        value = details?.period.orDash
                    )
                )
            }

            CATEGORY_VEHICLE_TAX -> {
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.nama_pelanggan),
                        value = details?.customerName.orDash,
                        margins = Margin(top = 8)
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.nomor_hp),
                        value = details?.phoneNumber.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.policy_number),
                        value = details?.policyNumber.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.vehicle_brand),
                        value = details?.vehicleBrand.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.period),
                        value = details?.period.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.transportation_type),
                        value = details?.vehicleName.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.vehicle_type),
                        value = details?.vehicleType.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.vehicle_colour),
                        value = details?.vehicleColor.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.vehicle_build_year),
                        value = details?.buildYear.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.machine_number),
                        value = details?.machineNumber.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.frame_number),
                        value = details?.frameNumber.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.pkb),
                        value = details?.pkb.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.total_bill),
                        value = Utils.formatAmount(item?.amount.orNil)
                    )
                )
            }

            CATEGORY_MULTIFINANCE -> {
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.contact_number),
                        value = details?.customerNumber.orDash,
                        margins = Margin(top = 8)
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.nama_pelanggan),
                        value = details?.customerName.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.installment_product),
                        value = order.metadata?.billerName.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.period),
                        value = details?.period.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.fine),
                        value = details?.fine.orDash
                    )
                )
            }

            CATEGORY_INTERNET_DAN_TV_CABLE -> {
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.nama_pelanggan),
                        value = details?.customerName.orDash,
                        margins = Margin(top = 8)
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.provider),
                        value = order.metadata?.billerName.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.period),
                        value = details?.period.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.total_bill),
                        value = Utils.formatAmount(order.amount)
                    )
                )
            }

            CATEGORY_TRAIN_TICKET -> {
                val numberOfAdult =
                    details?.trainPassenger?.filter { passenger ->
                        passenger.type ==
                            ADULT_TYPE
                    }?.size.orNil
                val numberOfChild =
                    details?.trainPassenger?.filter { passenger ->
                        passenger.type ==
                            INFANT_TYPE
                    }?.size.orNil

                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.route_label),
                        value = buildString {
                            append(details?.trainOriginStationCode)
                            append(" - ")
                            append(details?.trainDestinationStationCode)
                        },
                        margins = Margin(top = 8)
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.nama_pelanggan),
                        value = details?.customerName.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.nomor_hp),
                        value = details?.phoneNumber.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.e_mail_label),
                        value = details?.customerEmail.orDash
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.passenger),
                        value = if (numberOfChild > 0) {
                            "$numberOfAdult ${context.getString(R.string.adult)}, $numberOfChild ${
                                context.getString(
                                    R.string.baby
                                )
                            }"
                        } else {
                            "$numberOfAdult ${context.getString(R.string.adult)}"
                        }
                    )
                )
                invoiceItems.add(InvoiceItem(type = InvoiceItemType.DIVIDER))
                invoiceItems.add(
                    InvoiceItem(
                        type = InvoiceItemType.NOTES,
                        label = context.getString(R.string.notes),
                        value = context.getString(R.string.code_booking_info)
                    )
                )
                invoiceItems.add(
                    InvoiceItem(
                        type = InvoiceItemType.CODE_INFO,
                        label = context.getString(R.string.code_booking),
                        value = details?.token.orDash
                    )
                )
            }

            CATEGORY_VOUCHER_GAME -> {
                if (order.metadata?.billerType.isNotNullOrBlank() &&
                    order.metadata?.billerType == VOUCHER_TYPE_TOPUP
                ) {
                    invoiceItems.add(
                        InvoiceItem(
                            label = context.getString(R.string.voucher_type),
                            value = order.metadata.billerName.orDash,
                            margins = Margin(top = 8)
                        )
                    )
                    invoiceItems.add(
                        InvoiceItem(
                            label = context.getString(R.string.produk),
                            value = item?.name.orDash
                        )
                    )
                    invoiceItems.add(
                        InvoiceItem(
                            label = order.metadata.idFieldName.orDash,
                            value = order.metadata.idFieldValue.orDash
                        )
                    )
                    invoiceItems.add(InvoiceItem(type = InvoiceItemType.DIVIDER))
                    invoiceItems.add(
                        InvoiceItem(
                            type = InvoiceItemType.NOTES,
                            label = context.getString(R.string.notes),
                            value = context.getString(R.string.voucher_code_info)
                        )
                    )
                    invoiceItems.add(
                        InvoiceItem(
                            type = InvoiceItemType.NOTES,
                            label = context.getString(R.string.notes),
                            value = order.metadata.infoboxText.orDash,
                            margins = Margin(top = 0),
                            hideIfMissing = true
                        )
                    )
                    invoiceItems.add(
                        InvoiceItem(
                            type = InvoiceItemType.CODE_INFO,
                            label = context.getString(R.string.code_voucher),
                            value = details?.voucherCode.orDash
                        )
                    )
                } else {
                    invoiceItems.add(
                        InvoiceItem(
                            label = context.getString(R.string.voucher_type),
                            value = order.metadata?.billerName.orDash,
                            margins = Margin(top = 8)
                        )
                    )
                    invoiceItems.add(
                        InvoiceItem(
                            label = context.getString(R.string.produk),
                            value = item?.name.orDash
                        )
                    )
                    invoiceItems.add(
                        InvoiceItem(
                            label = context.getString(R.string.nomor_hp),
                            value = item?.beneficiary?.phoneNumber.orDash
                        )
                    )
                    invoiceItems.add(InvoiceItem(type = InvoiceItemType.DIVIDER))
                    invoiceItems.add(
                        InvoiceItem(
                            type = InvoiceItemType.NOTES,
                            label = context.getString(R.string.notes),
                            value = context.getString(R.string.voucher_code_info)
                        )
                    )
                    invoiceItems.add(
                        InvoiceItem(
                            type = InvoiceItemType.CODE_INFO,
                            label = context.getString(R.string.code_voucher),
                            value = details?.voucherCode.orDash
                        )
                    )
                }
            }
            // payment in and out case
            else -> {
                if (paymentType == TYPE_PAYMENT_IN) {
                    invoiceItems.add(
                        InvoiceItem(
                            label = context.getString(R.string.receipient_detail),
                            value = "$code - $accountNumber\n${item?.beneficiary?.name}"
                        )
                    )
                    val value = if (Utils.getBeneficiaryName().isNotNullOrBlank()) {
                        "${
                            order.payments?.getOrNull(
                                0
                            )?.paymentMethod?.code
                        } - ${Utils.getBeneficiaryName()}"
                    } else {
                        "${order.payments?.getOrNull(0)?.paymentMethod?.code}"
                    }
                    invoiceItems.add(
                        InvoiceItem(
                            type = InvoiceItemType.PAYMENT_CODE_INFO,
                            label = Utils.getBeneficiaryName(),
                            value = value,
                            heading = context.getString(R.string.sender_account)
                        )
                    )
                } else if (paymentType == TYPE_PAYMENT_OUT) {
                    invoiceItems.add(
                        InvoiceItem(
                            label = context.getString(R.string.sender_detail),
                            value = "${
                                order.payments?.getOrNull(
                                    0
                                )?.paymentMethod?.code
                            } - $businessPhone"
                        )
                    )
                    invoiceItems.add(
                        InvoiceItem(
                            type = InvoiceItemType.PAYMENT_CODE_INFO,
                            label = item?.beneficiary?.name,
                            value = "$code - $accountNumber",
                            heading = context.getString(R.string.destination_account)
                        )
                    )
                }
            }
        }
        val amount = order.paymentCollectionInfo?.paymentCollectionCashTransactionInfo?.amount
        val sellingPrice = when {
            amount.orNil > 0.0 -> amount
            item?.sellingPrice.orNil > 0.0 -> item?.sellingPrice
            (paymentType == TYPE_PAYMENT_OUT) -> order.amount?.minus(item?.discountedFee.orNil)
                ?.plus(order.loyalty?.tierDiscount.orNil)

            else -> order.amount
        }
        var totalAmount = sellingPrice
        if (isPaymentInOrOut(paymentType) && order.transactionType == PaymentConst.RECORD_IN_CASH) {
            val visibility = (order.agentFeeInfo?.amount.orNil != 0.0).asVisibility()
            invoiceItems.add(
                InvoiceItem(
                    type = InvoiceItemType.DIVIDER,
                    viewTag = PaymentConst.SERVICE_FEE_DIVIDER_VIEW_TAG,
                    viewVisibility = visibility
                )
            )
            if (paymentType == TYPE_PAYMENT_OUT) {
                // Jumlah Pembayaran
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.payment_amount),
                        value = Utils.formatAmount(sellingPrice),
                        viewTag = PaymentConst.PAYMENT_AMOUNT_VIEW_TAG,
                        viewVisibility = visibility
                    )
                )
                totalAmount = getTotalAmountForPaymentOut(order)
            } else {
                invoiceItems.add(
                    InvoiceItem(
                        label = context.getString(R.string.bill_amount),
                        value = Utils.formatAmount(getBillAmountForPaymentIn(order)),
                        viewTag = PaymentConst.BILL_AMOUNT_VIEW_TAG,
                        viewVisibility = visibility
                    )
                )
            }
            // Biaya Layanan
            invoiceItems.add(
                InvoiceItem(
                    label = context.getString(R.string.service_fee),
                    value = Utils.formatAmount(order.agentFeeInfo?.amount),
                    viewTag = PaymentConst.SERVICE_FEE_VIEW_TAG,
                    viewVisibility = visibility
                )
            )
        }
        invoiceItems.add(InvoiceItem(type = InvoiceItemType.DIVIDER))
        invoiceItems.add(
            InvoiceItem(
                label = context.getString(R.string.label_total_payment),
                value = Utils.formatAmount(totalAmount),
                margins = Margin(top = 8),
                labelTextDecor = TextDecor(
                    textColor = context.getColorCompat(R.color.black),
                    textStyle = TextStyle.BOLD,
                    fontSize = 16
                ),
                valueTextDecor = TextDecor(
                    textColor = context.getColorCompat(R.color.black),
                    textStyle = TextStyle.BOLD,
                    fontSize = 16
                ),
                viewTag = PaymentConst.TOTAL_PAYMENT_VIEW_TAG
            )
        )

        if (isPaymentInOrOut(paymentType)) {
            invoiceItems.add(
                InvoiceItem(
                    type = InvoiceItemType.NOTES,
                    label = context.getString(R.string.additional_notes),
                    value = order.description?.trim().orEmpty(),
                    hideIfMissing = true,
                    heading = context.getString(R.string.notes),
                    margins = Margin(top = 24)
                )
            )
        }

        return invoiceItems
    }

    private fun getTotalAmountForPaymentOut(order: OrderResponse?): Double? =
        order?.amount?.minus(order.fee.orNil)?.plus(order.agentFeeInfo?.amount.orNil)

    private fun getBillAmountForPaymentIn(order: OrderResponse?): Double? =
        order?.amount?.minus(order.agentFeeInfo?.amount.orNil)

    private fun isPaymentInOrOut(paymentType: String?): Boolean =
        paymentType == TYPE_PAYMENT_IN || paymentType == TYPE_PAYMENT_OUT

    data class InvoiceItem(
        val type: InvoiceItemType? = InvoiceItemType.ITEM_DETAIL,
        val label: String? = null,
        val value: String? = null,
        val hideIfMissing: Boolean? = false,
        val labelTextDecor: TextDecor? = null,
        val valueTextDecor: TextDecor? = null,
        val margins: Margin = Margin(top = 4),
        val heading: String? = null,
        val viewTag: String? = null,
        val viewVisibility: Int = View.VISIBLE,
        val statusWording: String? = null,
        val statusIcon: Int? = null,
        val responseCode: String? = null,
        val amount: Double? = null,
        val transactionType: String? = null,
        val guaranteeMessage: String? = null,
        val isCardTransaction: Boolean = false
    )

    data class TextDecor(
        val textColor: Int? = null,
        val textStyle: TextStyle? = null,
        val fontSize: Int? = null
    )

    data class Margin(
        val left: Int? = 0,
        val top: Int? = 0,
        val right: Int? = 0,
        val bottom: Int? = 0
    )

    enum class TextStyle {
        BOLD,
        ITALIC
    }

    enum class InvoiceItemType {
        ITEM_DETAIL,
        ITEM_CENTER,
        ITEM_DETAIL_CENTER,
        DIVIDER,
        CODE_INFO,
        NOTES,
        PAYMENT_CODE_INFO,
        ITEM_TITLE,
        PAYMENT_CODE_TRANSFER
    }
}
