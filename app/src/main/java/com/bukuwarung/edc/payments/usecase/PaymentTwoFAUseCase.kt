package com.bukuwarung.edc.payments.usecase

import com.bukuwarung.edc.global.enums.AuthActions
import com.bukuwarung.edc.payments.data.model.request.PinForgetRequest
import com.bukuwarung.edc.payments.data.model.request.PinSetupRequest
import com.bukuwarung.edc.payments.data.model.request.PinUpdateRequest
import com.bukuwarung.edc.payments.data.repository.PaymentTwoFARepository
import javax.inject.Inject

class PaymentTwoFAUseCase @Inject constructor(private val repository: PaymentTwoFARepository) {

    suspend fun verifyOtp(otp: String, authAction: String = AuthActions.SETUP_PIN.action) =
        repository.verifyOTP(otp, authAction)

    suspend fun requestOtpForPIN(
        otpChannel: String,
        authAction: String = AuthActions.SETUP_PIN.action
    ) = repository.requestOTPForPIN(otpChannel, authAction)

    suspend fun checkPinChangeRequest() = repository.checkPinChangeRequest()
    suspend fun verifyPin(pin: String) = repository.verifyPIN(pin)

    suspend fun checkPinLength() = repository.checkPinLength()
    suspend fun createPinV3(pinCreateRequest: PinSetupRequest) =
        repository.createPinV3(pinCreateRequest)

    suspend fun forgotPinV3(pinForgetRequest: PinForgetRequest) =
        repository.forgotPinV3(pinForgetRequest)

    suspend fun updatePinV3(pinUpdateRequest: PinUpdateRequest) =
        repository.updatePinV3(pinUpdateRequest)
}
