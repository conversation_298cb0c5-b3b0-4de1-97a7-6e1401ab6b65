package com.bukuwarung.edc.payments.data.datasource

import androidx.lifecycle.MutableLiveData
import androidx.paging.PageKeyedDataSource
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.payments.data.model.OrderHistoryData
import com.bukuwarung.edc.payments.data.model.PagingStatus
import com.bukuwarung.edc.payments.ui.history.OrderHistoryViewModel
import com.bukuwarung.edc.payments.usecase.OrderUseCase
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.errorMessage
import com.bukuwarung.edc.util.orNil
import com.bukuwarung.network.interceptors.NoConnectivityException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class OrdersPagedDataSource(
    private val ordersUseCase: OrderUseCase,
    private val coroutineScope: CoroutineScope,
    private val pagingStatusLiveData: MutableLiveData<PagingStatus>,
    private val activeTab: PaymentConst.HistoryTabs,
    private val orderDates: HashMap<PaymentConst.HistoryTabs, ArrayList<String>>,
    private var queryParams: QueryParams
) : PageKeyedDataSource<Int, OrderHistoryData>() {

    private var itemCount: Int = 0

    data class QueryParams(
        var accountId: String = Utils.getPaymentAccountId(),
        var searchQuery: String? = null,
        var customerId: String? = null,
        var startDate: String? = null,
        var endDate: String? = null,
        var type: List<String>? = null,
        var status: List<String>? = null,
        var page: Int? = null,
        var limit: Int? = null,
        var billerCode: String? = null,
        var sorting: String? = null,
        var bukuOrigin: String? = null,
        var serialNumberSelected: String? = null
    )

    override fun loadInitial(
        params: LoadInitialParams<Int>,
        callback: LoadInitialCallback<Int, OrderHistoryData>
    ) {
        pagingStatusLiveData.postValue(PagingStatus.Loading)

        coroutineScope.launch {
            try {
                ordersUseCase.getOrders(
                    accountId = queryParams.accountId,
                    customerId = queryParams.customerId,
                    startDate = queryParams.startDate,
                    endDate = queryParams.endDate,
                    type = queryParams.type,
                    status = queryParams.status,
                    page = queryParams.page,
                    limit = queryParams.limit,
                    billerCode = queryParams.billerCode,
                    sorting = queryParams.sorting,
                    searchQuery = queryParams.searchQuery,
                    bukuOrigin = queryParams.bukuOrigin
                ).let { it ->
                    if (it.isSuccessful) {
                        val orders = it.body()?.let { OrderHistoryViewModel.sortByDescending(it) }
                        val data = orders?.let { it1 ->
                            OrderHistoryViewModel.addDateHeaders(
                                it1,
                                OrderHistoryViewModel.getExistingOrderDates(activeTab, orderDates),
                                shouldAddDateHeaders = queryParams.searchQuery.isNullOrEmpty()
                            )
                        }
                        itemCount += orders?.size.orNil
                        if (data?.isNotEmpty()!!) {
                            val totalCount =
                                it.headers()[Constant.X_TOTAL_COUNT]?.toInt().orNil
                            pagingStatusLiveData.postValue(PagingStatus.Loaded(totalCount))
                            callback.onResult(
                                data as List<OrderHistoryData>,
                                0,
                                data.size,
                                0,
                                if (itemCount != totalCount) {
                                    1
                                } else {
                                    null
                                }
                            )
                        } else {
                            pagingStatusLiveData.postValue(PagingStatus.Empty)
                        }
                    } else {
                        pagingStatusLiveData.postValue(PagingStatus.Error(it.errorMessage()))
                    }
                }
            } catch (e: NoConnectivityException) {
                pagingStatusLiveData.postValue(PagingStatus.NoInternet)
            } catch (e: Exception) {
                pagingStatusLiveData.postValue(PagingStatus.Error(e.message.orEmpty()))
            }
        }
    }

    override fun loadBefore(
        params: LoadParams<Int>,
        callback: LoadCallback<Int, OrderHistoryData>
    ) {
        // no implementation required
    }

    override fun loadAfter(params: LoadParams<Int>, callback: LoadCallback<Int, OrderHistoryData>) {
        pagingStatusLiveData.postValue(PagingStatus.LoadingNextPage)

        coroutineScope.launch {
            delay(3000)
            try {
                ordersUseCase.getOrders(
                    accountId = queryParams.accountId,
                    customerId = queryParams.customerId,
                    startDate = queryParams.startDate,
                    endDate = queryParams.endDate,
                    type = queryParams.type,
                    status = queryParams.status,
                    page = queryParams.page,
                    limit = queryParams.limit,
                    billerCode = queryParams.billerCode,
                    sorting = queryParams.sorting,
                    searchQuery = queryParams.searchQuery,
                    bukuOrigin = queryParams.bukuOrigin
                ).let { it ->
                    if (it.isSuccessful) {
                        val orders = it.body()
                            ?.let { it1 -> OrderHistoryViewModel.sortByDescending(it1) }
                        val data = OrderHistoryViewModel.addDateHeaders(
                            orders!!,
                            OrderHistoryViewModel.getExistingOrderDates(activeTab, orderDates),
                            shouldAddDateHeaders = queryParams.searchQuery.isNullOrEmpty()
                        )
                        itemCount += data.size.orNil
                        val totalCount = it.headers()[Constant.X_TOTAL_COUNT]?.toInt().orNil
                        if (data.isNotEmpty()) {
                            callback.onResult(
                                data as List<OrderHistoryData>,
                                if (itemCount != totalCount) {
                                    params.key + 1
                                } else {
                                    null
                                }
                            )
                        }

                        pagingStatusLiveData.postValue(PagingStatus.Loaded(totalCount))
                    } else {
                        pagingStatusLiveData.postValue(PagingStatus.Error(it.errorMessage()))
                    }
                }
            } catch (e: NoConnectivityException) {
                pagingStatusLiveData.postValue(PagingStatus.NoInternet)
            } catch (e: Exception) {
                pagingStatusLiveData.postValue(PagingStatus.Error(e.message.orEmpty()))
            }
        }
    }
}
