package com.bukuwarung.edc.payments.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

data class TopupSaldoRequest(
    @SerializedName("amount")
    val amount: Double? = null,
    @SerializedName("cancel_unpaid_topup_id")
    val cancelUnpaidTopupId: String? = null,
    @SerializedName("account_id")
    val accountId: String? = null,
    @SerializedName("payment_methods")
    val paymentMethods: List<FinproCheckoutPayment>? = null
)

@Parcelize
data class FinproCheckoutPayment(val method: String, val channel: String) : Parcelable
