package com.bukuwarung.edc.payments.util

import android.content.Context
import com.bukuwarung.edc.R
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.payments.constant.PaymentRemoteConfig
import com.bukuwarung.edc.payments.data.model.DateFilter
import com.bukuwarung.edc.payments.data.model.OrderItem
import com.bukuwarung.edc.payments.data.model.OrderResponse
import com.bukuwarung.edc.payments.data.model.PaymentHistory
import com.bukuwarung.edc.payments.data.model.PaymentProgress
import com.bukuwarung.edc.payments.data.model.UrlType
import com.bukuwarung.edc.util.getColorCompat
import com.bukuwarung.edc.util.isNotNullOrBlank
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.webview.WebviewActivity
import com.bukuwarung.edc.webview.constant.ClassConstants.APPEAL_BANK_ACCOUNT
import com.bukuwarung.edc.webview.constant.ClassConstants.WEBVIEW_URL
import java.util.Calendar
import java.util.Locale.getDefault

object PaymentAuxilliary {

    const val TEXT_MESSAGE = "text_message"
    const val ICON_RES = "icon_res"
    const val TEXT_STYLE = "text_style"
    const val TEXT_COLOR = "text_color"
    const val ICON_COLOR = "icon_color"

    fun isPaymentOut(paymentType: String?): Boolean =
        paymentType.equals(PaymentConst.TYPE_PAYMENT_OUT, ignoreCase = true)

    fun isSaldoIn(paymentType: String?): Boolean =
        paymentType.equals(PaymentConst.TYPE_SALDO_IN, ignoreCase = true)

    fun isSaldoRefund(paymentType: String?): Boolean =
        paymentType.equals(PaymentConst.TYPE_SALDO_REFUND, ignoreCase = true)

    fun isSaldoOut(paymentType: String?): Boolean =
        paymentType.equals(PaymentConst.TYPE_SALDO_OUT, ignoreCase = true)

    fun isPaymentSaldoCashback(paymentType: String?): Boolean =
        paymentType.equals(PaymentConst.TYPE_SALDO_CASHBACK, ignoreCase = true) ||
            paymentType.equals(PaymentConst.TYPE_CASHBACK_IN, ignoreCase = true) ||
            paymentType.equals(PaymentConst.TYPE_CASHBACK_OUT, ignoreCase = true)

    fun isCashbackIn(order: OrderResponse): Boolean =
        order.type.equals(PaymentConst.TYPE_CASHBACK_IN, ignoreCase = true)

    fun isCashbackOut(order: OrderResponse): Boolean =
        order.type.equals(PaymentConst.TYPE_CASHBACK_OUT, ignoreCase = true)

    /**
     * Returns "Total Diterima(Total Received)" amount after deducting admin and other fees like qris
     */
    fun getTotalReceivedAmount(order: OrderResponse, item: OrderItem?): Double? {
        var totalReceived = item?.amount
        // Deduct admin fee
        item?.discountedFee?.let {
            totalReceived = totalReceived?.minus(it)
        }
        order.loyalty?.tierDiscount?.let {
            totalReceived = totalReceived?.plus(it)
        }
        return totalReceived
    }

    fun getDates(dateFilterDay: DateFilter): Pair<Long?, Long?> {
        val presetVal = dateFilterDay.presetValue
        var cal = Calendar.getInstance()
        presetVal?.let {
            return when (presetVal) {
                PaymentConst.DatePreset.TODAY -> {
                    Pair(cal.timeInMillis, cal.timeInMillis)
                }

                PaymentConst.DatePreset.YESTERDAY -> {
                    cal.add(Calendar.DATE, -1)
                    Pair(cal.timeInMillis, cal.timeInMillis)
                }

                PaymentConst.DatePreset.THIS_WEEK -> {
                    val endTime = cal.timeInMillis
                    val dayOfWeek = cal.get(Calendar.DAY_OF_WEEK)
                    // get Monday for this week, DAY_OF_WEEK is 2 for Monday
                    val minus = if (dayOfWeek == 1) -6 else 2 - dayOfWeek
                    cal.add(Calendar.DATE, minus)
                    Pair(cal.timeInMillis, endTime)
                }

                PaymentConst.DatePreset.LAST_WEEK -> {
                    val dayOfWeek = cal.get(Calendar.DAY_OF_WEEK)
                    val minus = if (dayOfWeek == 1) -6 else 2 - dayOfWeek
                    val lastWeekMonday = minus - 7
                    cal.add(Calendar.DATE, lastWeekMonday)
                    val sTime = cal.timeInMillis
                    cal.add(Calendar.DATE, 6)
                    Pair(sTime, cal.timeInMillis)
                }

                PaymentConst.DatePreset.THIS_MONTH -> {
                    val endTime = cal.timeInMillis
                    cal[Calendar.DAY_OF_MONTH] = 1
                    Pair(cal.timeInMillis, endTime)
                }

                PaymentConst.DatePreset.LAST_MONTH -> {
                    val aCalendar = Calendar.getInstance()
                    aCalendar.add(Calendar.MONTH, -1)
                    aCalendar[Calendar.DATE] = 1
                    val sTime = aCalendar.timeInMillis
                    aCalendar[Calendar.DATE] = aCalendar.getActualMaximum(Calendar.DAY_OF_MONTH)
                    val endTime = aCalendar.timeInMillis
                    Pair(sTime, endTime)
                }

                PaymentConst.DatePreset.THIS_YEAR -> {
                    val endTime = cal.timeInMillis
                    cal.set(Calendar.MONTH, 0)
                    cal.set(Calendar.DATE, 1)
                    val sTime = cal.timeInMillis
                    Pair(sTime, endTime)
                }

                PaymentConst.DatePreset.LAST_YEAR -> {
                    cal.set(Calendar.MONTH, 0)
                    cal.set(Calendar.DATE, 1)
                    cal.add(Calendar.YEAR, -1)
                    val sTime = cal.timeInMillis
                    cal.set(Calendar.MONTH, 11)
                    cal[Calendar.DATE] = cal.getActualMaximum(Calendar.DAY_OF_MONTH)
                    val endTime = cal.timeInMillis
                    Pair(sTime, endTime)
                }

                PaymentConst.DatePreset.LAST_SEVEN_DAYS -> {
                    val endTime = cal.timeInMillis
                    cal.add(Calendar.DATE, -6)
                    Pair(cal.timeInMillis, endTime)
                }

                PaymentConst.DatePreset.LAST_TEN_DAYS -> {
                    val endTime = cal.timeInMillis
                    cal.add(Calendar.DATE, -9)
                    Pair(cal.timeInMillis, endTime)
                }

                PaymentConst.DatePreset.CUSTOM_RANGE -> {
                    Pair(dateFilterDay.startDate, dateFilterDay.endDate)
                }

                PaymentConst.DatePreset.ALL -> {
                    Pair(null, null)
                }
            }
        } ?: run {
            val startDate = dateFilterDay.startDays
            val endDate = dateFilterDay.endDays
            return if (startDate != null && endDate != null) {
                cal.add(Calendar.DATE, startDate)
                val sTime = cal.timeInMillis
                cal = Calendar.getInstance()
                cal.add(Calendar.DATE, endDate)
                val endTime = cal.timeInMillis
                Pair(sTime, endTime)
            } else {
                Pair(null, null)
            }
        }
    }

    fun getPaymentStatusLabelData(
        type: String?,
        status: String?,
        context: Context
    ): MutableMap<String, Any>? {
        return when (type) {
            PaymentHistory.TYPE_CASHBACK_SETTLEMENT,
            PaymentHistory.TYPE_SALDO_IN,
            PaymentHistory.TYPE_SALDO_REFUND,
            PaymentHistory.TYPE_SALDO_OUT -> {
                val defaultIcon = when (type) {
                    PaymentHistory.TYPE_CASHBACK_SETTLEMENT -> R.drawable.ic_komisi_agen_dollar

                    PaymentHistory.TYPE_SALDO_IN,
                    PaymentHistory.TYPE_SALDO_REFUND -> R.drawable.vector_saldo_in_new

                    else -> R.drawable.vector_saldo_out_new
                }
                val failedIcon = when (type) {
                    PaymentHistory.TYPE_CASHBACK_SETTLEMENT
                    -> R.drawable.ic_komisi_agen_dollar_expired

                    PaymentHistory.TYPE_SALDO_IN,
                    PaymentHistory.TYPE_SALDO_REFUND -> R.drawable.vector_saldo_in_grey_new

                    else -> R.drawable.vector_saldo_out_grey_new
                }
                val baseMap: MutableMap<String, Any> = mutableMapOf(
                    ICON_RES to defaultIcon,
                    TEXT_STYLE to R.style.Body3
                )
                when (status) {
                    PaymentHistory.STATUS_EXPIRED -> {
                        baseMap[ICON_RES] = failedIcon
                        baseMap[TEXT_MESSAGE] = context.getString(R.string.expired_label)
                        baseMap
                    }

                    PaymentHistory.STATUS_CREATED,
                    PaymentHistory.STATUS_PENDING -> {
                        baseMap[TEXT_MESSAGE] = context.getString(R.string.waiting_label)
                        baseMap
                    }

                    PaymentHistory.STATUS_PAID -> {
                        baseMap[TEXT_MESSAGE] = context.getString(R.string.in_process)
                        baseMap
                    }

                    PaymentHistory.STATUS_COMPLETED -> {
                        baseMap[TEXT_MESSAGE] = context.getString(R.string.payment_status_completed)
                        baseMap
                    }

                    PaymentHistory.STATUS_CANCELLED -> {
                        baseMap[ICON_RES] = failedIcon
                        baseMap[TEXT_MESSAGE] = context.getString(R.string.cancelled_label)
                        baseMap
                    }

                    PaymentHistory.STATUS_FAILED -> {
                        baseMap[TEXT_MESSAGE] = context.getString(R.string.fail_label)
                        baseMap[TEXT_STYLE] = R.style.Body3_red80Bold
                        baseMap
                    }

                    PaymentHistory.STATUS_REFUNDING -> {
                        baseMap[TEXT_MESSAGE] = context.getString(R.string.refund_in_process)
                        baseMap
                    }

                    PaymentHistory.STATUS_REFUNDED -> {
                        baseMap[TEXT_MESSAGE] = context.getString(R.string.refund_successful)
                        baseMap[TEXT_STYLE] = R.style.Body3_green80
                        baseMap
                    }

                    PaymentHistory.STATUS_REFUNDING_FAILED -> {
                        baseMap[TEXT_MESSAGE] = context.getString(R.string.refund_failed_1)
                        baseMap[TEXT_STYLE] = R.style.Body3_red80Bold
                        baseMap
                    }

                    else -> null
                }
            }

            PaymentHistory.TYPE_PAYMENT_IN, PaymentHistory.TYPE_PAYMENT_OUT -> {
                val defaultIcon =
                    if (type ==
                        PaymentHistory.TYPE_PAYMENT_IN
                    ) {
                        R.drawable.ic_payment_in
                    } else {
                        R.drawable.ic_payment_out
                    }
                val failureIcon =
                    if (type ==
                        PaymentHistory.TYPE_PAYMENT_IN
                    ) {
                        R.drawable.ic_payment_in_failed
                    } else {
                        R.drawable.ic_payment_out_failed
                    }
                val baseMap: MutableMap<String, Any> = mutableMapOf(
                    ICON_RES to defaultIcon,
                    TEXT_STYLE to R.style.Body3
                )
                when (status) {
                    PaymentHistory.STATUS_EXPIRED -> {
                        baseMap[ICON_RES] = failureIcon
                        baseMap[TEXT_MESSAGE] = context.getString(R.string.expired_label)
                        baseMap
                    }

                    PaymentHistory.STATUS_CREATED,
                    PaymentHistory.STATUS_PENDING -> {
                        baseMap[TEXT_MESSAGE] = context.getString(R.string.waiting_label)
                        baseMap
                    }

                    PaymentHistory.STATUS_PAID,
                    PaymentHistory.STATUS_HOLD -> {
                        baseMap[TEXT_MESSAGE] = context.getString(R.string.in_process)
                        baseMap
                    }

                    PaymentHistory.STATUS_COMPLETED -> {
                        baseMap[TEXT_MESSAGE] = context.getString(R.string.payment_status_completed)
                        baseMap
                    }

                    PaymentHistory.STATUS_FAILED,
                    PaymentHistory.STATUS_REJECTED -> {
                        baseMap[TEXT_MESSAGE] = context.getString(R.string.fail_label)
                        baseMap[TEXT_STYLE] = R.style.Body3_red80Bold
                        baseMap
                    }

                    PaymentHistory.STATUS_REFUNDING -> {
                        baseMap[TEXT_MESSAGE] = context.getString(R.string.refund_in_process)
                        baseMap
                    }

                    PaymentHistory.STATUS_REFUNDED -> {
                        baseMap[TEXT_MESSAGE] = context.getString(R.string.refund_successful)
                        baseMap[TEXT_STYLE] = R.style.Body3_green80
                        baseMap
                    }

                    PaymentHistory.STATUS_REFUNDING_FAILED -> {
                        baseMap[TEXT_MESSAGE] = context.getString(R.string.refund_failed_1)
                        baseMap[TEXT_STYLE] = R.style.Body3_red80Bold
                        baseMap
                    }

                    else -> null
                }
            }

            PaymentHistory.TYPE_SALDO_REDEMPTION -> mutableMapOf(
                TEXT_MESSAGE to context.getString(R.string.payment_status_completed),
                ICON_RES to R.drawable.vector_saldo_cashback,
                TEXT_STYLE to R.style.Body3_black40
            )

            PaymentHistory.TYPE_SALDO_CASHBACK -> {
                val statusText = when (status) {
                    PaymentHistory.STATUS_COMPLETED -> context.getString(
                        R.string.payment_status_completed
                    )

                    PaymentHistory.STATUS_PENDING -> context.getString(R.string.in_process)
                    else -> ""
                }
                return mutableMapOf(
                    TEXT_MESSAGE to statusText,
                    ICON_RES to R.drawable.vector_saldo_cashback,
                    TEXT_STYLE to R.style.Body3_black40
                )
            }

            PaymentConst.TYPE_CASHBACK_IN, PaymentConst.TYPE_CASHBACK_OUT -> {
                val defaultIcon =
                    if (type ==
                        PaymentConst.TYPE_CASHBACK_IN
                    ) {
                        R.drawable.vector_cashback_in
                    } else {
                        R.drawable.vector_cashback_out
                    }
                val failureIcon =
                    if (type ==
                        PaymentConst.TYPE_CASHBACK_IN
                    ) {
                        R.drawable.vector_cashback_in_failed
                    } else {
                        R.drawable.vector_cashback_out_failed
                    }
                val baseMap: MutableMap<String, Any> = mutableMapOf(
                    ICON_RES to defaultIcon,
                    TEXT_STYLE to R.style.Body3_black40
                )
                when (status) {
                    PaymentHistory.STATUS_EXPIRED -> {
                        baseMap[ICON_RES] = failureIcon
                        baseMap[TEXT_MESSAGE] = context.getString(R.string.expired_label)
                        baseMap
                    }

                    PaymentHistory.STATUS_PENDING -> {
                        baseMap[TEXT_MESSAGE] = context.getString(R.string.in_process)
                        baseMap
                    }

                    PaymentHistory.STATUS_FAILED -> {
                        baseMap[TEXT_MESSAGE] = context.getString(R.string.payment_status_failed)
                        baseMap[TEXT_STYLE] = R.style.Body3_red80Bold
                        baseMap
                    }

                    PaymentHistory.STATUS_COMPLETED -> {
                        baseMap[TEXT_MESSAGE] = context.getString(R.string.payment_status_completed)
                        baseMap
                    }

                    PaymentHistory.STATUS_REFUNDED -> {
                        baseMap[TEXT_MESSAGE] = context.getString(R.string.refund_successful)
                        baseMap[TEXT_STYLE] = R.style.Body3_green80
                        baseMap
                    }

                    else -> null
                }
            }

            else -> {
                if (isPpob(type)) {
                    when (status) {
                        PaymentHistory.STATUS_EXPIRED -> mutableMapOf(
                            TEXT_MESSAGE to context.getString(R.string.expired_label),
                            ICON_RES to PaymentConst.CATEGORY_EXPIRED_ICON_CIRCLE_MAP[type]!!,
                            TEXT_STYLE to R.style.Body3_black40
                        )

                        PaymentHistory.STATUS_PAID -> mutableMapOf(
                            TEXT_MESSAGE to context.getString(R.string.in_process),
                            ICON_RES to PaymentConst.CATEGORY_ICON_CIRCLE_MAP[type]!!,
                            TEXT_STYLE to R.style.Body3_black40
                        )

                        PaymentHistory.STATUS_COMPLETED -> mutableMapOf(
                            TEXT_MESSAGE to context.getString(R.string.payment_status_completed),
                            ICON_RES to PaymentConst.CATEGORY_ICON_CIRCLE_MAP[type]!!,
                            TEXT_STYLE to R.style.Body3_black40
                        )

                        PaymentHistory.STATUS_FAILED -> mutableMapOf(
                            TEXT_MESSAGE to context.getString(R.string.fail_label),
                            ICON_RES to PaymentConst.CATEGORY_ICON_CIRCLE_MAP[type]!!,
                            TEXT_STYLE to R.style.Body3_red80Bold
                        )

                        PaymentHistory.STATUS_CREATED,
                        PaymentHistory.STATUS_PENDING -> mutableMapOf(
                            TEXT_MESSAGE to context.getString(R.string.waiting_label),
                            ICON_RES to PaymentConst.CATEGORY_ICON_CIRCLE_MAP[type]!!,
                            TEXT_STYLE to R.style.Body3_black40
                        )

                        PaymentHistory.STATUS_REFUNDING -> mutableMapOf(
                            TEXT_MESSAGE to context.getString(R.string.refund_in_process),
                            ICON_RES to PaymentConst.CATEGORY_ICON_CIRCLE_MAP[type]!!,
                            TEXT_STYLE to R.style.Body3_black40
                        )

                        PaymentHistory.STATUS_REFUNDED -> mutableMapOf(
                            TEXT_MESSAGE to context.getString(R.string.refund_successful),
                            ICON_RES to PaymentConst.CATEGORY_ICON_CIRCLE_MAP[type]!!,
                            TEXT_STYLE to R.style.Body3_green80
                        )

                        PaymentHistory.STATUS_REFUNDING_FAILED -> mutableMapOf(
                            TEXT_MESSAGE to context.getString(R.string.refund_failed_1),
                            ICON_RES to PaymentConst.CATEGORY_ICON_CIRCLE_MAP[type]!!,
                            TEXT_STYLE to R.style.Body3_red80Bold
                        )

                        else -> mutableMapOf(
                            TEXT_MESSAGE to "",
                            ICON_RES to PaymentConst.CATEGORY_ICON_CIRCLE_MAP[type]!!,
                            TEXT_STYLE to R.style.Body3_black40
                        )
                    }
                } else {
                    null
                }
            }
        }
    }

    fun isPpob(category: String?): Boolean {
        category ?: return false
        var b = false
        for ((key, _) in PaymentConst.CATEGORY_NAME_MAP) {
            b = category.lowercase(getDefault()).contains(key.lowercase(getDefault()))
            if (b) break
        }
        return b
    }

    fun getPaymentAmountData(type: String?, status: String?): MutableMap<String, Any>? {
        val baseMap: MutableMap<String, Any> = mutableMapOf(
            TEXT_STYLE to R.style.SubHeading1_black40
        )
        return when (type) {
            PaymentHistory.TYPE_SALDO_IN,
            PaymentHistory.TYPE_SALDO_REFUND,
            PaymentHistory.TYPE_PAYMENT_IN,
            PaymentHistory.TYPE_SALDO_REDEMPTION,
            PaymentConst.TYPE_CASHBACK_IN,
            PaymentHistory.TYPE_SALDO_CASHBACK -> {
                when (status) {
                    PaymentHistory.STATUS_PENDING,
                    PaymentHistory.STATUS_COMPLETED,
                    PaymentHistory.STATUS_PAID -> {
                        baseMap[TEXT_STYLE] = R.style.SubHeading1_green80
                        baseMap
                    }

                    else -> baseMap
                }
            }

            PaymentConst.TYPE_CASHBACK_OUT,
            PaymentHistory.TYPE_SALDO_OUT,
            PaymentHistory.TYPE_PAYMENT_OUT -> {
                when (status) {
                    PaymentHistory.STATUS_PENDING,
                    PaymentHistory.STATUS_COMPLETED,
                    PaymentHistory.STATUS_PAID -> {
                        baseMap[TEXT_STYLE] = R.style.SubHeading1_red80
                        baseMap
                    }

                    else -> baseMap
                }
            }

            else -> {
                if (isPpob(type)) {
                    when (status) {
                        PaymentHistory.STATUS_PENDING,
                        PaymentHistory.STATUS_COMPLETED,
                        PaymentHistory.STATUS_PAID -> {
                            baseMap[TEXT_STYLE] = R.style.SubHeading1_red80
                            baseMap
                        }

                        else -> baseMap
                    }
                } else {
                    null
                }
            }
        }
    }

    fun getPaymentProgressData(
        context: Context,
        paymentProgress: PaymentProgress
    ): MutableMap<String, Any> = when {
        PaymentHistory.STATUS_RED_LIST.contains(paymentProgress.state.lowercase()) -> {
            mutableMapOf(
                TEXT_COLOR to context.getColorCompat(R.color.out_red),
                ICON_RES to R.drawable.ic_cross_filled_circle,
                ICON_COLOR to context.getColorCompat(R.color.out_red)
            )
        }

        (
            paymentProgress.state.equals(
                PaymentHistory.STATUS_COMPLETED,
                ignoreCase = true
            ) && paymentProgress.timestamp.isNullOrBlank() && paymentProgress.hasNextTimestamp
            ) ||
            (
                paymentProgress.state.equals(
                    PaymentHistory.STATUS_PENDING,
                    ignoreCase = true
                ) && paymentProgress.timestamp.isNotNullOrBlank() &&
                    !paymentProgress.hasPreviousTimestamp
                ) ||
            paymentProgress.state.equals(
                PaymentHistory.STATUS_REFUNDING,
                ignoreCase = true
            ) || paymentProgress.state.equals(
                PaymentHistory.STATUS_HOLD,
                ignoreCase = true
            ) -> {
            mutableMapOf(
                TEXT_COLOR to context.getColorCompat(R.color.yellow),
                ICON_RES to R.drawable.ic_processing_filled_circle,
                ICON_COLOR to context.getColorCompat(R.color.transparent)
            )
        }

        paymentProgress.state.equals(PaymentHistory.STATUS_UNHOLD, ignoreCase = true) -> {
            mutableMapOf(
                TEXT_COLOR to context.getColorCompat(R.color.green),
                ICON_RES to R.drawable.ic_check_circle_60,
                ICON_COLOR to context.getColorCompat(R.color.green)
            )
        }

        paymentProgress.timestamp.isNullOrEmpty().not() -> {
            mutableMapOf(
                TEXT_COLOR to context.getColorCompat(R.color.green),
                ICON_RES to R.drawable.ic_check_circle_60,
                ICON_COLOR to context.getColorCompat(R.color.green)
            )
        }

        else -> mutableMapOf(
            TEXT_COLOR to context.getColorCompat(R.color.black_40),
            ICON_RES to R.drawable.ic_check_circle_60,
            ICON_COLOR to context.getColorCompat(R.color.black_40)
        )
    }

    fun getBankAccountRedirectionIntent(
        context: Context,
        urlType: UrlType,
        bankAccount: String? = null,
        paymentType: String,
        entryPoint: String
    ) {
        when (urlType) {
            UrlType.APPEAL_FLOW -> {
                context.openActivity(WebviewActivity::class.java) {
                    putString(
                        WEBVIEW_URL,
                        PaymentRemoteConfig.getPaymentConfigs().appealFlowUrl +
                            "?type=$paymentType&entryPoint=$entryPoint"
                    )
                    putString(APPEAL_BANK_ACCOUNT, bankAccount)
                }
            }

            UrlType.MATCHING_INFO -> {
                context.openActivity(WebviewActivity::class.java) {
                    putString(WEBVIEW_URL, PaymentRemoteConfig.getPaymentConfigs().matchingInfoUrl)
                }
            }

            UrlType.FAQ_USED_ACCOUNT -> {
                context.openActivity(WebviewActivity::class.java) {
                    putString(
                        WEBVIEW_URL,
                        PaymentRemoteConfig.getPaymentConfigs().faqUsedAccountUrl
                    )
                }
            }

            UrlType.FAQ_BLOCKED_ACCOUNT -> {
                context.openActivity(WebviewActivity::class.java) {
                    putString(
                        WEBVIEW_URL,
                        PaymentRemoteConfig.getPaymentConfigs().faqBlockedAccountUrl
                    )
                }
            }
        }
    }
}
