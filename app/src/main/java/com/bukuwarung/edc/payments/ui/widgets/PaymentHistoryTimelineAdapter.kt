package com.bukuwarung.edc.payments.ui.widgets

import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.ItemPaymentHistoryDetailTimelineBinding
import com.bukuwarung.edc.payments.data.model.PaymentHistory
import com.bukuwarung.edc.payments.data.model.PaymentProgress
import com.bukuwarung.edc.payments.util.PaymentAuxilliary
import com.bukuwarung.edc.payments.util.PaymentAuxilliary.ICON_COLOR
import com.bukuwarung.edc.payments.util.PaymentAuxilliary.ICON_RES
import com.bukuwarung.edc.payments.util.PaymentAuxilliary.TEXT_COLOR
import com.bukuwarung.edc.util.ComponentUtil
import com.bukuwarung.edc.util.DateTimeUtils.getTimestampFromUtcDate
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.isNotNullOrEmpty
import com.bukuwarung.ui_component.utils.setSingleClickListener
import java.util.Date

class PaymentHistoryTimelineAdapter(private val showPaymentCallback: (Boolean) -> Unit) :
    ListAdapter<PaymentProgress, PaymentHistoryTimelineAdapter.PaymentHistoryTimelineViewHolder>(
        ItemDiffCallback()
    ) {

    private var showReceipt = false
    private var orderStatus: String? = null
    private var paymentType: String? = null

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): PaymentHistoryTimelineViewHolder = PaymentHistoryTimelineViewHolder(
        ItemPaymentHistoryDetailTimelineBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    )

    override fun onBindViewHolder(holder: PaymentHistoryTimelineViewHolder, position: Int) {
        getItem(position)?.let { paymentProgress ->
            val hasAdditionalInfo =
                !paymentProgress.additional_info.isNullOrEmpty()
            if (position < itemCount - 1) {
                paymentProgress.hasNextTimestamp = getItem(position + 1).timestamp != null
            }
            if (position > 0) {
                paymentProgress.hasPreviousTimestamp = getItem(position - 1).timestamp != null
            }

            holder.bind(paymentProgress, position == itemCount - 1, hasAdditionalInfo, orderStatus)
        }
    }

    fun setOrderStatus(status: String?, paymentType: String?) {
        orderStatus = status
        this.paymentType = paymentType
    }

    fun setShowReceiptStatus(showReceipt: Boolean) {
        this.showReceipt = showReceipt
    }

    inner class PaymentHistoryTimelineViewHolder(
        private val binding: ItemPaymentHistoryDetailTimelineBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind(
            item: PaymentProgress,
            isLast: Boolean,
            hasAdditionalInfo: Boolean = false,
            orderStatus: String?
        ) {
            binding.apply {
                if (item.state == PaymentHistory.STATUS_PAID && item.timestamp != null &&
                    !item.hasNextTimestamp &&
                    (
                        Date().time - getTimestampFromUtcDate(
                            item.timestamp
                        )
                        ) > (20 * 60 * 60000L)
                ) {
                    txtInfoPending.visibility = View.VISIBLE
                } else {
                    txtInfoPending.visibility = View.GONE
                }
                viewPendingStatusTimeline.visibility = (!isLast).asVisibility()
                txtLabelStatusPending.text = item.description
                txtStatusPendingDate.text = item.getFormattedTimestamp()
                ComponentUtil.setVisible(txtStatusPendingDate, item.timestamp.isNotNullOrEmpty())
                txtAdditionalInfo.text = item.additional_info
                ComponentUtil.setVisible(txtAdditionalInfo, hasAdditionalInfo)
                val statusData =
                    PaymentAuxilliary.getPaymentProgressData(binding.root.context, item)
                (statusData[TEXT_COLOR] as Int?)?.let {
                    txtLabelStatusPending.setTextColor(it)
                    txtStatusPendingDate.setTextColor(it)
                    viewPendingStatusTimeline.background.mutate().colorFilter =
                        PorterDuffColorFilter(it, PorterDuff.Mode.SRC_ATOP)
                }
                (statusData[ICON_RES] as Int?)?.let { iconStatusPending1.setImageResource(it) }
                (statusData[ICON_COLOR] as Int?)?.let {
                    iconStatusPending1.setColorFilter(it, PorterDuff.Mode.SRC_ATOP)
                }

                txtShowReceipt.visibility =
                    (
                        orderStatus == PaymentHistory.STATUS_PAID &&
                            item.state == PaymentHistory.STATUS_PAID
                        ).asVisibility()
                txtShowReceipt.setSingleClickListener {
                    showReceipt = !showReceipt
                    showPaymentCallback.invoke(showReceipt)
                    txtShowReceipt.text =
                        txtShowReceipt.context.getString(
                            if (showReceipt) R.string.hide_receipt else R.string.show_receipt
                        )
                }

                txtShowReceipt.text =
                    txtShowReceipt.context.getString(
                        if (showReceipt) R.string.hide_receipt else R.string.show_receipt
                    )
            }
        }
    }

    class ItemDiffCallback : DiffUtil.ItemCallback<PaymentProgress>() {
        override fun areItemsTheSame(oldItem: PaymentProgress, newItem: PaymentProgress): Boolean =
            oldItem.toString() == newItem.toString()

        override fun areContentsTheSame(
            oldItem: PaymentProgress,
            newItem: PaymentProgress
        ): Boolean = oldItem.toString() == newItem.toString()
    }
}
