package com.bukuwarung.edc.payments.usecase

import com.bukuwarung.edc.payments.data.model.PaymentHealthCheckRequest
import com.bukuwarung.edc.payments.data.model.TopupSaldoRequest
import com.bukuwarung.edc.payments.data.model.request.FavouriteRequest
import com.bukuwarung.edc.payments.data.repository.FinproRepository
import com.bukuwarung.edc.ppob.common.model.Biller
import com.bukuwarung.edc.ppob.common.model.FinproAddCartRequest
import com.bukuwarung.edc.ppob.confirmation.model.PpobStatusPollingResponse
import com.bukuwarung.edc.ppob.train.model.TrainEnrollmentRequest
import com.bukuwarung.payments.data.model.FinproCheckoutOrderRequest
import javax.inject.Inject
import retrofit2.Response

class FinproUseCase @Inject constructor(private val finproRepository: FinproRepository) {

    suspend fun getSaldo() = finproRepository.getSaldo()

    suspend fun getSaldoAdminFee() = finproRepository.getSaldoAdminFee()

    suspend fun getExistingSaldoTopup() = finproRepository.getExistingSaldoTopup()

    suspend fun createSaldoTopup(request: TopupSaldoRequest) =
        finproRepository.createSaldoTopup(request)

    suspend fun getPaymentOutStatus(accountId: String, disbursementId: String) =
        finproRepository.getPaymentOutStatus(accountId, disbursementId)

    suspend fun getOrderDetail(accountId: String, orderId: String, ledgerAccountId: String?) =
        finproRepository.getOrderDetail(accountId, orderId, ledgerAccountId)

    suspend fun getPpobProductsWithBillerDetails(
        category: String,
        map: Map<String, String>,
        isSpecialProduct: Boolean?
    ) = finproRepository.getPpobProductsWithBillerDetails(category, map, isSpecialProduct)

    suspend fun addItemToCart(bookId: String, request: FinproAddCartRequest) =
        finproRepository.addItemToCart(bookId, request)

    suspend fun getTrainTicketUrl(accountId: String, request: TrainEnrollmentRequest) =
        finproRepository.getTrainTicketUrl(accountId, request)

    suspend fun getTrainEnrollmentDetail(accountId: String, enrollmentId: String) =
        finproRepository.getTrainEnrollmentDetail(accountId, enrollmentId)

    suspend fun cancelOrder(accountId: String, orderId: String) =
        finproRepository.cancelOrder(accountId, orderId)

    suspend fun deleteFavourite(bookId: String, favouriteId: String) =
        finproRepository.deleteFavourite(bookId, favouriteId)

    suspend fun getBillers(category: String?): Response<List<Biller>> =
        finproRepository.getBillers(category)

    suspend fun getFavourites(bookId: String, category: String, page: Int, count: Int) =
        finproRepository.getFavourites(bookId, category, page, count)

    suspend fun getRecentTransactions(bookId: String, category: String, page: Int, limit: Int) =
        finproRepository.getRecentTransactions(bookId, category, page, limit)

    suspend fun getPpobStatus(
        accountId: String,
        orderId: String
    ): Response<PpobStatusPollingResponse> = finproRepository.getPpobStatus(accountId, orderId)

    suspend fun getPaymentMethodsV2(amount: Double, category: String, productCode: String) =
        finproRepository.getPaymentMethodsV2(amount, category, productCode)

    suspend fun doHealthCheck(paymentHealthCheckRequest: PaymentHealthCheckRequest) =
        finproRepository.doHealthCheck(paymentHealthCheckRequest)

    suspend fun checkoutOrder(
        bookId: String,
        orderId: String,
        bankAccount: FinproCheckoutOrderRequest,
        checkoutToken: String
    ) = finproRepository.checkoutOrder(bookId, orderId, bankAccount, checkoutToken)

    suspend fun addFavourite(bookId: String, favouriteRequest: FavouriteRequest) =
        finproRepository.addFavourite(bookId, favouriteRequest)

    suspend fun getRecommendations(
        bookId: String,
        orderId: String,
        query: String,
        page: Int,
        count: Int
    ) = finproRepository.getRecommendations(bookId, orderId, query, page, count)
}
