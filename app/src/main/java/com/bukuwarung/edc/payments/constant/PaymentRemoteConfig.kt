package com.bukuwarung.edc.payments.constant

import com.bukuwarung.edc.InvoiceDataBlock
import com.bukuwarung.edc.payments.data.model.AppTexts
import com.bukuwarung.edc.payments.data.model.PaymentConfigs
import com.bukuwarung.edc.util.RemoteConfigUtils.remoteConfig
import com.bukuwarung.edc.util.Utils
import com.google.gson.reflect.TypeToken

object PaymentRemoteConfig {

    private const val MINIMUM_PAYMENT_OUT_AMOUNT = "minimum_payment_out_amount"
    private const val SALDO_MIN_TOPUP_AMOUNT = "saldo_min_topup_amount"
    private const val SALDO_MIN_TOPUP_AMOUNT_VALUE = 10000.0
    private const val APP_TEXTS = "app_texts"
    const val SALDO_BONUS = "saldo_bonus"
    const val SALDO_BONUS_URL = "https://bukuwarung.com/bukupoin-berubah-jadi-saldo-bonus/"
    const val INVOICE_DATA_BLOCK = "invoice_data_block"

    const val FAQ_USED = "faq_used"
    const val FAQ_USED_ACCOUNT_BW_URL = "https://bukuwarung.com/verifikasi-rekening-penerima/"

    const val TRANSFER_DESTINATION_WARNING = "transfer_destination_warning"
    const val TRANSFER_DESTINATION_WARNING_VALUE =
        "Saat ini ada beberapa Bank yang mengalami gangguan transfer. Cek daftarnya di sini."

    fun getMinimumPaymentOutAmount(): Double {
        val value = remoteConfig.getDouble(MINIMUM_PAYMENT_OUT_AMOUNT)
        if (value <= 0.0) return 10000.0
        return value
    }

    fun getAppText(): AppTexts {
        val json = remoteConfig.getString(APP_TEXTS)
        val type = object : TypeToken<AppTexts>() {}.type
        return Utils.jsonToObject(json, type) ?: AppTexts()
    }

    fun getMinimumTopupSaldoAmount(): Double {
        val value = remoteConfig.getDouble(SALDO_MIN_TOPUP_AMOUNT)
        if (value <= 0.0) return SALDO_MIN_TOPUP_AMOUNT_VALUE
        return value
    }

    private const val PAYMENT_CONFIGS = "payment_configs"
    const val TRANSACTION_HISTORY_SERVICE = "TRANSACTION_HISTORY_SERVICE"

    fun getPaymentConfigs(): PaymentConfigs {
        val json = getRemoteConfigString(PAYMENT_CONFIGS)
        val type = object : TypeToken<PaymentConfigs>() {}.type
        return Utils.jsonToObject(json, type) ?: PaymentConfigs()
    }

    private fun getRemoteConfigString(configKey: String): String {
        var originalValue = remoteConfig.getString(configKey)
        var budRemoteConfig =
            originalValue.replace("api-v3.bukuwarung.com", "api-v4.bukuwarung.com")
                .replace("api-v2.bukuwarung.com", "api-v4.bukuwarung.com")
        return budRemoteConfig
    }

    fun getTransactionHistoryService(): Boolean = remoteConfig.getBoolean(
        TRANSACTION_HISTORY_SERVICE
    )

    fun getSaldoUrl(): String = remoteConfig.getString(SALDO_BONUS)

    fun getFAQURL(): String = remoteConfig.getString(FAQ_USED)

    fun getInvoiceMetadata(): InvoiceDataBlock {
        val json = getRemoteConfigString(INVOICE_DATA_BLOCK)
        val type = object : TypeToken<InvoiceDataBlock>() {}.type
        return Utils.jsonToObject(json, type) ?: InvoiceDataBlock()
    }

    fun fetchTransferDestinationWarning(): String =
        remoteConfig.getString(TRANSFER_DESTINATION_WARNING)
}
