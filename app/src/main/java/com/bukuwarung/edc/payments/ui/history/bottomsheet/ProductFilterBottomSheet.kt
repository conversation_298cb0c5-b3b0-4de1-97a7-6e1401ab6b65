package com.bukuwarung.edc.payments.ui.history.bottomsheet

import android.content.res.Resources
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.BottomsheetOrderFilterBinding
import com.bukuwarung.edc.global.base.BaseBottomSheetDialogFragment
import com.bukuwarung.edc.payments.ui.history.OrderHistoryViewModel
import com.bukuwarung.edc.payments.ui.history.adapter.FilterAdapter
import com.bukuwarung.edc.payments.ui.history.adapter.FilterSectionAdapter
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.getClassTag
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class ProductFilterBottomSheet :
    BaseBottomSheetDialogFragment(),
    FilterAdapter.Callback {

    companion object {
        fun createInstance(fr: FragmentManager) = ProductFilterBottomSheet().show(fr, getClassTag())
    }

    private val viewModel: OrderHistoryViewModel by activityViewModels()

    private lateinit var binding: BottomsheetOrderFilterBinding

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = BottomsheetOrderFilterBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        dialog?.setOnShowListener {
            val dialog = it as BottomSheetDialog
            val bottomSheet =
                dialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
            bottomSheet?.let { _ ->
                dialog.behavior.state = BottomSheetBehavior.STATE_EXPANDED
                dialog.behavior.peekHeight = Resources.getSystem().displayMetrics.heightPixels - 100
            }
        }

        binding.tvTitle.text = getString(R.string.produk)
        binding.ivClose.setOnClickListener { dismiss() }

        binding.btnConfirmFilter.setOnClickListener {
            viewModel.applyTypeFilters()
            dismiss()
        }

        viewModel.filtersState.let {
            val filterValues = viewModel.getApplicableFilters()?.products
            val appliedTypeFilters = it[viewModel.activeTab]?.typeFilters
            Utils.safeLet(
                filterValues,
                appliedTypeFilters
            ) { productFilters, appliedFilters ->
                // Update filterValues from the appliedTypeFilters
                productFilters.map { filterSection ->
                    filterSection.filters.map { filter ->
                        filter.isChecked = appliedFilters.contains(filter.key)
                    }
                }
                binding.rvProductSections.apply {
                    layoutManager = LinearLayoutManager(requireContext())
                    adapter = FilterSectionAdapter(productFilters, this@ProductFilterBottomSheet)
                }
            }
        }
    }

    override fun isAnythingChanged() {
        binding.btnConfirmFilter.isEnabled = true
    }
}
