package com.bukuwarung.edc.payments.data.model.response

import android.os.Parcelable
import androidx.annotation.Keep
import com.bukuwarung.edc.payments.data.model.FavouriteDetail
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Keep
@Parcelize
data class AddFavouriteResponse(

    @SerializedName("favourite_detail")
    val favouriteDetail: FavouriteDetail? = null,

    @SerializedName("message")
    val message: String? = null,

    @SerializedName("success")
    val success: Boolean? = null
) : Parcelable
