package com.bukuwarung.edc.payments.ui.history

import android.os.Bundle
import android.text.Editable
import android.text.SpannableStringBuilder
import android.text.TextWatcher
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.ActivityOrderHistoryBinding
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.homepage.data.model.HomePageBodyContents
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.payments.constant.PaymentRemoteConfig
import com.bukuwarung.edc.payments.data.model.ExpiringCashBackInfo
import com.bukuwarung.edc.payments.data.model.PagingStatus
import com.bukuwarung.edc.payments.data.model.PaymentFilterDtoX
import com.bukuwarung.edc.payments.data.model.PaymentHistory
import com.bukuwarung.edc.payments.ui.core.OrderDetailActivity
import com.bukuwarung.edc.payments.ui.history.adapter.OrderHistoryAdapter
import com.bukuwarung.edc.payments.ui.history.adapter.OrderHistoryPaginationAdapter
import com.bukuwarung.edc.payments.ui.history.bottomsheet.DateFilterBottomSheet
import com.bukuwarung.edc.payments.ui.history.bottomsheet.ErrorBottomSheet
import com.bukuwarung.edc.payments.ui.history.bottomsheet.ProductFilterBottomSheet
import com.bukuwarung.edc.payments.ui.history.bottomsheet.SortingBottomSheet
import com.bukuwarung.edc.payments.ui.history.bottomsheet.StatusFilterBottomSheet
import com.bukuwarung.edc.payments.ui.widgets.ExpiringCashbacksView
import com.bukuwarung.edc.payments.util.HeaderItemDecoration
import com.bukuwarung.edc.payments.util.OrderListCallback
import com.bukuwarung.edc.payments.util.PpobProductsListener
import com.bukuwarung.edc.util.DateTimeUtils
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.Utils.safeLet
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.colorText
import com.bukuwarung.edc.util.getColorCompat
import com.bukuwarung.edc.util.getDrawableCompat
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.isNotNullOrBlank
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.orNil
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick
import com.bukuwarung.edc.webview.WebviewActivity
import com.bukuwarung.edc.webview.constant.ClassConstants
import com.google.android.material.tabs.TabLayout
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class OrderHistoryActivity :
    AppCompatActivity(),
    OrderListCallback,
    ExpiringCashbacksView.Callback,
    PpobProductsListener {

    companion object {
        const val BOOK_ID = "book_id"
        const val ACTIVE_TAB = "active_tab"
        const val CUSTOMER_ID = "customer_id"
        const val PRODUCT_FILTERS = "product_filters"
        const val STATUS_FILTERS = "status_filters"
        const val START_DATE = "start_date"
        const val END_DATE = "end_date"
        const val DATE_PRESET = "date_preset"

        /*
         * Biller code is used to differentiate between Postpaid and Prepaid Listrik
         */
        const val BILLER_CODE = "biller_code"
    }

    private lateinit var binding: ActivityOrderHistoryBinding
    private var ordersAdapter: OrderHistoryAdapter? = null
    private var paginationAdapter: OrderHistoryPaginationAdapter? = null
    private var isFilterApplied = false

    private val viewModel: OrderHistoryViewModel by viewModels()

    private val customerId by lazy { intent?.getStringExtra(CUSTOMER_ID).orEmpty() }
    private val bookId by lazy { intent?.getStringExtra(BOOK_ID).orEmpty() }
    private val billerCode by lazy { intent?.getStringExtra(BILLER_CODE) }
    private var searchWatcher = object : TextWatcher {
        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
        }

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
        }

        override fun afterTextChanged(s: Editable?) {
            debouncedSearch(s.toString())
        }
    }
    val debouncedSearch: (String) -> Unit = Utils.debounce(
        PaymentRemoteConfig.getPaymentConfigs().paginationConfig?.debounceDelay
            ?: PaymentConst.CLICK_DEBOUNCE_TIME,
        lifecycleScope,
        ::handleSearchChange
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityOrderHistoryBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setupToolbar()

        ordersAdapter = OrderHistoryAdapter(this, viewModel.linkedOrdersMap)
        paginationAdapter = OrderHistoryPaginationAdapter(this, viewModel.linkedOrdersMap)

        var preselectedTab = intent?.getSerializableExtra(ACTIVE_TAB) as? PaymentConst.HistoryTabs
        intent?.getStringExtra(ACTIVE_TAB)?.let {
            preselectedTab = Utils.tryToGetValueOrDefault(
                tryBlock = { PaymentConst.HistoryTabs.valueOf(it) },
                defaultValue = PaymentConst.HistoryTabs.ALL
            )
        }
        var datePreset = intent?.getSerializableExtra(DATE_PRESET) as? PaymentConst.DatePreset
        intent?.getStringExtra(DATE_PRESET)?.let {
            datePreset = Utils.tryToGetValueOrDefault(
                tryBlock = { PaymentConst.DatePreset.valueOf(it) },
                defaultValue = null
            )
        }
        val typeFilters = intent?.getStringArrayListExtra(PRODUCT_FILTERS)
        val statusFilters = intent?.getStringArrayListExtra(STATUS_FILTERS)
        val startDate = intent?.getLongExtra(START_DATE, 0L)
        val endDate = intent?.getLongExtra(END_DATE, 0L)
        viewModel.init(
            bookId, preselectedTab,
            typeFilters, statusFilters, startDate, endDate,
            customerId, billerCode, datePreset
        )

        with(binding) {
            tvEdcFilter.isSelected = true
            tvEdcFilter.setOnClickListener {
                tvEdcFilter.isSelected = !tvEdcFilter.isSelected
                viewModel.updateEdcFilterSelected(tvEdcFilter.isSelected)
            }
            with(baSaldoBonus) {
                text = getString(R.string.cashback_to_saldo_bonus)

                setOnClickListener {
                    openActivity(WebviewActivity::class.java) {
                        putString(ClassConstants.WEBVIEW_URL, PaymentRemoteConfig.getSaldoUrl())
                        putString("title", getString(R.string.filter_history_saldo_bonus))
                    }
                }
            }
            tlHistory.addTab(
                tlHistory.newTab().setText(getString(R.string.filter_history_all))
            )
            tlHistory.addTab(
                tlHistory.newTab().setText(getString(R.string.ppob_history_title))
            )
            tlHistory.addTab(
                tlHistory.newTab().setText(getString(R.string.filter_history_payment))
            )
            tlHistory.addTab(
                tlHistory.newTab().setText(getString(R.string.filter_history_saldo))
            )

            tlHistory.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
                override fun onTabSelected(tab: TabLayout.Tab?) {
                    layoutEmptyView.root.hideView()
                    Utils.hideKeyboard(this@OrderHistoryActivity)
                    isFilterApplied = false
                    val tabKey = getTabKey(tab?.position.orNil)
                    viewModel.onTabSelected(tabKey)
                    updatedFilters(viewModel.filtersState)
                    tilSearch.showView()
                    tvFilterProduct.showView()
                    tvSort.hideView()
                    baSaldoBonus.hideView()
                    expiringCashbackInfo.hideView()
                    if (!tvEdcFilter.isSelected) {
                        tvEdcFilter.isSelected = !tvEdcFilter.isSelected
                        viewModel.updateEdcFilterSelected(tvEdcFilter.isSelected)
                    }
                    when (tabKey) {
                        PaymentConst.HistoryTabs.ALL ->
                            tilSearch.editText?.hint = getString(R.string.search_transactions)

                        PaymentConst.HistoryTabs.PPOB ->
                            tilSearch.editText?.hint = getString(R.string.search_product_name)

                        PaymentConst.HistoryTabs.PEMBAYARAN ->
                            tilSearch.editText?.hint = getString(R.string.search_customer_name)

                        PaymentConst.HistoryTabs.SALDO ->
                            tilSearch.editText?.hint = getString(R.string.search_transactions)

                        PaymentConst.HistoryTabs.SALDOBONUS ->
                            tilSearch.editText?.hint =
                                getString(R.string.search_transactions)
                    }
                }

                override fun onTabUnselected(tab: TabLayout.Tab?) {
                }

                override fun onTabReselected(tab: TabLayout.Tab?) {
                }
            })

            tvFilterStatus.singleClick {
                StatusFilterBottomSheet.createInstance(supportFragmentManager)
            }

            tvFilterDate.singleClick {
                DateFilterBottomSheet.createInstance(supportFragmentManager)
            }

            tvFilterProduct.singleClick {
                ProductFilterBottomSheet.createInstance(supportFragmentManager)
            }

            tvSort.singleClick {
                SortingBottomSheet.createInstance(supportFragmentManager)
            }

            rvHistory.apply {
                adapter = paginationAdapter
                layoutManager = LinearLayoutManager(this@OrderHistoryActivity)
                addItemDecoration(
                    HeaderItemDecoration(rvHistory, false) { pos: Int ->
                        if (pos == -1) {
                            false
                        } else {
                            paginationAdapter?.isHeaderItem(pos).isTrue
                        }
                    }
                )
            }

            tilSearch.editText?.addTextChangedListener(searchWatcher)
            tilSearch.setEndIconOnClickListener {
                clearSearch()
                Toast.makeText(
                    this@OrderHistoryActivity,
                    R.string.filter_removed,
                    Toast.LENGTH_SHORT
                ).show()
            }

            ivHelp.setOnClickListener {
                Utils.launchBrowser(
                    this@OrderHistoryActivity,
                    PaymentRemoteConfig.getPaymentConfigs().supportUrls.payments
                )
            }

            ivClearFilter.setOnClickListener {
                viewModel.clearFilters()
                it.hideView()
                Toast.makeText(
                    this@OrderHistoryActivity,
                    R.string.transaction_history_updated,
                    Toast.LENGTH_SHORT
                ).show()
            }
        }

        subscribeState()
    }

    private fun handleSearchChange(searchQuery: String) {
        paginationAdapter?.searchText = searchQuery
        viewModel.updateSearchQuery(searchQuery)
    }

    private fun subscribeState() {
        viewModel.orderHistoryLive.observe(this) {
            updateAdapter()
        }

        viewModel.filtersStateLive.observe(this) { filtersMap ->
            updatedFilters(filtersMap)
        }

        viewModel.event.observe(this) {
            when (it) {
                OrderHistoryViewModel.Event.UpdateAdapter -> updateAdapter()
                OrderHistoryViewModel.Event.ClearSearch -> clearSearch(false)
                is OrderHistoryViewModel.Event.LinkedItemsFetched -> updateLinkedOrders(
                    it.adapterPos
                )

                is OrderHistoryViewModel.Event.ApiError -> {
                    handleError(it.errorMessage)
                    updateLinkedOrders(it.adapterPos)
                }
            }
        }

        viewModel.viewState.observe(this) {
            with(binding) {
                if (it.loading) {
                    rvHistory.hideView()
                    pbLoading.showView()
                } else {
                    rvHistory.showView()
                    pbLoading.hideView()
                }
                if (it.errorMessage != null) {
                    handleError(it.errorMessage)
                }
            }
        }

        viewModel.expiringCashbacks.observe(this) {
            setCashbackInfoView(it)
        }

        ordersAdapter?.registerAdapterDataObserver(object : RecyclerView.AdapterDataObserver() {
            override fun onChanged() {
                super.onChanged()
                if (viewModel.viewState.value?.loading.isTrue) return
                if (ordersAdapter?.itemCount == 0) {
                    setEmptyView()
                } else {
                    binding.layoutEmptyView.root.hideView()
                }
            }
        })

        viewModel.ordersPagedData?.observe(this) {
            paginationAdapter?.submitList(it)
        }

        viewModel.pagingStatus.observe(this) { status ->
            when (status) {
                PagingStatus.Loading -> {
                    with(binding) {
                        layoutEmptyView.root.hideView()
                        rvHistory.hideView()
                        pbLoading.showView()
                    }
                }

                is PagingStatus.Loaded -> {
                    with(binding) {
                        layoutEmptyView.root.hideView()
                        rvHistory.showView()
                        binding.pbLoading.hideView()
                        if (tilSearch.editText?.text?.isNotEmpty().isTrue) {
                            filterResultCount(status.totalItems)
                        } else {
                            filterResultCount(0)
                        }
                    }
                }

                PagingStatus.Empty -> {
                    binding.pbLoading.hideView()
                    setEmptyView()
                }

                PagingStatus.EmptyNextPage -> {
                    // Hide any loading for the next page
                    binding.pbLoading.hideView()
                    setEmptyView()
                }

                PagingStatus.LoadingNextPage -> {
                    // Show any loading for the next page
                    binding.pbLoading.showView()
                }

                is PagingStatus.Error -> {
                    handleError(status.errorMessage)
                }

                PagingStatus.NoInternet -> {
                    handleError(Constant.NO_INTERNET_ERROR_MESSAGE)
                }

                else -> {}
            }
        }
    }

    private fun setCashbackInfoView(expiringCashbackInfo: ExpiringCashBackInfo) {
        if (expiringCashbackInfo.amount.orNil > 0) {
            binding.baSaldoBonus.hideView()
            binding.expiringCashbackInfo.setView(expiringCashbackInfo, this)
        } else {
            binding.baSaldoBonus.showView()
            binding.expiringCashbackInfo.hideView()
        }
    }

    private fun handleError(errorMessage: String?) {
        if (errorMessage.isNotNullOrBlank()) {
            if (errorMessage != Constant.NO_INTERNET_ERROR_MESSAGE) {
                ErrorBottomSheet.createInstance(
                    ErrorBottomSheet.Companion.ApiErrorType.API_ERROR,
                    errorMessage?.ifEmpty { getString(R.string.try_again_or_wait) }
                ).show(supportFragmentManager, ErrorBottomSheet.TAG)
            } else {
                ErrorBottomSheet.createInstance(
                    ErrorBottomSheet.Companion.ApiErrorType.CONNECTION_ERROR,
                    errorMessage
                ).show(supportFragmentManager, ErrorBottomSheet.TAG)
            }
        }
    }

    private fun clearSearch(fetchResults: Boolean = true) {
        binding.tilSearch.editText?.apply {
            removeTextChangedListener(searchWatcher)
            setText("")
            paginationAdapter?.searchText = ""
            viewModel.updateSearchQuery("", fetchResults)
            addTextChangedListener(searchWatcher)
        }
    }

    private fun updateLinkedOrders(adapterPos: Int) {
        paginationAdapter?.notifyItemChanged(adapterPos)
    }

    /**
     * Sets existing searched query in the search input but doesn't trigger
     * text watcher to avoid duplicate network calls.
     */
    private fun setSearchQuery(searchQuery: String) {
        binding.tilSearch.editText?.apply {
            removeTextChangedListener(searchWatcher)
            setText(searchQuery)
            setSelection(searchQuery.length)
            paginationAdapter?.searchText = searchQuery
            addTextChangedListener(searchWatcher)
        }
    }

    private fun setEmptyView() {
        with(binding.layoutEmptyView) {
            root.showView()
            when {
                binding.tilSearch.editText?.text?.isNotEmpty().isTrue -> {
                    ivEmptyIcon.setImageResource(R.drawable.vector_no_search_result)
                    tvEmptyTitle.text = getString(R.string.no_search_transaction)
                    tvEmptyMessage.text = getString(R.string.no_search_transaction_description)
                    btnEmptyCta.text = getString(R.string.change_keyword)
                    btnEmptyCta.setOnClickListener {
                        clearSearch()
                    }
                }

                isFilterApplied -> {
                    ivEmptyIcon.setImageResource(R.drawable.vector_no_search_result)
                    tvEmptyTitle.text = getString(R.string.no_filter_transaction)
                    tvEmptyMessage.text = getString(R.string.no_filter_transaction_description)
                    btnEmptyCta.text = getString(R.string.change_keyword)
                    btnEmptyCta.setOnClickListener {
                        viewModel.clearFilters()
                        binding.ivClearFilter.hideView()
                    }
                }

                else -> {
                    ivEmptyIcon.setImageResource(R.drawable.vector_no_transaction)
                    tvEmptyTitle.text = getString(R.string.no_transaction)
                    tvEmptyMessage.text = getString(R.string.no_transaction_description)
                    btnEmptyCta.hideView()
                }
            }
        }
    }

    private fun updatedFilters(filtersMap: HashMap<PaymentConst.HistoryTabs, PaymentFilterDtoX>) {
        with(binding) {
            filtersMap[viewModel.activeTab]?.statusFilters?.let { statusFilter ->
                if (statusFilter.size > 0) {
                    if (statusFilter.size == 1) {
                        val selectedFilter =
                            viewModel.getApplicableFilters()?.status?.flatMap { it.filters }
                                ?.find { it.key == statusFilter[0] }
                        selectedFilter?.label?.let {
                            tvFilterStatus.isSelected = true
                            tvFilterStatus.text = it
                        } ?: run {
                            tvFilterStatus.isSelected = false
                            tvFilterStatus.text = getString(R.string.select_status)
                        }
                    } else {
                        tvFilterStatus.isSelected = true
                        tvFilterStatus.text = getString(R.string.multi_status)
                    }
                } else {
                    tvFilterStatus.isSelected = false
                    tvFilterStatus.text = getString(R.string.select_status)
                }
            }
            filtersMap[viewModel.activeTab]?.typeFilters?.let { typeFilters ->
                if (typeFilters.size > 0) {
                    if (typeFilters.size == 1) {
                        val selectedFilter =
                            viewModel.getApplicableFilters()?.products?.flatMap { it.filters }
                                ?.find { it.key == typeFilters[0] }
                        selectedFilter?.label?.let {
                            tvFilterProduct.isSelected = true
                            tvFilterProduct.text = it
                        } ?: run {
                            tvFilterProduct.isSelected = false
                            tvFilterProduct.text = getString(R.string.select_product)
                        }
                    } else {
                        tvFilterProduct.isSelected = true
                        tvFilterProduct.text = getString(R.string.multi_product)
                    }
                } else {
                    tvFilterProduct.isSelected = false
                    tvFilterProduct.text = getString(R.string.select_product)
                }
            }
            filtersMap[viewModel.activeTab]?.dateFilters?.let {
                if (it.startDate != null && it.startDate != 0L && it.endDate != null &&
                    it.endDate != 0L
                ) {
                    tvFilterDate.isSelected = true
                    if (it.presetValue == PaymentConst.DatePreset.CUSTOM_RANGE) {
                        safeLet(it.startDate, it.endDate) { startDate, endDate ->
                            tvFilterDate.text = getString(
                                R.string.two_dashed_strings,
                                DateTimeUtils.getFormattedDateTime(
                                    startDate,
                                    DateTimeUtils.DD_MMM_YY
                                ),
                                DateTimeUtils.getFormattedDateTime(
                                    endDate,
                                    DateTimeUtils.DD_MMM_YY
                                )
                            )
                        }
                    } else {
                        viewModel.getApplicableFilters()?.date?.find { dateFilter ->
                            it.presetValue ==
                                dateFilter.presetValue
                        }
                            ?.let { dateFilter ->
                                tvFilterDate.text = dateFilter.label
                            }
                    }
                } else {
                    tvFilterDate.isSelected = false
                    tvFilterDate.text = getString(R.string.select_date)
                }
            }
            filtersMap[viewModel.activeTab]?.sorting.let {
                val selectedSort = viewModel.getApplicableFilters()?.sort?.find { sortOption ->
                    sortOption.key == it
                }
                selectedSort?.let {
                    tvSort.text = it.label
                    tvSort.isSelected = true
                } ?: run {
                    tvSort.text = getString(R.string.sort)
                    tvSort.isSelected = false
                }
            }
            setSearchQuery(filtersMap[viewModel.activeTab]?.searchQuery.toString())

            isFilterApplied =
                tvFilterDate.isSelected || tvFilterProduct.isSelected || tvFilterStatus.isSelected
            ivClearFilter.visibility = isFilterApplied.asVisibility()
        }
    }

    private fun setupToolbar() {
        with(binding.toolbar) {
            navigationIcon =
                <EMAIL>(R.drawable.ic_back)
            setNavigationOnClickListener {
                Utils.hideKeyboard(this@OrderHistoryActivity)
                finish()
            }
        }
    }

    private fun updateAdapter() {
        viewModel.ordersHistoryMap[viewModel.activeTab]?.let {
            ordersAdapter?.setData(it)
        }
    }

    fun getTabKey(position: Int?): PaymentConst.HistoryTabs = when (position) {
        0 -> PaymentConst.HistoryTabs.ALL
        1 -> PaymentConst.HistoryTabs.PPOB
        2 -> PaymentConst.HistoryTabs.PEMBAYARAN
        3 -> PaymentConst.HistoryTabs.SALDO
        else -> PaymentConst.HistoryTabs.ALL
    }

    override fun openOrderDetail(order: PaymentHistory) {
        val orderType = order.type?.let {
            if (it.equals(
                    PaymentHistory.TYPE_CASHBACK_SETTLEMENT,
                    true
                )
            ) {
                PaymentHistory.TYPE_PAYMENT_OUT
            } else {
                it
            }
        }
        order.orderId?.let {
            openActivity(OrderDetailActivity::class.java) {
                putString(OrderDetailActivity.ORDER_ID, order.orderId)
                putString(OrderDetailActivity.PAYMENT_TYPE, orderType)
                putString(OrderDetailActivity.LEDGER_ACCOUNT_ID, order.ledgerAccountId)
            }
        }
    }

    override fun filterResultCount(result: Int) {
        binding.tvResultCount.visibility = (result != 0).asVisibility()
        if (result > 0) {
            binding.tvResultCount.text =
                SpannableStringBuilder(getString(R.string.result_count, result)).colorText(
                    result.toString(),
                    getColorCompat(R.color.colorPrimary),
                    true
                )
        }
    }

    override fun fetchLinkedOrders(orderId: String, adapterPos: Int) {
        viewModel.fetchLinkedOrders(orderId, adapterPos)
    }

    override fun openCashbackInfo() {
        openActivity(WebviewActivity::class.java) {
            putString(ClassConstants.WEBVIEW_URL, PaymentRemoteConfig.getSaldoUrl())
            putString("title", getString(R.string.filter_history_saldo_bonus))
        }
    }

    override fun spendCashbacks() {
    }

    override fun onPpobSelected(fragmentBodyBlock: HomePageBodyContents?) {
        fragmentBodyBlock?.let {
            openActivity(WebviewActivity::class.java) {
                putString(ClassConstants.WEBVIEW_URL, fragmentBodyBlock.deeplinkUrl)
                putString("type", fragmentBodyBlock.deeplinkType)
                putString("title", fragmentBodyBlock.displayName)
            }
        }
    }
}
