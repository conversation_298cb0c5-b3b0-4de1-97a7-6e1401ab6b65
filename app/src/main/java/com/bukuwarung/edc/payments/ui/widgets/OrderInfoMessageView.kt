package com.bukuwarung.edc.payments.ui.widgets

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.OrderInfoMessageViewBinding
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.payments.data.model.OrderResponse
import com.bukuwarung.edc.payments.util.PaymentAuxilliary
import com.bukuwarung.edc.util.getColorCompat
import com.bukuwarung.edc.util.getDrawableCompat
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick

class OrderInfoMessageView(context: Context, attrs: AttributeSet) :
    ConstraintLayout(context, attrs) {

    private val binding: OrderInfoMessageViewBinding =
        OrderInfoMessageViewBinding.inflate(LayoutInflater.from(context), this, true)

    fun setView(
        order: OrderResponse?,
        paymentType: String?,
        refreshScreen: () -> Unit,
        showViewInRed: Boolean = false
    ) {
        if (!(
                PaymentAuxilliary.isPaymentOut(
                    paymentType
                ) || PaymentAuxilliary.isPpob(paymentType)
                )
        ) {
            <EMAIL>()
            return
        }
        with(binding) {
            when (order?.status) {
                PaymentConst.STATUS_PENDING, PaymentConst.STATUS_PAID, PaymentConst.STATUS_HOLD -> {
                    tvInfoMessage.apply {
                        text =
                            context.getString(
                                if (showViewInRed) {
                                    R.string.longer_pending_trx_message
                                } else {
                                    R.string.pending_trx_time_message
                                }
                            )
                        setTextColor(
                            context.getColorCompat(
                                if (showViewInRed) R.color.red_80 else R.color.black_60
                            )
                        )
                        setCompoundDrawablesRelativeWithIntrinsicBounds(
                            0,
                            0,
                            0,
                            0
                        )
                    }
                    clInfoMessageContainer.background =
                        context.getDrawableCompat(
                            if (showViewInRed) {
                                R.drawable.bg_rounded_rectangle_red_5
                            } else {
                                R.drawable.bg_solid_yellow5_corder_10dp
                            }
                        )
                    btnLearn.setTextColor(
                        context.getColorCompat(
                            if (showViewInRed) R.color.white else R.color.cta_text_button
                        )
                    )
                    btnLearn.setBackgroundColor(
                        context.getColorCompat(
                            if (showViewInRed) R.color.red_80 else R.color.yellow
                        )
                    )
                    btnLearn.showView()
                    btnLearn.singleClick { refreshScreen() }
                    <EMAIL>()
                }

                PaymentConst.STATUS_FAILED -> {
                    when (order.progress?.lastOrNull()?.state) {
                        PaymentConst.STATUS_REFUNDED -> {
                            tvInfoMessage.apply {
                                text = context.getString(R.string.refunded_trx_message)
                                setCompoundDrawablesRelativeWithIntrinsicBounds(
                                    R.drawable.vector_progress_icon_active,
                                    0,
                                    0,
                                    0
                                )
                            }
                            clInfoMessageContainer.background =
                                context.getDrawableCompat(
                                    R.drawable.bg_solid_green5_corner_8dp_stroke_green10
                                )
                        }

                        PaymentConst.STATUS_REFUNDING_FAILED -> {
                            tvInfoMessage.apply {
                                text = context.getString(R.string.contact_manual_refund)
                                setTextColor(context.getColorCompat(R.color.black_80))
                                setCompoundDrawablesRelativeWithIntrinsicBounds(0, 0, 0, 0)
                            }
                            clInfoMessageContainer.background =
                                context.getDrawableCompat(R.drawable.bg_rounded_rectangle_red_5)
                        }

                        else -> {
                            tvInfoMessage.apply {
                                text = context.getString(R.string.refunding_trx_message)
                                setCompoundDrawablesRelativeWithIntrinsicBounds(
                                    R.drawable.ic_info,
                                    0,
                                    0,
                                    0
                                )
                            }
                            clInfoMessageContainer.background =
                                context.getDrawableCompat(
                                    R.drawable.bg_solid_blue15_corner_8dp_stroke_blue40
                                )
                        }
                    }
                    <EMAIL>()
                }

                else -> {
                    <EMAIL>()
                }
            }
        }
    }
}
