package com.bukuwarung.edc.payments.ui.core

import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.CountDownTimer
import androidx.activity.OnBackPressedCallback
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import com.airbnb.lottie.LottieDrawable
import com.bukuwarung.edc.R
import com.bukuwarung.edc.app.config.VariantConfig
import com.bukuwarung.edc.card.domain.model.consts.Constants
import com.bukuwarung.edc.card.ui.receipt.CardReceiptViewModel
import com.bukuwarung.edc.databinding.ActivityPaymentStatusBinding
import com.bukuwarung.edc.global.Constant.TAG_PRINT
import com.bukuwarung.edc.global.network.Status
import com.bukuwarung.edc.homepage.ui.home.HomePageActivity
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.payments.data.model.BankAccount
import com.bukuwarung.edc.payments.ui.core.OrderDetailActivity.Companion.ORDER_ID
import com.bukuwarung.edc.payments.ui.core.OrderDetailActivity.Companion.PAYMENT_TYPE
import com.bukuwarung.edc.payments.ui.widgets.BankAccountView
import com.bukuwarung.edc.printer.util.PaxPrinterUtils
import com.bukuwarung.edc.util.ToastUtil.setToast
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.Utils.bwLog
import com.bukuwarung.edc.util.getColorCompat
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.milliSecondsToSeconds
import com.bukuwarung.edc.util.orNil
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick
import dagger.hilt.android.AndroidEntryPoint
import java.util.Calendar
import javax.inject.Inject

@AndroidEntryPoint
class PaymentStatusActivity : AppCompatActivity() {

    companion object {
        const val DISBURSEMENT_ID = "disbursement_id"
        const val BANK_ACCOUNT = "bank_account"
    }

    private lateinit var binding: ActivityPaymentStatusBinding
    private val paymentViewModel: PaymentViewModel by viewModels()

    @Inject
    lateinit var variantConfig: VariantConfig

    private val disbursementId: String by lazy { intent.getStringExtra(DISBURSEMENT_ID).orEmpty() }
    private val bankAccount by lazy { intent?.getParcelableExtra(BANK_ACCOUNT) as? BankAccount }
    private var timer: CountDownTimer? = null
    private var apiCallTimeInMillis: Long? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityPaymentStatusBinding.inflate(layoutInflater)
        setContentView(binding.root)

        if (variantConfig.variantIdentifier == "MINIATMPRO") {
            binding.clPaymentStatus.setBackgroundColor(getColorCompat(R.color.colorPrimary))
        }

        onBackPressedDispatcher.addCallback(
            this,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    launchSingleTopHome()
                }
            }
        )

        with(binding) {
            ivClose.setOnClickListener { launchSingleTopHome() }
            paymentViewModel.setDisbursementId(disbursementId)
            val paymentOutStatusPollingTotalTime: Long = 30 * 1000
            val paymentOutStatusPollingInterval: Long = 5 * 1000
            timer = object :
                CountDownTimer(paymentOutStatusPollingTotalTime, paymentOutStatusPollingInterval) {
                override fun onTick(millisUntilFinished: Long) {
                    if (apiCallTimeInMillis == null) {
                        apiCallTimeInMillis =
                            Calendar.getInstance().timeInMillis
                    }
                    paymentViewModel.getPaymentStatus()
                }

                override fun onFinish() {
                    finishAndRedirectToPaymentHistoryScreen()
                }
            }
            timer?.start()
            btnViewPaymentDetails.text = getString(
                R.string.view_payment_details_with_time,
                paymentOutStatusPollingTotalTime.milliSecondsToSeconds().toString()
            )
            btnViewPaymentDetails.setOnClickListener { finishAndRedirectToPaymentHistoryScreen() }
            bankAccount?.let {
                bankView.setBankView(
                    it,
                    BankAccountView.BankStatus.VERIFIED
                )
            }
        }

        paymentViewModel.paymentStatus.observe(this) {
            when (it.status) {
                Status.SUCCESS -> {
                    when (it.data?.status) {
                        PaymentConst.STATUS_PENDING -> {
                            setPendingStatusView()
                        }

                        PaymentConst.STATUS_COMPLETED -> {
                            setSuccessStatusView()
                        }

                        else -> {
                            setFailedStatusView()
                        }
                    }
                }

                Status.ERROR -> {
                    finishAndRedirectToPaymentHistoryScreen()
                }

                Status.LOADING -> {
                }

                Status.NO_INTERNET -> {
                }
            }
        }

        paymentViewModel.orderDetail.observe(this) {
            if (it.status == Status.SUCCESS) {
                it.data?.let {
                    with(binding.includeOrderInvoice) {
                        orderInvoice.apply {
                            makeInvoice(it, it.items?.getOrNull(0)?.sku)
                            showView()
                        }
                        this.root.showView()

                        btnPrint.singleClick {
                            bwLog("PaymentStatusActivity", "btnPrint.singleClick")
                            var textToPrint = orderInvoice.extractTextForPaxPrinting()
                            when (Build.MANUFACTURER) {
                                Constants.DEVICE_MANUFACTURER_VERIFONE -> {
                                    textToPrint = orderInvoice.extractTextForVerifonePrinting()
                                    paymentViewModel.printPaymentReceipt(textToPrint)
                                }

                                Constants.DEVICE_MANUFACTURER_PAX -> {
                                    PaxPrinterUtils.printMultiline(textToPrint)
                                    PaxPrinterUtils.commitPrintJob()
                                }

                                else -> {
                                    paymentViewModel.printPaymentOutReceipt(it)
                                }
                            }
                        }
                    }
                }
            }
        }

        paymentViewModel.printState.observe(this) {
            when (it) {
                is CardReceiptViewModel.State.SetPrintError -> {
                    bwLog(
                        TAG_PRINT,
                        "Print error [${it.errorStatus.errorCode}]: ${it.errorStatus.msg}"
                    )
                    setToast(
                        this,
                        it.errorStatus.errorLevel,
                        it.errorStatus.msg,
                        binding.root
                    )
                }

                else -> {
                }
            }
        }
    }

    private fun finishAndRedirectToPaymentHistoryScreen() {
        finish()
        timer?.cancel()
        startActivities(
            arrayOf(
                Intent(this, HomePageActivity::class.java).apply {
                    flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
                },
                Intent(this, OrderDetailActivity::class.java).apply {
                    putExtra(ORDER_ID, disbursementId)
                    putExtra(PAYMENT_TYPE, PaymentConst.TYPE_PAYMENT_OUT)
                }
            )
        )
    }

    private fun setPendingStatusView() = with(binding) {
        lavAnimation.setAnimation(R.raw.hourglass)
        lavAnimation.repeatCount = LottieDrawable.INFINITE
        tvPaymentStatus.text = getString(R.string.payment_out_status_pending_title)
        tvStatusInfo.text = getString(R.string.payment_out_status_pending_subtitle)
        tvTotalPaymentAmount.text = Utils.formatAmount(paymentViewModel.paymentOutStatus?.amount)
    }

    private fun setSuccessStatusView() = with(binding) {
        lavAnimation.setAnimation(R.raw.checkmark_success)
        lavAnimation.repeatCount = LottieDrawable.INFINITE
        tvPaymentStatus.text = getString(R.string.payment_out_status_success_title)
        tvStatusInfo.text = getString(R.string.payment_out_status_success_subtitle)
        tvTotalPaymentAmount.text = Utils.formatAmount(paymentViewModel.paymentOutStatus?.amount)
        btnViewPaymentDetails.hideView()
        tvFastPayment.showView()
        tvPaymentCompletionTime.showView()
        val timeToCompleteTrx =
            (Calendar.getInstance().timeInMillis - apiCallTimeInMillis.orNil).toDouble()
                .milliSecondsToSeconds()
        tvPaymentCompletionTime.text = getString(
            R.string.payment_completed_time,
            timeToCompleteTrx.toString()
        )
        vwReference.showView()
        timer?.cancel()
    }

    private fun setFailedStatusView() = with(binding) {
        lavAnimation.setAnimation(R.raw.trx_failed)
        lavAnimation.repeatCount = LottieDrawable.INFINITE
        tvPaymentStatus.text = getString(R.string.payment_out_status_failed_title)
        tvStatusInfo.text = getString(R.string.payment_out_status_failed_subtitle)
        tvTotalPaymentAmount.text = Utils.formatAmount(paymentViewModel.paymentOutStatus?.amount)
        timer?.cancel()
        btnViewPaymentDetails.text = getString(R.string.view_payment_details)
    }

    override fun onDestroy() {
        bwLog(e = Exception("PaymentStatusActivity-onDestroy"))
        super.onDestroy()
        timer?.cancel()
    }

    private fun launchSingleTopHome() {
        // TODO: Should redirect to payment history
        val intent = Intent(this, HomePageActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP)
        startActivity(intent)
    }
}
