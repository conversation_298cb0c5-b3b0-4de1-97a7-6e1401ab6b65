package com.bukuwarung.edc.payments.ui.history.bottomsheet

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.ApiErrorBottomsheetBinding
import com.bukuwarung.lib.webview.bottomsheet.BaseBottomSheetDialogFragment
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class ErrorBottomSheet : BaseBottomSheetDialogFragment() {

    companion object {
        private const val ERROR_TYPE = "error_type"
        private const val MESSAGE = "message"
        private const val ERROR_TITLE = "error_title"
        private const val CTA_TEXT = "cta_text"
        const val TAG = "ApiErrorBottomSheet"

        enum class ApiErrorType {
            SERVER_UNREACHABLE,
            SERVER_ERROR,
            CONNECTION_ERROR,
            API_ERROR,
            CUSTOM
        }

        fun createInstance(
            errorType: ApiErrorType,
            message: String?,
            errorTitle: String? = null,
            ctaText: String? = null
        ) = ErrorBottomSheet().apply {
            val bundle = Bundle().apply {
                putSerializable(ERROR_TYPE, errorType)
                putString(MESSAGE, message)
                putString(ERROR_TITLE, errorTitle)
                putString(CTA_TEXT, ctaText)
            }
            arguments = bundle
        }
    }

    interface Callback {
        fun onDismiss()
        fun onButtonClicked()
    }

    private val errorType by lazy { arguments?.getSerializable(ERROR_TYPE) as ApiErrorType }
    private val message by lazy { arguments?.getString(MESSAGE) }
    private val errorTitle by lazy { arguments?.getString(ERROR_TITLE) }
    private val ctaText by lazy { arguments?.getString(CTA_TEXT) }
    private var _binding: ApiErrorBottomsheetBinding? = null
    private val binding get() = _binding!!

    private var callback: Callback? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(DialogFragment.STYLE_NO_FRAME, R.style.BottomSheetDialogTheme)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = ApiErrorBottomsheetBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        parentFragment?.let { callback = it as? Callback }
        if (context is Callback) callback = context
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        with(binding) {
            ivClose.setOnClickListener {
                dismiss()
                callback?.onDismiss()
            }
            when (errorType) {
                ApiErrorType.SERVER_UNREACHABLE -> {
                    bukuErrorView.text = "Server Unreachable"
                }

                ApiErrorType.SERVER_ERROR -> {
                    bukuErrorView.text = "Server Error"
                }

                ApiErrorType.CONNECTION_ERROR -> {
                    bukuErrorView.text = "Connection Error"
                }

                ApiErrorType.API_ERROR -> {
                    bukuErrorView.text = "API Error"
                }

                ApiErrorType.CUSTOM -> {
                    bukuErrorView.text = "Some Error Occured"
                }
            }
        }
    }
}
