package com.bukuwarung.edc.payments.ui.core

import android.app.Activity
import android.graphics.Typeface
import android.os.Bundle
import android.view.MenuItem
import android.widget.Toast
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.core.view.children
import com.bukuwarung.analytic.BtAnalytics.bwLog
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.ActivityPaymentInputBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.global.network.Status
import com.bukuwarung.edc.payments.constant.PaymentAnalyticsConst
import com.bukuwarung.edc.payments.constant.PaymentRemoteConfig
import com.bukuwarung.edc.payments.data.model.BankAccount
import com.bukuwarung.edc.payments.data.model.KycTier
import com.bukuwarung.edc.payments.data.model.PaymentCategoryItem
import com.bukuwarung.edc.payments.ui.category.PaymentCategoryActivity
import com.bukuwarung.edc.payments.ui.core.PaymentConfirmationActivity.Companion.AMOUNT
import com.bukuwarung.edc.payments.ui.core.PaymentConfirmationActivity.Companion.PAYMENT_CATEGORY_ID
import com.bukuwarung.edc.payments.ui.widgets.BankAccountView
import com.bukuwarung.edc.util.EncryptedPreferencesHelper
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.afterTextChanged
import com.bukuwarung.edc.util.getColorCompat
import com.bukuwarung.edc.util.getDrawableCompat
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.ifNull
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.orNil
import com.bukuwarung.edc.util.showView
import com.bukuwarung.network.NetworkConst.BUKUWARUNG_TOKEN
import com.bukuwarung.network.utils.ResourceState
import com.google.android.material.chip.Chip
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class PaymentInputActivity : AppCompatActivity() {

    companion object {
        const val BANK_ACCOUNT = "bank_account"
        const val PAYMENT_CATEGORY = "payment_category"
    }

    private lateinit var binding: ActivityPaymentInputBinding

    private val paymentViewModel: PaymentViewModel by viewModels()
    private val bankAccount by lazy { intent?.getParcelableExtra(BANK_ACCOUNT) as? BankAccount }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityPaymentInputBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        setBankAccountDetails()
        Analytics.trackEvent(PaymentAnalyticsConst.PAYMENT_AMOUNT_SELECTION_PAGE_VISITED)

        paymentViewModel.getCategories()
        paymentViewModel.getLimits()
        paymentViewModel.getSaldo()

        paymentViewModel.categories.observe(this) {
            when (it.status) {
                Status.SUCCESS -> {
                    it?.data?.let { showCategories(it) }
                }

                else -> {}
            }
        }

        paymentViewModel.saldo.observe(this) {
            when (it) {
                is ResourceState.Loading -> {
                }

                is ResourceState.Success -> {
                    val balance = it.data.subBalance?.saldo.ifNull { it.data.amount }
                    binding.tvSaldoBalanceValue.text = Utils.formatAmount(balance)
                }

                is ResourceState.Failure -> {
                    Toast.makeText(this, it.message, Toast.LENGTH_SHORT).show()
                    bwLog(it.message, Exception(it.throwable))
                }
            }
        }

        paymentViewModel.event.observe(this) {
            when (it) {
                is PaymentViewModel.Event.SetSelectedCategory -> {
                    setSelectedCategory(it.category)
                }
            }
        }

        with(binding) {
            with(includePaymentCategories) {
                hsvCategory.setBackgroundColor(getColorCompat(R.color.transparent))
                vwDivider.hideView()
                chipAllCategories.apply {
                    chipIcon = getDrawableCompat(R.drawable.ic_all_category)
                    chipStrokeColor = ContextCompat.getColorStateList(
                        this@PaymentInputActivity,
                        R.color.payment_chip_stroke_tint
                    )
                    text = getString(R.string.category)
                    setOnClickListener {
                        chipAllCategories.isChecked = false
                        startPaymentCategoryActivityForResult.launch(
                            PaymentCategoryActivity.createIntent(
                                this@PaymentInputActivity,
                                paymentViewModel.selectedPaymentCategory?.paymentCategoryId
                            )
                        )
                    }
                }
            }

            btnContinue.apply {
                isEnabled = !paymentViewModel.checkDisableButton(etAmount.getNumberValue())
                setOnClickListener {
                    Analytics.trackEvent(PaymentAnalyticsConst.PAYMENT_AMOUNT_INPUTTED)
                    goToPaymentConfirmation()
                }
            }
            etAmount.afterTextChanged {
                val amount = etAmount.getNumberValue()
                checkAmountEligibility(amount)
                etAmount.setSelection(binding.etAmount.length())
                btnContinue.isEnabled = !paymentViewModel.checkDisableButton(amount)
            }
            etAmount.setOnFocusChangeListener { _, hasFocus ->
                if (hasFocus) {
                    etAmount.setSelection(etAmount.length())
                }
            }
            etAmount.setSelection(etAmount.length())
            etAmount.requestFocus()
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> onBackPressedDispatcher.onBackPressed()
        }
        return super.onOptionsItemSelected(item)
    }

    private fun setBankAccountDetails() {
        bankAccount?.let {
            binding.bankAccountView.setBankView(
                it,
                BankAccountView.BankStatus.VERIFIED,
                null
            )
        }
    }

    private fun checkAmountEligibility(amount: Long) {
        with(binding) {
            when {
                paymentViewModel.isBelowThreshold(amount) -> {
                    tvAmountError.showView()
                    tvAmountError.text = getString(
                        R.string.error_minimum_payment_limit,
                        Utils.formatAmount(PaymentRemoteConfig.getMinimumPaymentOutAmount())
                    )
                    vwDivider.setBackgroundColor(getColorCompat(R.color.red_80))
                }

                paymentViewModel.isAboveThreshold(amount) -> {
                    performMaxLimitChecks(binding.etAmount.getNumberValue())
                }

                paymentViewModel.isMoreThanSaldoBalance(amount) -> {
                    tvAmountError.showView()
                    tvAmountError.text = getString(R.string.saldo_balance_not_enough)
                    vwDivider.setBackgroundColor(getColorCompat(R.color.red_80))
                }

                else -> {
                    tvAmountError.hideView()
                    binding.vwDivider.setBackgroundColor(getColorCompat(R.color.colorPrimary))
                }
            }
        }
    }

    private fun showCategories(categories: List<PaymentCategoryItem>) {
        with(binding) {
            includePaymentCategories.cgPaymentCategories.removeAllViews()
            for (i in 0 until 3) {
                if (i >= categories.size) return
                val chip = layoutInflater.inflate(
                    R.layout.payment_category_chip,
                    includePaymentCategories.cgPaymentCategories,
                    false
                ) as Chip
                chip.chipStrokeColor = ContextCompat.getColorStateList(
                    this@PaymentInputActivity,
                    R.color.payment_chip_stroke_tint
                )
                chip.apply {
                    text = categories[i].name
                    id = i
                    isCloseIconVisible = false
                    isCheckedIconVisible = false
                }
                includePaymentCategories.cgPaymentCategories.addView(chip)
            }
            includePaymentCategories.cgPaymentCategories.setOnCheckedChangeListener {
                    group,
                    chipId
                ->
                if (chipId == -1) return@setOnCheckedChangeListener
                paymentViewModel.assignPaymentCategory(categories[chipId])
                group.children.forEach {
                    (it as Chip?)?.typeface = Typeface.DEFAULT
                }
                val chip = group.getChildAt(group.checkedChipId) as Chip?
                chip?.typeface = Typeface.DEFAULT_BOLD
                includePaymentCategories.chipAllCategories.isChecked = false
                includePaymentCategories.chipAllCategories.typeface = Typeface.DEFAULT
            }
        }
    }

    /**
     * Makes selected category chip to be active. In case it's not in the chip group,
     * then "other" categories chip is made active.
     * It loops through the Chips in the chip group and finds the name of the chip that
     * matches the selected category. Checking through name is fine because this is a in-memory data
     * and we can expect it to always be there.
     */
    private fun setSelectedCategory(category: PaymentCategoryItem) {
        var chipFound = false
        with(binding.includePaymentCategories) {
            cgPaymentCategories.children.forEach {
                (it as Chip?)?.let { chip ->
                    if (chip.text == category.name) {
                        chip.isChecked = true
                        chip.typeface = Typeface.DEFAULT_BOLD
                        chipFound = true
                    } else {
                        chip.isChecked = false
                        chip.typeface = Typeface.DEFAULT
                    }
                }
            }
            chipAllCategories.apply {
                isChecked = !chipFound
                typeface = if (!chipFound) Typeface.DEFAULT_BOLD else Typeface.DEFAULT
            }
        }
    }

    private val startPaymentCategoryActivityForResult =
        registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result: ActivityResult ->
            if (result.resultCode == Activity.RESULT_OK) {
                result.data?.getParcelableExtra<PaymentCategoryItem>(PAYMENT_CATEGORY)
                    ?.let {
                        paymentViewModel.assignPaymentCategory(it)
                        setSelectedCategory(it)
                    }
            }
        }

    private fun goToPaymentConfirmation() {
        openActivity(PaymentConfirmationActivity::class.java) {
            putParcelable(BANK_ACCOUNT, bankAccount)
            putLong(AMOUNT, binding.etAmount.getNumberValue())
            putString(
                PAYMENT_CATEGORY_ID,
                paymentViewModel.selectedPaymentCategory?.paymentCategoryId
            )
        }
    }

    /**
     * Performs following checks on the entered amount
     * 1. Max Daily transaction limit check
     * 2. Max Per transaction limit check
     */
    private fun performMaxLimitChecks(amount: Long) {
        paymentViewModel.limits?.let {
            val infoText = when (
                Utils.getKycTierFromToken(
                    EncryptedPreferencesHelper.get(
                        BUKUWARUNG_TOKEN,
                        ""
                    )
                )
            ) {
                KycTier.NON_KYC -> {
                    when {
                        amount > it.remainingDailyTrxLimit.orNil -> {
                            setMaximumAmountLimitError(
                                R.string.todays_payment_limit_is,
                                it.remainingDailyTrxLimit
                            )
                            getString(R.string.must_verify_account_for_payments)
                        }

                        amount > it.perTrxLimit.orNil -> {
                            setMaximumAmountLimitError(R.string.maximum_x, it.perTrxLimit)
                            getString(R.string.must_verify_account_for_payments)
                        }

                        else -> {
                            setMaximumAmountLimitError(null, null)
                            ""
                        }
                    }
                }

                KycTier.ADVANCED -> {
                    when {
                        amount > it.remainingDailyTrxLimit.orNil -> {
                            setMaximumAmountLimitError(
                                R.string.todays_payment_limit_is,
                                it.remainingDailyTrxLimit
                            )
                            getString(
                                R.string.upgrade_kyc_to_increase_daily_limit,
                                Utils.formatAmount(it.dailyTrxLimit)
                            )
                        }

                        amount > it.perTrxLimit.orNil -> {
                            setMaximumAmountLimitError(R.string.maximum_x, it.perTrxLimit)
                            getString(
                                R.string.upgrade_kyc_to_increase_trx_limit,
                                Utils.formatAmount(it.perTrxLimit)
                            )
                        }

                        amount > it.whitelistLimits?.remainingTrxAmountLimit.orNil -> {
                            setMaximumAmountLimitError(
                                R.string.remaining_limit_x,
                                it.whitelistLimits?.remainingTrxAmountLimit
                            )
                            getString(
                                R.string.whitelisted_user_limit_reached,
                                Utils.formatAmount(it.whitelistLimits?.maxTrxAmountLimit)
                            )
                        }

                        else -> {
                            setMaximumAmountLimitError(null, null)
                            ""
                        }
                    }
                }

                KycTier.SUPREME -> {
                    when {
                        amount > it.remainingDailyTrxLimit.orNil -> {
                            setMaximumAmountLimitError(
                                R.string.todays_payment_limit_is,
                                it.remainingDailyTrxLimit
                            )
                            getString(
                                R.string.upgrade_kyc_to_increase_daily_limit_supreme,
                                Utils.formatAmount(it.dailyTrxLimit)
                            )
                        }

                        amount > it.perTrxLimit.orNil -> {
                            setMaximumAmountLimitError(R.string.maximum_x, it.perTrxLimit)
                            getString(
                                R.string.upgrade_kyc_to_increase_trx_limit_supreme,
                                Utils.formatAmount(it.perTrxLimit)
                            )
                        }

                        else -> {
                            setMaximumAmountLimitError(null, null)
                            ""
                        }
                    }
                }
            }
            binding.tvAmountError.showView()
            binding.tvAmountError.text = infoText
            binding.vwDivider.setBackgroundColor(getColorCompat(R.color.red_80))
        } ?: run {
            setMaximumAmountLimitError(null, null)
        }
    }

    private fun setMaximumAmountLimitError(messageRes: Int?, maxLimit: Double?) {
        with(binding) {
            messageRes?.let {
                tvAmountError.showView()
                tvAmountError.text = getString(
                    it,
                    Utils.formatAmount(maxLimit)
                )
                vwDivider.setBackgroundColor(getColorCompat(R.color.red_80))
            } ?: run {
                tvAmountError.hideView()
                tvAmountError.text = ""
                vwDivider.setBackgroundColor(getColorCompat(R.color.black_10))
            }
        }
    }
}
