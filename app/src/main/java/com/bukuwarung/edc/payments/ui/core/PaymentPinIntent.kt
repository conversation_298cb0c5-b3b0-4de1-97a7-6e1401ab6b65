package com.bukuwarung.edc.payments.ui.core

import com.bukuwarung.edc.payments.constant.PinType

sealed class PaymentPinIntent {
    data class OnCreateView(val useCase: PinType, val skipOtpCall: Boolean = false) :
        PaymentPinIntent()

    data class OnRequestOTP(val forceRequest: Boolean = false) : PaymentPinIntent()
    data class OnVerifyOTP(val otp: String) : PaymentPinIntent()
    data class AddDigit(val digit: Int) : PaymentPinIntent()
    object DeleteDigit : PaymentPinIntent()
    object ConfirmNewPin : PaymentPinIntent()
    object ClickedCreatePinView : PaymentPinIntent()
    object ClickedConfirmPinView : PaymentPinIntent()
    object ChangeOtpChannel : PaymentPinIntent()
}
