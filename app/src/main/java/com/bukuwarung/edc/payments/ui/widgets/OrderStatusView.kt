package com.bukuwarung.edc.payments.ui.widgets

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.DrawableCompat
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.OrderStatusViewBinding
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.payments.data.model.OrderResponse
import com.bukuwarung.edc.util.DateTimeUtils
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.showView

class OrderStatusView(context: Context, attrs: AttributeSet) :
    ConstraintLayout(context, attrs) {

    private val binding: OrderStatusViewBinding =
        OrderStatusViewBinding.inflate(LayoutInflater.from(context), this, true)

    fun setView(order: OrderResponse) {
        with(binding) {
            when (order.status) {
                PaymentConst.STATUS_PENDING, PaymentConst.STATUS_PAID, PaymentConst.STATUS_HOLD -> {
                    tvStatus.apply {
                        text = context.getString(R.string.in_the_process)
                        setCompoundDrawablesRelativeWithIntrinsicBounds(
                            R.drawable.ic_order_in_process,
                            0,
                            0,
                            0
                        )
                        DrawableCompat.setTint(
                            background,
                            ContextCompat.getColor(context, R.color.yellow)
                        )
                    }
                }

                PaymentConst.STATUS_COMPLETED -> {
                    tvStatus.apply {
                        text = context.getString(R.string.completed)
                        setCompoundDrawablesRelativeWithIntrinsicBounds(
                            R.drawable.ic_order_completed,
                            0,
                            0,
                            0
                        )
                        DrawableCompat.setTint(
                            background,
                            ContextCompat.getColor(context, R.color.green_100)
                        )
                    }
                }

                PaymentConst.STATUS_FAILED -> {
                    when (order.progress?.lastOrNull()?.state) {
                        PaymentConst.STATUS_REFUNDING -> {
                            tvStatus.apply {
                                text = context.getString(R.string.refund_processed)
                                setCompoundDrawablesRelativeWithIntrinsicBounds(
                                    R.drawable.ic_order_in_process,
                                    0,
                                    0,
                                    0
                                )
                                DrawableCompat.setTint(
                                    background,
                                    ContextCompat.getColor(context, R.color.yellow)
                                )
                            }
                        }

                        PaymentConst.STATUS_REFUNDED -> {
                            tvStatus.apply {
                                text = context.getString(R.string.refund_successful)
                                setCompoundDrawablesRelativeWithIntrinsicBounds(
                                    R.drawable.ic_order_completed,
                                    0,
                                    0,
                                    0
                                )
                                DrawableCompat.setTint(
                                    background,
                                    ContextCompat.getColor(context, R.color.green_100)
                                )
                            }
                        }

                        PaymentConst.STATUS_REFUNDING_FAILED -> {
                            tvStatus.apply {
                                text = context.getString(R.string.refund_failed)
                                setCompoundDrawablesRelativeWithIntrinsicBounds(
                                    R.drawable.ic_order_failed,
                                    0,
                                    0,
                                    0
                                )
                                DrawableCompat.setTint(
                                    background,
                                    ContextCompat.getColor(context, R.color.red_80)
                                )
                            }
                        }

                        else -> {
                            tvStatus.apply {
                                text = context.getString(R.string.failed)
                                setCompoundDrawablesRelativeWithIntrinsicBounds(
                                    R.drawable.ic_order_failed,
                                    0,
                                    0,
                                    0
                                )
                                DrawableCompat.setTint(
                                    background,
                                    ContextCompat.getColor(context, R.color.red_80)
                                )
                            }
                        }
                    }
                }

                PaymentConst.STATUS_CANCELLED -> {
                    tvStatus.apply {
                        text = context.getString(R.string.failed)
                        setCompoundDrawablesRelativeWithIntrinsicBounds(
                            R.drawable.ic_order_cancelled,
                            0,
                            0,
                            0
                        )
                        DrawableCompat.setTint(
                            background,
                            ContextCompat.getColor(context, R.color.black_20)
                        )
                    }
                }

                else -> {
                }
            }
            tvDate.text = DateTimeUtils.getLocalStringFromUtc(
                order.createdAt,
                DateTimeUtils.DD_MMM_YYYY_HH_MM
            )
            <EMAIL>()
            tvEdcTag.visibility =
                order.metadata?.bukuOrigin.equals(Constant.BUKUWARUNG_EDC).asVisibility()
        }
    }
}
