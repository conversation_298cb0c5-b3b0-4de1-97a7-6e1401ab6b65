package com.bukuwarung.edc.payments.data.model

import com.google.gson.annotations.SerializedName

data class PaymentHealthCheckResponse(
    @SerializedName("money_in")
    val moneyIn: MoneyInHealthResponse? = null,

    @SerializedName("money_out")
    val moneyOut: List<HealthStatus>? = null,

    @SerializedName("providers")
    val providers: List<HealthStatus>? = null
)

data class MoneyInHealthResponse(
    @SerializedName("payment_in")
    val paymentIn: List<HealthStatus>? = null,

    @SerializedName("payment_out")
    val paymentOut: List<HealthStatus>? = null
)
