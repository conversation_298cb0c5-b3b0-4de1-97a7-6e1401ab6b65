package com.bukuwarung.edc.payments.ui.contacts

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.widget.doOnTextChanged
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.ContactSearchTextLayoutBinding
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.getColorCompat
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.setupForSearch
import com.bukuwarung.edc.util.showView
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class ContactSearchFragment : Fragment() {
    private val viewModel: ContactSearchViewModel by viewModels({ requireParentFragment() })
    private var iUserContactFragmentCommunicator: IUserContactFragmentCommunicator? = null
    private var useCase = CustomerSearchUseCase.ACCOUNTING
    private var contactSearchFragmentBinding: ContactSearchTextLayoutBinding? = null
    private val binding get() = contactSearchFragmentBinding!!
    private var orderId = ""

    override fun onAttach(context: Context) {
        super.onAttach(context)
        iUserContactFragmentCommunicator = parentFragment as? IUserContactFragmentCommunicator
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        contactSearchFragmentBinding =
            ContactSearchTextLayoutBinding.inflate(inflater, container, false)
        setupView()
        return binding.root
    }

    private fun observe() {
        viewModel.inputContactNameObserver.observe(viewLifecycleOwner) {
            when (it) {
                is ContactSearchViewModel.OnContactNameInput.UpdateInputName -> {
                    if (parentFragment != null && parentFragment is OnCustomerNameEntered) {
                        (parentFragment as OnCustomerNameEntered).onCustomerNameEntered(
                            it.inputName
                        )
                    } else {
                        activity?.apply {
                            if (this is OnCustomerNameEntered) {
                                this.onCustomerNameEntered(it.inputName)
                            }
                        }
                    }
                }
            }
        }
        viewModel.contactsObserver.observe(viewLifecycleOwner) {
            when (it) {
                is ContactSearchViewModel.ContactEvent.ShowLoaderOnSearchResultsFragment -> {
                    showOrHideSearch(it.showLoader)
                }

                else -> {}
            }
        }
    }

    private fun showOrHideSearch(showLoader: Boolean) {
        binding.clSearchFragment.visibility = (!showLoader).asVisibility()
        binding.searchInput.requestFocus()
        binding.searchInput.setSelection(binding.searchInput.text.length)
    }

    private fun setupView() {
        useCase = CustomerSearchUseCase.FAVORITE
        orderId = arguments?.getString(ORDER_ID).orEmpty()
        viewModel.init(orderId)
        with(binding) {
            arguments?.apply {
                viewModel.setShowFavoriteContacts(useCase == CustomerSearchUseCase.FAVORITE)
                if (getString(QUERY) != "") {
                    searchInput.setText(getString(QUERY))
                    searchInput.setSelection(binding.searchInput.text.length)
                }
                utangContactIcon.setImageResource(R.drawable.ic_favourite_blue)
                tvModalMainTitle1.text = getString(R.string.enter_name)
                searchInput.setTextColor(requireContext().getColorCompat(R.color.blue_80))

                searchInput.requestFocus()
            }

            if (orderId.isNotBlank()) {
                searchInput.setupForSearch(
                    coroutineScope = lifecycleScope,
                    delay = 1000,
                    enableImeAction = false
                ) { newQuery ->
                    searchOnTextChange(newQuery)
                }
            } else {
                searchInput.doOnTextChanged { text, _, _, _ ->
                    searchOnTextChange(text.toString())
                }
            }
        }
        viewModel.onSearchTextChange(arguments?.getString(QUERY) ?: "")
        Utils.showKeyboard(requireContext())
        observe()
    }

    private fun searchOnTextChange(text: String) {
        var txt = text
        if (txt.isNotEmpty() && txt.length > 17) {
            txt = txt.substring(0, 14) + "..."
        }
        binding.tvError.hideView()
        iUserContactFragmentCommunicator?.onTextChanged(txt)
        viewModel.onSearchTextChange(txt)
    }

    fun showSameNameError(message: String) {
        binding.tvError.showView()
        binding.tvError.text = message
    }

    companion object {
        private const val QUERY = "query"
        private const val TRANSACTION_TYPE = "transaction_type"
        private const val USE_CASE = "use_case"
        private const val ORDER_ID = "order_id"

        fun getInstance(
            query: String = "",
            type: Int?,
            customerSearchUseCase: CustomerSearchUseCase = CustomerSearchUseCase.FAVORITE,
            orderId: String
        ): ContactSearchFragment {
            val fragment = ContactSearchFragment()
            val bundle = Bundle().apply {
                this.putString(QUERY, query)
                this.putInt(TRANSACTION_TYPE, type ?: 0)
                this.putString(USE_CASE, customerSearchUseCase.name)
                this.putString(ORDER_ID, orderId)
            }
            fragment.arguments = bundle
            return fragment
        }
    }

    fun getQueryText() = binding.searchInput.text.toString()

    override fun onStop() {
        super.onStop()
        Utils.hideKeyboard(requireActivity())
    }

    interface OnCustomerNameEntered {
        fun onCustomerNameEntered(customerNameEntered: String)
    }

    interface IUserContactFragmentCommunicator {
        fun onTextChanged(text: String)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        contactSearchFragmentBinding = null
    }
}
