package com.bukuwarung.edc.payments.ui.category

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.edc.databinding.ItemPaymentCategoryItemBinding
import com.bukuwarung.edc.payments.data.model.PaymentCategoryItem
import com.bumptech.glide.Glide
import javax.inject.Inject

class PaymentCategoryItemAdapter @Inject constructor(
    val list: List<PaymentCategoryItem>,
    val selectedCatId: String?,
    var callback: Callback
) : RecyclerView.Adapter<PaymentCategoryItemAdapter.PaymentCategoryItemViewHolder>() {

    interface Callback {
        fun onCategorySelected(category: PaymentCategoryItem)
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): PaymentCategoryItemViewHolder {
        val itemBinding = ItemPaymentCategoryItemBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return PaymentCategoryItemViewHolder(itemBinding)
    }

    override fun onBindViewHolder(holder: PaymentCategoryItemViewHolder, position: Int) {
        holder.bind(list[position])
    }

    override fun getItemCount(): Int = list.size

    inner class PaymentCategoryItemViewHolder(private val binding: ItemPaymentCategoryItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(paymentCategoryItem: PaymentCategoryItem) {
            with(binding) {
                Glide.with(root.context)
                    .load(paymentCategoryItem.logoAddress)
                    .into(ivPaymentCategory)
                tvCategoryName.text = paymentCategoryItem.name
                itemView.setOnClickListener {
                    rbPaymentCategory.isSelected = true
                    callback.onCategorySelected(list[adapterPosition])
                }
                rbPaymentCategory.isChecked =
                    selectedCatId == paymentCategoryItem.paymentCategoryId
            }
        }
    }
}
