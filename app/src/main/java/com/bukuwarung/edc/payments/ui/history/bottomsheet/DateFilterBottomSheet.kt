package com.bukuwarung.edc.payments.ui.history.bottomsheet

import android.content.res.Resources
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.edc.R
import com.bukuwarung.edc.app.config.VariantConfig
import com.bukuwarung.edc.databinding.BottomsheetDateFilterBinding
import com.bukuwarung.edc.global.base.BaseBottomSheetDialogFragment
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.payments.constant.PaymentConst.DEFAULT_FILTER_CALENDAR_MAX_RANGE
import com.bukuwarung.edc.payments.constant.PaymentRemoteConfig
import com.bukuwarung.edc.payments.data.model.DateFilter
import com.bukuwarung.edc.payments.ui.history.OrderHistoryViewModel
import com.bukuwarung.edc.payments.ui.history.adapter.DateFilterAdapter
import com.bukuwarung.edc.payments.util.RangeDateValidator
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.getClassTag
import com.bukuwarung.edc.util.orNil
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.datepicker.CalendarConstraints
import com.google.android.material.datepicker.CompositeDateValidator
import com.google.android.material.datepicker.DateValidatorPointBackward
import com.google.android.material.datepicker.MaterialDatePicker
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class DateFilterBottomSheet(
    private val isFromCardHistory: Boolean,
    private val defaultOrSelectedDateFilter: DateFilter?,
    private val dateSelectedCallback: ((DateFilter?) -> Unit)?
) : BaseBottomSheetDialogFragment(),
    DateFilterAdapter.Callback {

    companion object {
        fun createInstance(
            fr: FragmentManager,
            isFromCardHistory: Boolean = false,
            defaultOrSelectedDateFilter: DateFilter? = null,
            dateSelectedCallback: ((DateFilter?) -> Unit)? = null
        ) = DateFilterBottomSheet(
            isFromCardHistory,
            defaultOrSelectedDateFilter,
            dateSelectedCallback
        ).show(
            fr,
            getClassTag()
        )
    }

    private val viewModel: OrderHistoryViewModel by activityViewModels()

    @Inject
    lateinit var variantConfig: VariantConfig

    private var _binding: BottomsheetDateFilterBinding? = null
    private val binding get() = _binding!!
    private var dateAdapter: DateFilterAdapter? = null
    private var dateFilterForCardHistory: DateFilter? = defaultOrSelectedDateFilter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = BottomsheetDateFilterBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        dialog?.setOnShowListener {
            val dialog = it as BottomSheetDialog
            val bottomSheet =
                dialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
            bottomSheet?.let { _ ->
                dialog.behavior.state = BottomSheetBehavior.STATE_EXPANDED
                dialog.behavior.peekHeight = Resources.getSystem().displayMetrics.heightPixels - 100
            }
        }

        binding.ivClose.setOnClickListener { dismiss() }

        binding.btnConfirmFilter.setOnClickListener {
            if (isFromCardHistory) {
                dateSelectedCallback?.invoke(dateFilterForCardHistory)
            } else {
                viewModel.applyDateFilters()
            }
            dismiss()
        }

        viewModel.filtersState.let {
            val filterValues =
                if (isFromCardHistory &&
                    variantConfig.shouldUseDefaultVariantForHistoryFilter
                ) {
                    viewModel.getMiniATMProTransactionDateFilter()
                } else {
                    viewModel.getApplicableFilters()?.date
                }
            val appliedDateFilters = it[viewModel.activeTab]?.dateFilters
            Utils.safeLet(
                filterValues,
                appliedDateFilters
            ) { dateFilters, appliedFilters ->
                // Update filterValues from the appliedTypeFilters
                dateFilters.map { dateFilter ->
                    dateFilter.isChecked = dateFilter.presetValue == appliedFilters.presetValue
                }
                binding.rvDateFilters.apply {
                    layoutManager = LinearLayoutManager(requireContext())
                    dateAdapter = DateFilterAdapter(
                        isFromCardHistory,
                        defaultOrSelectedDateFilter,
                        dateFilters,
                        this@DateFilterBottomSheet
                    )
                    adapter = dateAdapter
                }
            }
        }
    }

    override fun onDateFilterSelected(dateFilter: DateFilter) {
        dateFilterForCardHistory = dateFilter
        binding.btnConfirmFilter.isEnabled =
            !(
                dateFilter.presetValue == PaymentConst.DatePreset.CUSTOM_RANGE &&
                    (dateFilter.startDate.orNil == 0L || dateFilter.endDate.orNil == 0L)
                )
    }

    override fun showDatePicker(dateFilter: DateFilter, position: Int) {
        val calendarMaxRange = PaymentRemoteConfig.getPaymentConfigs().customCalendarMaxRange
            ?: DEFAULT_FILTER_CALENDAR_MAX_RANGE
        val constraintsBuilder = CalendarConstraints.Builder()
        val validators: ArrayList<CalendarConstraints.DateValidator> = ArrayList()
        val dateValidator =
            RangeDateValidator(calendarMaxRange)
        validators.add(DateValidatorPointBackward.now())
        validators.add(dateValidator)
        constraintsBuilder.setValidator(CompositeDateValidator.allOf(validators))

        dateFilter.startDate?.let {
            constraintsBuilder.setOpenAt(it)
        }
        val calendarBuilder = MaterialDatePicker.Builder.dateRangePicker()
            .setTheme(
                if (isFromCardHistory) {
                    R.style.CardMaterialCalendarTheme
                } else {
                    R.style.MaterialCalendarTheme
                }
            )
            .setTitleText(getString(R.string.select_date_range_max_x_days, calendarMaxRange))
            .setCalendarConstraints(constraintsBuilder.build())
            .setNegativeButtonText(getString(R.string.batal))

        Utils.safeLet(dateFilter.startDate, dateFilter.endDate) { start, end ->
            calendarBuilder.setSelection(androidx.core.util.Pair(start, end))
        }

        val dateRangePicker = calendarBuilder.build()
        dateValidator.setDatePicker(dateRangePicker)

        dateRangePicker.show(childFragmentManager, dateRangePicker.toString())
        dateRangePicker.addOnPositiveButtonClickListener {
            dateFilter.startDate = it.first
            dateFilter.endDate = it.second
            dateAdapter?.updateSelectedDates(position)
            onDateFilterSelected(dateFilter)
        }
    }
}
