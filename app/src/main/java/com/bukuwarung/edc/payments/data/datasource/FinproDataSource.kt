package com.bukuwarung.edc.payments.data.datasource

import com.bukuwarung.edc.payments.data.model.ExistingTopupSaldoResponse
import com.bukuwarung.edc.payments.data.model.OrderResponse
import com.bukuwarung.edc.payments.data.model.PaymentHealthCheckRequest
import com.bukuwarung.edc.payments.data.model.PaymentHealthCheckResponse
import com.bukuwarung.edc.payments.data.model.PaymentOutStatusPollingResponse
import com.bukuwarung.edc.payments.data.model.SaldoAdminFeeResponse
import com.bukuwarung.edc.payments.data.model.SaldoResponse
import com.bukuwarung.edc.payments.data.model.TopupSaldoRequest
import com.bukuwarung.edc.payments.data.model.request.FavouriteRequest
import com.bukuwarung.edc.payments.data.model.response.AddFavouriteResponse
import com.bukuwarung.edc.ppob.common.model.Biller
import com.bukuwarung.edc.ppob.common.model.FinproAddCartRequest
import com.bukuwarung.edc.ppob.common.model.FinproGetPaymentMethodsV2Response
import com.bukuwarung.edc.ppob.common.model.PpobProductsWithBillerDetails
import com.bukuwarung.edc.ppob.confirmation.model.PpobStatusPollingResponse
import com.bukuwarung.edc.ppob.recentsandfavourites.model.DeleteFavouriteResponse
import com.bukuwarung.edc.ppob.recentsandfavourites.model.FavouriteResponse
import com.bukuwarung.edc.ppob.train.model.TrainEnrollmentDetailResponse
import com.bukuwarung.edc.ppob.train.model.TrainEnrollmentRequest
import com.bukuwarung.edc.ppob.train.model.TrainEnrollmentResponse
import com.bukuwarung.payments.data.model.FinproCheckoutOrderRequest
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Path
import retrofit2.http.Query

interface FinproDataSource {

    @GET("finpro/api/saldo")
    suspend fun getSaldo(): Response<SaldoResponse>

    @GET("finpro/api/saldo/fees")
    suspend fun getSaldoAdminFee(): Response<SaldoAdminFeeResponse>

    @GET("finpro/api/saldo/topup")
    suspend fun getExistingSaldoTopup(
        @Query("status") status: String
    ): Response<List<ExistingTopupSaldoResponse>>

    @POST("finpro/api/saldo/topup")
    suspend fun createSaldoTopup(@Body request: TopupSaldoRequest): Response<OrderResponse>

    @GET("finpro/api/{account_id}/orders/{order_id}/poll")
    suspend fun getPaymentOutStatus(
        @Path("account_id") accountId: String,
        @Path("order_id") disbursementId: String
    ): Response<PaymentOutStatusPollingResponse>

    @GET("finpro/api/{account_id}/orders/{order_id}")
    suspend fun getOrderDetail(
        @Path("account_id") accountId: String,
        @Path("order_id") orderId: String,
        @Query("ledger_account_id") ledgerAccountId: String?
    ): Response<OrderResponse>

    @POST("finpro/api/{account_id}/orders/items")
    suspend fun addItemToCart(
        @Path("account_id") accountId: String,
        @Body request: FinproAddCartRequest
    ): Response<OrderResponse>

    @GET("finpro/api/ppob/products/v2")
    suspend fun getPpobProductsWithBillerDetails(
        @Query("category") category: String,
        @Query("filter") filter: String,
        @Query("is_special_product") isSpecialProduct: Boolean?
    ): Response<PpobProductsWithBillerDetails>

    @POST("finpro/api/{account_id}/orders/enroll")
    suspend fun getTrainTicketUrl(
        @Path("account_id") accountId: String,
        @Body request: TrainEnrollmentRequest
    ): Response<TrainEnrollmentResponse>

    @GET("finpro/api/{account_id}/orders/enroll/{enrollment_id}")
    suspend fun getTrainEnrollmentDetail(
        @Path("account_id") accountId: String,
        @Path("enrollment_id") enrollmentId: String
    ): Response<TrainEnrollmentDetailResponse>

    @DELETE("finpro/api/{account_id}/orders/enroll/{order_id}/cancel")
    suspend fun cancelOrder(
        @Path("account_id") accountId: String,
        @Path("order_id") orderId: String
    )

    @DELETE("finpro/api/favourite/{bookId}/remove/{favouriteId}")
    suspend fun deleteFavourite(
        @Path("bookId") bookId: String,
        @Path("favouriteId") favouriteId: String
    ): Response<DeleteFavouriteResponse>

    @GET("finpro/api/ppob/products/billers")
    suspend fun getBillers(@Query("category") category: String?): Response<List<Biller>>

    @GET("finpro/api/favourite/{bookId}")
    suspend fun getFavourites(
        @Path("bookId") bookId: String,
        @Query("category") category: String,
        @Query("page") page: Int,
        @Query("count") count: Int
    ): Response<FavouriteResponse>

    @GET("finpro/api/favourite/{bookId}/recents")
    suspend fun getRecentTransactions(
        @Path("bookId") bookId: String,
        @Query("category") category: String? = null,
        @Query("page") page: Int? = null,
        @Query("limit") limit: Int? = null
    ): Response<FavouriteResponse>

    @GET("finpro/api/{account_id}/orders/finpro/{order_id}/poll")
    suspend fun getPpobStatus(
        @Path("account_id") accountId: String,
        @Path("order_id") orderId: String
    ): Response<PpobStatusPollingResponse>

    @GET("finpro/api/payments/v2/methods")
    suspend fun getPaymentMethodsV2(
        @Query("amount") amount: Double,
        @Query("productCategory") productCategory: String,
        @Query("productCode") productCode: String
    ): Response<FinproGetPaymentMethodsV2Response>

    @POST("finpro/api/health")
    suspend fun doHealthCheck(
        @Body paymentHealthCheckRequest: PaymentHealthCheckRequest
    ): Response<PaymentHealthCheckResponse>

    @PUT("finpro/api/{account_id}/orders/{order_id}/checkout")
    suspend fun checkoutOrder(
        @Path("account_id") accountId: String,
        @Path("order_id") orderId: String,
        @Body bankAccount: FinproCheckoutOrderRequest,
        @Header("TWOFA-AUTH-TOKEN") otpToken: String = ""
    ): Response<OrderResponse>

    @POST("finpro/api/favourite/{bookId}/add")
    suspend fun addFavourite(
        @Path("bookId") bookId: String,
        @Body request: FavouriteRequest
    ): Response<AddFavouriteResponse>

    @GET("finpro/api/favourite/{bookId}/recommendations/{orderId}")
    suspend fun getRecommendations(
        @Path("bookId") bookId: String,
        @Path("orderId") orderId: String,
        @Query("search") search: String,
        @Query("page") page: Int,
        @Query("count") count: Int
    ): Response<FavouriteResponse>
}
