package com.bukuwarung.edc.payments.data.datasource

import com.bukuwarung.edc.payments.data.model.PaymentHistory
import com.bukuwarung.edc.payments.data.model.TransactionServiceFeatureFlag
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Path
import retrofit2.http.Query

interface OrderDataSource {
    @GET("transaction-history/api/orders")
    suspend fun getOrders(
        @Query("account_id") accountId: String,
        @Query("customer_id") customerId: String? = null,
        @Query("start_date") startDate: String? = null,
        @Query("end_date") end_date: String? = null,
        @Query("type") type: List<String>? = null,
        @Query("status") status: List<String>? = null,
        @Query("page") page: Int? = null,
        @Query("limit") limit: Int? = null,
        @Query("biller_code") billerCode: String? = null,
        @Query("sort") sorting: String? = null,
        @Query("search-string") searchQuery: String? = null,
        @Query("buku-origin") bukuOrigin: String? = null,
        @Query("serial-number") serialNumberSelected: String? = null
    ): Response<List<PaymentHistory>>

    @GET("transaction-history/api/feature-flags")
    suspend fun getFeatureFlag(): Response<TransactionServiceFeatureFlag>

    @GET("transaction-history/api/linked-orders/{order_id}")
    suspend fun getLinkedOrders(@Path("order_id") orderId: String): Response<List<PaymentHistory>>
}
