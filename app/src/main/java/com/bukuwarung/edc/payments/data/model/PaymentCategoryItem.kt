package com.bukuwarung.edc.payments.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class PaymentCategoryItem(
    @SerializedName("payment_category_id")
    val paymentCategoryId: String,
    @SerializedName("disbursable_type")
    val disbursableType: String,
    @SerializedName("name")
    val name: String,
    @SerializedName("title")
    val title: String,
    @SerializedName("description")
    val description: String,
    @SerializedName("logo_address")
    val logoAddress: String,
    @SerializedName("priority")
    val priority: Int?
) : Parcelable
