package com.bukuwarung.edc.payments.ui.contacts

import android.os.Bundle
import android.text.TextPaint
import android.text.style.ClickableSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.ContactSearchResultsLayoutBinding
import com.bukuwarung.edc.payments.data.model.Contact
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.getColorCompat
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class ContactSearchResultsFragment : Fragment() {

    private val viewModel: ContactSearchViewModel by viewModels({ requireParentFragment() })
    private lateinit var adapter: ContactAdapter
    private var useCase = CustomerSearchUseCase.ACCOUNTING
    private var contactSearchResultsLayoutBinding: ContactSearchResultsLayoutBinding? = null
    private val binding get() = contactSearchResultsLayoutBinding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        contactSearchResultsLayoutBinding =
            ContactSearchResultsLayoutBinding.inflate(layoutInflater, container, false)
        setupView()
        subscribeState()
        return binding.root
    }

    private fun setupView() {
        useCase = CustomerSearchUseCase.FAVORITE
        initContactRecyclerView()
    }

    private fun subscribeState() {
        viewModel.contactsObserver.observe(viewLifecycleOwner) {
            when (it) {
                is ContactSearchViewModel.ContactEvent.DisplayContacts -> {
                    adapter.setRowHolderList(it.contactRowHolder, it.query)
                    showOrHideAddNewFavText(it.contactRowHolder.isEmpty(), it.query)
                }

                is ContactSearchViewModel.ContactEvent.ShowLoaderOnSearchResultsFragment -> {
                    showOrHideSearchResults(it.showLoader)
                }

                is ContactSearchViewModel.ContactEvent.UpdateSelectedCustomer -> {
                    activity?.apply {
                        if (this is OnCustomerSelectedCallback) {
                            this.onCustomerSelected(it.contact, it.contactSource)
                        }
                    }
                }

                else -> {}
            }
        }
    }

    private fun showOrHideAddNewFavText(flag: Boolean, customerName: String) {
        binding.tvAddNewFavContact.visibility = (flag && customerName.isNotBlank()).asVisibility()
        binding.tvAddNewFavContact.text = Utils.makeSectionOfTextBold(
            getString(R.string.save_favourite_subscriber, customerName),
            customerName,
            object : ClickableSpan() {
                override fun onClick(widget: View) {
                    // if click requires
                }

                override fun updateDrawState(ds: TextPaint) {
                    super.updateDrawState(ds)
                    ds.isUnderlineText = false
                    ds.color = requireContext().getColorCompat(R.color.blue_80)
                }
            }
        )
    }

    private fun showOrHideSearchResults(showLoader: Boolean) {
        binding.clSearchResultsFragment.visibility = (!showLoader).asVisibility()
        binding.progressBar.visibility = showLoader.asVisibility()
    }

    private fun initContactRecyclerView() {
        adapter = ContactAdapter(
            arrayListOf(),
            useCase,
            { id, contactSource -> viewModel.onContactSelected(id, contactSource) },
            { viewModel.fetchRecommendations(incrementPageNumber = true) }
        )
        binding.rvContactPicker.adapter = adapter
        val layoutManager = LinearLayoutManager(context)
        binding.rvContactPicker.setHasFixedSize(false)
        binding.rvContactPicker.layoutManager = layoutManager
    }

    companion object {
        private const val USE_CASE = "use_case"
        fun getInstance(
            customerSearchUseCase: CustomerSearchUseCase = CustomerSearchUseCase.ACCOUNTING
        ): ContactSearchResultsFragment = ContactSearchResultsFragment().apply {
            arguments = Bundle().apply {
                putString(USE_CASE, customerSearchUseCase.name)
            }
        }
    }

    interface OnCustomerSelectedCallback {
        fun onCustomerSelected(contact: Contact?, contactSource: String)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        contactSearchResultsLayoutBinding = null
    }
}

enum class CustomerSearchUseCase { FAVORITE, PAYMENT, ACCOUNTING }
