package com.bukuwarung.edc.payments.ui.widgets

import android.content.Context
import android.text.method.LinkMovementMethod
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import com.bukuwarung.bluetooth_printer.utils.hideView
import com.bukuwarung.edc.R
import com.bukuwarung.edc.app.config.AppConfig
import com.bukuwarung.edc.databinding.BankAccountViewBinding
import com.bukuwarung.edc.payments.data.model.BankAccount
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.getColorCompat
import com.bukuwarung.edc.util.getDrawableCompat
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick
import com.bukuwarung.edc.util.textHTML
import com.bukuwarung.ui_component.utils.dp
import com.bukuwarung.ui_component.utils.isNotNullOrEmpty
import com.bumptech.glide.Glide

class BankAccountView(context: Context, attrs: AttributeSet) :
    ConstraintLayout(context, attrs) {

    enum class BankStatus {
        USED_ALREADY,
        BLOCKED,
        MATCHING_FAILED,
        VERIFIED,
        UNSUPPORTED,
        MANUAL_MATCHING_IN_PROGRESS,
        VERIFIED_ACCOUNT_ALREADY_EXIST,
        COMMON
    }

    private val binding: BankAccountViewBinding =
        BankAccountViewBinding.inflate(LayoutInflater.from(context), this, true)
    private val variantConfig = AppConfig.current.variantConfig

    fun setBankView(
        bankAccount: BankAccount,
        bankStatus: BankStatus?,
        errorMessage: String? = null
    ) {
        with(binding) {
            root.showView()
            tvBankError.hideView()
            tvBankInfoText.hideView()
            if (bankAccount.accountHolderName.isNotNullOrEmpty()) {
                tvBankHolderName.text = bankAccount.accountHolderName
            } else {
                tvBankHolderName.text = bankAccount.bankCode
            }
            tvBankAccountNumber.text = context.getString(
                R.string.two_dashed_strings,
                bankAccount.bankCode,
                bankAccount.accountNumber
            )
            Glide.with(context)
                .load(bankAccount.logo)
                .placeholder(R.drawable.ic_bank_placeholder)
                .error(R.drawable.ic_bank_placeholder)
                .into(binding.ivBankLogo)

            when (bankStatus) {
                BankStatus.BLOCKED -> {
                    clBankDetails.background =
                        context.getDrawableCompat(R.drawable.bg_solid_red5_corner_8dp)
                    tvBankError.text = context.getString(R.string.account_blocked_error)
                    tvBankError.setTextColor(context.getColorCompat(R.color.red_60))
                    tvBankError.showView()
                }

                BankStatus.VERIFIED -> {
                    tvBankError.hideView()
                    clBankDetails.background =
                        context.getDrawableCompat(R.drawable.bg_solid_primary_color5_corner_6dp)
                }

                BankStatus.UNSUPPORTED -> {
                    clBankDetails.background =
                        context.getDrawableCompat(R.drawable.bg_solid_red5_corner_8dp)
                    tvBankError.text = bankAccount.message
                    tvBankError.setTextColor(context.getColorCompat(R.color.red_60))
                    tvBankError.showView()
                }

                else -> {}
            }

            // In case some custom error message has to be shown
            if (errorMessage.isNotNullOrEmpty()) {
                tvBankError.text = errorMessage
                tvBankError.showView()
            }
        }
    }

    fun setBankViewForMoneyTransfer(bankAccount: BankAccount) {
        with(binding) {
            root.showView()
            tvBankError.hideView()
            tvBankHolderName.hideView()
            tvBankInfoText.hideView()
            tvBankError.hideView()
            tvBankAccountNumber.visibility =
                bankAccount.accountNumber.isNotNullOrEmpty().asVisibility()
            clBankDetails.background =
                context.getDrawableCompat(R.drawable.bg_solid_primary_color5_corner_6dp)
            tvBankAccountNumber.text = context.getString(
                R.string.two_dashed_strings,
                bankAccount.bankName,
                bankAccount.accountNumber
            )
            Glide.with(context)
                .load(bankAccount.logo)
                .placeholder(R.drawable.ic_bank_placeholder)
                .error(R.drawable.ic_bank_placeholder)
                .into(binding.ivBankLogo)
        }
    }

    fun setBankViewForCardTransaction(bankAccount: BankAccount, errorMessage: String? = null) {
        with(binding) {
            root.showView()
            tvBankError.hideView()
            tvBankInfoText.hideView()
            if (bankAccount.accountHolderName.isNotNullOrEmpty()) {
                tvBankHolderName.showView()
                tvBankHolderName.text = bankAccount.accountHolderName
            } else {
                tvBankHolderName.hideView()
            }
            if (bankAccount.bankName.isNullOrEmpty()) {
                tvBankAccountNumber.text = bankAccount.accountNumber
            } else if (bankAccount.accountNumber.isNotNullOrEmpty()) {
                tvBankAccountNumber.text = context.getString(
                    R.string.two_dashed_strings,
                    bankAccount.bankName,
                    bankAccount.accountNumber
                )
            } else {
                tvBankAccountNumber.text = bankAccount.bankName
            }
            Glide.with(context)
                .load(bankAccount.logo)
                .placeholder(R.drawable.ic_bank_placeholder)
                .error(R.drawable.ic_bank_placeholder)
                .into(binding.ivBankLogo)

            clBankDetails.setBackgroundColor(context.getColorCompat(R.color.blue_5))

            // In case some custom error message has to be shown
            if (errorMessage.isNotNullOrEmpty()) {
                tvBankError.text = errorMessage
                tvBankError.showView()
            }
        }
    }

    fun setBankViewForCashWithdrawal(
        bankAccount: BankAccount?,
        bankStatus: BankStatus?,
        errorMessage: String? = null
    ) {
        with(binding) {
            root.showView()
            tvBankError.hideView()
            tvBankInfoText.hideView()
            if (bankAccount?.accountHolderName.isNotNullOrEmpty()) {
                tvBankHolderName.text =
                    bankAccount?.bankCode.plus("-").plus(bankAccount?.accountHolderName)
            } else {
                tvBankHolderName.text = bankAccount?.bankCode
            }
            tvBankAccountNumber.text = Utils.maskSensitiveInfo(bankAccount?.accountNumber.orEmpty())
            Glide.with(context)
                .load(bankAccount?.logo)
                .placeholder(R.drawable.ic_bank_placeholder)
                .error(R.drawable.ic_bank_placeholder)
                .into(binding.ivBankLogo)

            when (bankStatus) {
                BankStatus.BLOCKED -> {
                    tvBankChange.showView()
                    tvBankInfo.showView()
                    ivVerifiedIcon.showView()
                    tvBankChange.text = context.getString(R.string.delete)
                    tvBankInfo.text = context.getString(R.string.locked_account)
                    tvBankInfo.setTextColor(context.getColorCompat(R.color.black_80))
                    ivVerifiedIcon.setImageResource(R.drawable.ic_blocked_icon)
                    clBankAccountLayout.setPadding(12.dp, 12.dp, 12.dp, 12.dp)
                    clBankAccountLayout.setBackgroundColor(context.getColorCompat(R.color.red_5))
                    clBankDetails.setBackgroundColor(context.getColorCompat(R.color.red_5))

                    tvBankError.hideView()
                    tvBankErrorWithBg.showView()
                    val errorText = buildString {
                        append(context.getString(R.string.locked_account_message))
                        if (variantConfig.shouldShowBankAccountEducation) {
                            val link = "https://bukuwarung.com/rekening-penerima-terkunci/"
                            val message = context.getString(R.string.find_out_more)
                            append("<a href = \"$link\">$message</a>.")
                        }
                    }
                    tvBankErrorWithBg.textHTML(errorText)
                    tvBankErrorWithBg.movementMethod = LinkMovementMethod.getInstance()
                    tvBankErrorWithBg.setLinkTextColor(context.getColorCompat(R.color.red_60))
                }

                BankStatus.VERIFIED -> {
                    tvBankChange.showView()
                    tvBankInfo.showView()
                    ivVerifiedIcon.showView()
                    tvBankChange.text = context.getString(R.string.delete)
                    tvBankInfo.text = context.getString(R.string.account_found)
                    tvBankInfo.setTextColor(context.getColorCompat(R.color.colorPrimary))
                    ivVerifiedIcon.setImageResource(R.drawable.ic_white_checked_circle)
                    clBankAccountLayout.setBackgroundColor(
                        context.getColorCompat(getVerifiedBgColor())
                    )
                    tvBankError.hideView()
                    tvBankErrorWithBg.hideView()
                    clBankAccountLayout.setPadding(12.dp, 12.dp, 12.dp, 12.dp)
                    clBankDetails.background =
                        context.getDrawableCompat(
                            R.drawable.bg_solid_white_corner_8dp_stroke_black10
                        )
                }

                BankStatus.UNSUPPORTED -> {
                    tvBankChange.showView()
                    tvBankInfo.showView()
                    ivVerifiedIcon.showView()
                    tvBankChange.text = context.getString(R.string.delete)
                    tvBankInfo.text = context.getString(R.string.account_found)
                    tvBankInfo.setTextColor(context.getColorCompat(R.color.colorPrimary))
                    ivVerifiedIcon.setImageResource(R.drawable.ic_white_checked_circle)
                    clBankAccountLayout.setBackgroundColor(context.getColorCompat(R.color.black_10))

                    clBankAccountLayout.setPadding(12.dp, 12.dp, 12.dp, 12.dp)
                    clBankDetails.background =
                        context.getDrawableCompat(
                            R.drawable.bg_solid_white_corner_8dp_stroke_black10
                        )

                    tvBankError.hideView()
                    tvBankErrorWithBg.showView()
                    tvBankErrorWithBg.text = if (bankAccount?.message.isNotNullOrEmpty()) {
                        bankAccount?.message
                    } else {
                        errorMessage
                    }
                }

                else -> {}
            }

            // In case some custom error message has to be shown
            if (errorMessage.isNotNullOrEmpty()) {
                tvBankErrorWithBg.text = errorMessage
                tvBankErrorWithBg.showView()
            }
        }
    }

    fun deleteBankView(callback: () -> Unit) {
        binding.tvBankChange.singleClick {
            binding.root.hideView()
            callback.invoke()
        }
    }

    private fun getVerifiedBgColor(): Int {
        return variantConfig.bankAccountPrimaryColor
    }
}
