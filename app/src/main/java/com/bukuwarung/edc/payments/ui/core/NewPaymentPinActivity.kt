package com.bukuwarung.edc.payments.ui.core

import android.content.Intent
import android.os.Bundle
import android.text.SpannableStringBuilder
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.ActivityNewPaymentPinBinding
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.payments.constant.PaymentConst.CHECKOUT_TOKEN
import com.bukuwarung.edc.payments.constant.PaymentRemoteConfig
import com.bukuwarung.edc.payments.constant.PinType
import com.bukuwarung.edc.ppob.common.view.PaymentDownBottomSheet
import com.bukuwarung.edc.printer.ui.dialog.BukuDialog
import com.bukuwarung.edc.util.DateTimeUtils
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.Utils.sharedPreferences
import com.bukuwarung.edc.util.afterTextChanged
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.boldText
import com.bukuwarung.edc.util.get
import com.bukuwarung.edc.util.getDrawableCompat
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.orDefault
import com.bukuwarung.edc.util.put
import com.bukuwarung.edc.util.setDrawableRightListener
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick
import com.bukuwarung.edc.webview.WebviewActivity
import com.bukuwarung.edc.webview.constant.ClassConstants
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class NewPaymentPinActivity :
    AppCompatActivity(),
    PaymentDownBottomSheet.PaymentDownBsListener {

    private lateinit var binding: ActivityNewPaymentPinBinding
    private val viewModel: NewPaymentPinViewModel by viewModels()

    companion object {
        const val USECASE = "usecase"
        const val OP_TOKEN = "op_token"
        const val OTP_LENGTH = 4
    }

    private var showPassword = false
    private var useCaseEnum = PinType.PIN_CONFIRM
    private var showLastPinChange = true

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        useCaseEnum = PinType.PIN_FORGOT
        val opToken = intent.getStringExtra(OP_TOKEN).orEmpty()
        sharedPreferences.put(OP_TOKEN, opToken)
        viewModel.onEventReceived(PaymentPinIntent.OnCreateView(useCaseEnum, opToken.isNotBlank()))
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityNewPaymentPinBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setUseCaseEnum()
        setToolBar()
        viewModel.onEventReceived(PaymentPinIntent.OnCreateView(useCaseEnum))
        setObservables()
        setKeyboardClickListeners()
        binding.enterOtpLayout.tvResendOtp.singleClick {
            viewModel.onEventReceived(PaymentPinIntent.ChangeOtpChannel)
        }
        binding.createPinLayout.llConfirmPin.ivEye.hideView()
        binding.enterOtpLayout.inputOtp.afterTextChanged {
            if (it.length == OTP_LENGTH) {
                Utils.hideKeyboard(this@NewPaymentPinActivity)
                viewModel.onEventReceived(PaymentPinIntent.OnVerifyOTP(it))
            }
        }
        binding.tvPinInfo.setDrawableRightListener {
            binding.tvPinInfo.hideView()
            showLastPinChange = false
        }
    }

    private fun setUseCaseEnum() {
        useCaseEnum = try {
            PinType.valueOf(intent?.getStringExtra(USECASE).orEmpty())
        } catch (_: Exception) {
            PinType.PIN_CONFIRM
        }
    }

    private fun setObservables() {
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.CREATED) {
                viewModel.viewSharedFlow.collect {
                    binding.apply {
                        if (it.checkChangePinStatus) {
                            when (it.changePinStatus) {
                                PinChangeStatus.PENDING_OTP_VERIFICATION.name -> {
                                    checkIfFixedTerminal {
                                        useCaseEnum = PinType.PIN_FORGOT_ENTER_OTP_STEP
                                        viewModel.onEventReceived(
                                            PaymentPinIntent.OnCreateView(
                                                useCaseEnum
                                            )
                                        )
                                    }
                                }

                                PinChangeStatus.PENDING_PIN_INPUT.name -> {
                                    checkIfFixedTerminal {
                                        useCaseEnum = PinType.PIN_FORGOT_CREATE_PIN_STEP
                                        viewModel.onEventReceived(
                                            PaymentPinIntent.OnCreateView(
                                                useCaseEnum
                                            )
                                        )
                                    }
                                }

                                PinChangeStatus.PENDING_MANUAL_VERIFICATION.name -> {
                                    openForgotPinUrl(false)
                                }

                                PinChangeStatus.REJECTED.name -> {
                                    checkIfFixedTerminal { openForgotPinUrl(false) }
                                }

                                PinChangeStatus.VERIFIED.name -> {
                                    if (sharedPreferences.get("pin_change_verified", false)) {
                                        checkIfFixedTerminal { openForgotPinUrl(true) }
                                    } else {
                                        sharedPreferences.put("pin_change_verified", true)
                                        openForgotPinUrl(false)
                                    }
                                }

                                PinChangeStatus.VOID.name, "" -> {
                                    checkIfFixedTerminal { openForgotPinUrl(true) }
                                }
                            }
                        }
                        setToolBar()
                        // generic ui changes
                        keyboard.root.hideView()
                        createPinLayout.llPin.clPin.background = getDrawableCompat(
                            if (it.createPinViewSelected) {
                                R.drawable.bg_solid_white_corner_8dp_stroke_blue60
                            } else {
                                R.drawable.bg_solid_white_corner_8dp
                            }
                        )
                        createPinLayout.llConfirmPin.clPin.background = getDrawableCompat(
                            if (it.confirmPinViewSelected) {
                                R.drawable.bg_solid_white_corner_8dp_stroke_blue60
                            } else {
                                R.drawable.bg_solid_white_corner_8dp
                            }
                        )
                        confirmPinLayout.tvForgotPin.visibility =
                            it.pinConfirmationError.isNotBlank().asVisibility()
                        pbLoader.visibility = it.showLoader.asVisibility()
                        enterOtpLayout.apply {
                            tvOtpError.text = it.otpError
                            tvOtpError.visibility = it.otpError.isNotBlank().asVisibility()
                        }
                        confirmPinLayout.apply {
                            tvPinError.text = it.pinConfirmationError
                            tvPinError.visibility =
                                it.pinConfirmationError.isNotBlank().asVisibility()
                            if (showLastPinChange && useCaseEnum == PinType.PIN_UPDATE &&
                                it.lastPinChangeDate.isNotBlank()
                            ) {
                                val actionDate = DateTimeUtils.getStringFromUtc(
                                    it.lastPinChangeDate,
                                    DateTimeUtils.DD_MMM_YY
                                )
                                binding.tvPinInfo.text =
                                    getString(R.string.pin_changed_on, actionDate)
                                binding.tvPinInfo.showView()
                            } else {
                                binding.tvPinInfo.hideView()
                            }
                        }
                        createPinLayout.apply {
                            llPin.tvError.text = when {
                                it.pinPatternError -> getString(R.string.easy_pin_error)
                                it.pinMatchingOldPin -> getString(R.string.old_pin_error)
                                else -> ""
                            }
                            llPin.tvError.visibility =
                                (it.pinPatternError || it.pinMatchingOldPin).asVisibility()
                            llConfirmPin.tvError.text = it.pinUpdateError
                            llConfirmPin.tvError.visibility =
                                it.pinUpdateError.isNotBlank().asVisibility()
                            if (it.showPinUpdateView2) {
                                llPin.cvPin.singleClick {
                                    viewModel.onEventReceived(PaymentPinIntent.ClickedCreatePinView)
                                }
                                llConfirmPin.cvPin.singleClick {
                                    viewModel.onEventReceived(
                                        PaymentPinIntent.ClickedConfirmPinView
                                    )
                                }
                            } else {
                                llPin.cvPin.singleClick {}
                                llConfirmPin.cvPin.singleClick {}
                            }
                        }
                        if (it.internetError) {
                            showPaymentDownBottomSheet(
                                false,
                                it.errorContent.ifEmpty { getString(R.string.try_again_or_wait) }
                            )
                        } else if (it.serverError) {
                            showPaymentDownBottomSheet(false, it.errorContent)
                        }
                        if (it.pinConfirmationSuccessful) {
                            val message = when (useCaseEnum) {
                                PinType.PIN_CREATE -> getString(R.string.set_saldo_pin_success)
                                PinType.PIN_UPDATE -> getString(R.string.change_saldo_pin_success)
                                else -> ""
                            }
                            if (message.isNotBlank()) {
                                Toast.makeText(
                                    this@NewPaymentPinActivity,
                                    message,
                                    Toast.LENGTH_LONG
                                ).show()
                            }
                            if (useCaseEnum == PinType.PIN_FORGOT ||
                                useCaseEnum == PinType.PIN_FORGOT_CREATE_PIN_STEP ||
                                useCaseEnum == PinType.PIN_FORGOT_ENTER_OTP_STEP
                            ) {
                                sharedPreferences.put("pin_change_verified", false)
                                openForgotPinUrl(false)
                            } else {
                                setResult(
                                    RESULT_OK,
                                    Intent().apply {
                                        putExtra(CHECKOUT_TOKEN, it.checkoutToken)
                                    }
                                )
                            }
                            finish()
                        }
                    }
                    if (it.livelinessExpired) showLivelinessExpiredDialog()
                    // view based segretation of ui
                    showOtpView(it.showOtpView, it.otpTimeInSecs, it.otpChannel)
                    showConfirmPinView(
                        it.showConfirmPinView,
                        it.pinLength.orDefault(6),
                        it.currentPin
                    )
                    showUpdatePinView(
                        it.showPinUpdateView1,
                        it.showPinUpdateView2,
                        it.enablePinUpdateButton,
                        it.createNewPin,
                        it.confirmNewPin
                    )
                }
            }
        }
    }

    private fun showOtpView(showView: Boolean, timeLeft: Int?, otpChannel: String) {
        binding.enterOtpLayout.root.visibility = showView.asVisibility()
        if (showView) {
            binding.enterOtpLayout.apply {
                val newChannel = if (otpChannel == Constant.SMS) Constant.WA else Constant.SMS
                tvResendOtp.text = getString(R.string.otp_change_channel_placeholder, newChannel)
                root.showView()
                subtitle.text = SpannableStringBuilder(
                    getString(
                        R.string.otp_message_placeholder,
                        otpChannel,
                        Utils.getUserId()
                    )
                ).boldText(otpChannel, ignoreCase = true)
                    .boldText(Utils.getUserId(), ignoreCase = true)
                if (timeLeft != null && timeLeft > 0) {
                    tvOtpRetry.text = getString(R.string.otp_counter, timeLeft)
                    tvOtpRetry.setTextColor(
                        ContextCompat.getColorStateList(
                            this@NewPaymentPinActivity,
                            R.color.black_60
                        )
                    )
                    tvOtpRetry.singleClick {}
                    tvResendOtp.hideView()
                } else {
                    tvOtpRetry.text = getString(R.string.otp_didnt_receive)
                    tvOtpRetry.setTextColor(
                        ContextCompat.getColorStateList(
                            this@NewPaymentPinActivity,
                            R.color.colorPrimary
                        )
                    )
                    tvOtpRetry.singleClick {
                        tvOtpError.text = ""
                        viewModel.onEventReceived(PaymentPinIntent.OnRequestOTP(false))
                    }
                    tvResendOtp.showView()
                }
            }
        }
    }

    private fun showUpdatePinView(
        showPinUpdateView1: Boolean,
        showPinUpdateView2: Boolean,
        enablePinUpdateButton: Boolean,
        createPin: String,
        confirmPin: String
    ) {
        binding.createPinLayout.apply {
            root.visibility = showPinUpdateView1.asVisibility()
            if (showPinUpdateView1) {
                binding.keyboard.root.showView()
                llConfirmPin.root.visibility = showPinUpdateView2.asVisibility()
                btnVerify.visibility = showPinUpdateView2.asVisibility()
                btnVerify.isEnabled = enablePinUpdateButton
                btnVerify.singleClick { viewModel.onEventReceived(PaymentPinIntent.ConfirmNewPin) }
                updatePasswordView(showPassword, createPin, confirmPin)
                llPin.apply {
                    tvTitle.text = getString(R.string.create_pin)
                    tvSubtitle.text = getString(R.string.enter_new_pin)
                    ivEye.singleClick {
                        showPassword = !showPassword
                        ivEye.setImageDrawable(
                            getDrawableCompat(
                                if (showPassword) {
                                    R.drawable.ic_eye_open
                                } else {
                                    R.drawable.ic_eye_close
                                }
                            )
                        )
                        updatePasswordView(showPassword, createPin, confirmPin)
                    }
                }
                llConfirmPin.apply {
                    tvTitle.text = getString(R.string.confirm_pin)
                    tvSubtitle.text = getString(R.string.confirm_new_pin, createPin.length)
                }
            }
        }
    }

    private fun showConfirmPinView(
        showConfirmPinView: Boolean,
        pinLength: Int,
        currentPin: String
    ) {
        binding.confirmPinLayout.root.visibility = showConfirmPinView.asVisibility()
        if (showConfirmPinView) {
            binding.keyboard.root.showView()
            binding.confirmPinLayout.apply {
                pinCodeSlot5.visibility = (pinLength == 6).asVisibility()
                pinCodeSlot6.visibility = (pinLength == 6).asVisibility()
                tvTitle.text = getString(
                    if (useCaseEnum == PinType.PIN_UPDATE) {
                        R.string.enter_current_pin
                    } else {
                        R.string.confirm_pin
                    }
                )
                tvSubTitle.text = getString(
                    if (useCaseEnum == PinType.PIN_UPDATE) {
                        R.string.enter_pin_subtitle
                    } else {
                        R.string.confirm_new_pin
                    },
                    pinLength
                )
                tvForgotPin.singleClick {
                    if (useCaseEnum == PinType.PIN_CONFIRM) {
                        viewModel.checkPinChangeRequest()
                    } else {
                        checkIfFixedTerminal { openForgotPinUrl(true) }
                    }
                }
                pinCodeSlot1.background = getDrawableCompat(
                    if (currentPin.isNotEmpty()) {
                        R.drawable.background_pin_filled
                    } else {
                        R.drawable.background_pin_empty
                    }
                )
                pinCodeSlot2.background = getDrawableCompat(
                    if (currentPin.length >= 2) {
                        R.drawable.background_pin_filled
                    } else {
                        R.drawable.background_pin_empty
                    }
                )
                pinCodeSlot3.background = getDrawableCompat(
                    if (currentPin.length >= 3) {
                        R.drawable.background_pin_filled
                    } else {
                        R.drawable.background_pin_empty
                    }
                )
                pinCodeSlot4.background = getDrawableCompat(
                    if (currentPin.length >= 4) {
                        R.drawable.background_pin_filled
                    } else {
                        R.drawable.background_pin_empty
                    }
                )
                pinCodeSlot5.background = getDrawableCompat(
                    if (currentPin.length >= 5) {
                        R.drawable.background_pin_filled
                    } else {
                        R.drawable.background_pin_empty
                    }
                )
                pinCodeSlot6.background = getDrawableCompat(
                    if (currentPin.length >= 6) {
                        R.drawable.background_pin_filled
                    } else {
                        R.drawable.background_pin_empty
                    }
                )
            }
        }
    }

    private fun setToolBar() {
        binding.apply {
            toolbar.navigationIcon =
                ContextCompat.getDrawable(this@NewPaymentPinActivity, R.drawable.vector_arrow_back)
            toolbar.setNavigationOnClickListener { finish() }
            toolbar.title = when (useCaseEnum) {
                PinType.PIN_CREATE -> getString(R.string.set_pin)
                PinType.PIN_CONFIRM -> getString(R.string.confirm_pin)
                else -> getString(R.string.change_pin)
            }
        }
    }

    private fun setKeyboardClickListeners() {
        binding.keyboard.btn0.setOnClickListener {
            viewModel.onEventReceived(PaymentPinIntent.AddDigit(0))
        }
        binding.keyboard.btn1.setOnClickListener {
            viewModel.onEventReceived(PaymentPinIntent.AddDigit(1))
        }
        binding.keyboard.btn2.setOnClickListener {
            viewModel.onEventReceived(PaymentPinIntent.AddDigit(2))
        }
        binding.keyboard.btn3.setOnClickListener {
            viewModel.onEventReceived(PaymentPinIntent.AddDigit(3))
        }
        binding.keyboard.btn4.setOnClickListener {
            viewModel.onEventReceived(PaymentPinIntent.AddDigit(4))
        }
        binding.keyboard.btn5.setOnClickListener {
            viewModel.onEventReceived(PaymentPinIntent.AddDigit(5))
        }
        binding.keyboard.btn6.setOnClickListener {
            viewModel.onEventReceived(PaymentPinIntent.AddDigit(6))
        }
        binding.keyboard.btn7.setOnClickListener {
            viewModel.onEventReceived(PaymentPinIntent.AddDigit(7))
        }
        binding.keyboard.btn8.setOnClickListener {
            viewModel.onEventReceived(PaymentPinIntent.AddDigit(8))
        }
        binding.keyboard.btn9.setOnClickListener {
            viewModel.onEventReceived(PaymentPinIntent.AddDigit(9))
        }
        binding.keyboard.btnDelete.setOnClickListener {
            viewModel.onEventReceived(PaymentPinIntent.DeleteDigit)
        }
    }

    private fun updatePasswordView(showPassword: Boolean, createPin: String, confirmPin: String) {
        if (showPassword) {
            binding.createPinLayout.apply {
                llPin.apply {
                    tvSlot1.background = getDrawableCompat(R.drawable.background_pin_empty)
                    tvSlot2.background = getDrawableCompat(R.drawable.background_pin_empty)
                    tvSlot3.background = getDrawableCompat(R.drawable.background_pin_empty)
                    tvSlot4.background = getDrawableCompat(R.drawable.background_pin_empty)
                    tvSlot5.background = getDrawableCompat(R.drawable.background_pin_empty)
                    tvSlot6.background = getDrawableCompat(R.drawable.background_pin_empty)

                    tvSlot1.text = if (createPin.isNotEmpty()) createPin[0].toString() else ""
                    tvSlot2.text = if (createPin.length >= 2) createPin[1].toString() else ""
                    tvSlot3.text = if (createPin.length >= 3) createPin[2].toString() else ""
                    tvSlot4.text = if (createPin.length >= 4) createPin[3].toString() else ""
                    tvSlot5.text = if (createPin.length >= 5) createPin[4].toString() else ""
                    tvSlot6.text = if (createPin.length >= 6) createPin[5].toString() else ""
                }
                llConfirmPin.apply {
                    tvSlot1.background = getDrawableCompat(R.drawable.background_pin_empty)
                    tvSlot2.background = getDrawableCompat(R.drawable.background_pin_empty)
                    tvSlot3.background = getDrawableCompat(R.drawable.background_pin_empty)
                    tvSlot4.background = getDrawableCompat(R.drawable.background_pin_empty)
                    tvSlot5.background = getDrawableCompat(R.drawable.background_pin_empty)
                    tvSlot6.background = getDrawableCompat(R.drawable.background_pin_empty)

                    tvSlot1.text = if (confirmPin.isNotEmpty()) confirmPin[0].toString() else ""
                    tvSlot2.text = if (confirmPin.length >= 2) confirmPin[1].toString() else ""
                    tvSlot3.text = if (confirmPin.length >= 3) confirmPin[2].toString() else ""
                    tvSlot4.text = if (confirmPin.length >= 4) confirmPin[3].toString() else ""
                    tvSlot5.text = if (confirmPin.length >= 5) confirmPin[4].toString() else ""
                    tvSlot6.text = if (confirmPin.length >= 6) confirmPin[5].toString() else ""
                }
            }
        } else {
            binding.createPinLayout.apply {
                llPin.apply {
                    tvSlot1.background = getDrawableCompat(
                        if (createPin.isNotEmpty()) {
                            R.drawable.background_pin_filled
                        } else {
                            R.drawable.background_pin_empty
                        }
                    )
                    tvSlot2.background = getDrawableCompat(
                        if (createPin.length >= 2) {
                            R.drawable.background_pin_filled
                        } else {
                            R.drawable.background_pin_empty
                        }
                    )
                    tvSlot3.background = getDrawableCompat(
                        if (createPin.length >= 3) {
                            R.drawable.background_pin_filled
                        } else {
                            R.drawable.background_pin_empty
                        }
                    )
                    tvSlot4.background = getDrawableCompat(
                        if (createPin.length >= 4) {
                            R.drawable.background_pin_filled
                        } else {
                            R.drawable.background_pin_empty
                        }
                    )
                    tvSlot5.background = getDrawableCompat(
                        if (createPin.length >= 5) {
                            R.drawable.background_pin_filled
                        } else {
                            R.drawable.background_pin_empty
                        }
                    )
                    tvSlot6.background = getDrawableCompat(
                        if (createPin.length >= 6) {
                            R.drawable.background_pin_filled
                        } else {
                            R.drawable.background_pin_empty
                        }
                    )

                    tvSlot1.text = ""
                    tvSlot2.text = ""
                    tvSlot3.text = ""
                    tvSlot4.text = ""
                    tvSlot5.text = ""
                    tvSlot6.text = ""
                }
                llConfirmPin.apply {
                    tvSlot1.background = getDrawableCompat(
                        if (confirmPin.isNotEmpty()) {
                            R.drawable.background_pin_filled
                        } else {
                            R.drawable.background_pin_empty
                        }
                    )
                    tvSlot2.background = getDrawableCompat(
                        if (confirmPin.length >= 2) {
                            R.drawable.background_pin_filled
                        } else {
                            R.drawable.background_pin_empty
                        }
                    )
                    tvSlot3.background = getDrawableCompat(
                        if (confirmPin.length >= 3) {
                            R.drawable.background_pin_filled
                        } else {
                            R.drawable.background_pin_empty
                        }
                    )
                    tvSlot4.background = getDrawableCompat(
                        if (confirmPin.length >= 4) {
                            R.drawable.background_pin_filled
                        } else {
                            R.drawable.background_pin_empty
                        }
                    )
                    tvSlot5.background = getDrawableCompat(
                        if (confirmPin.length >= 5) {
                            R.drawable.background_pin_filled
                        } else {
                            R.drawable.background_pin_empty
                        }
                    )
                    tvSlot6.background = getDrawableCompat(
                        if (confirmPin.length >= 6) {
                            R.drawable.background_pin_filled
                        } else {
                            R.drawable.background_pin_empty
                        }
                    )

                    tvSlot1.text = ""
                    tvSlot2.text = ""
                    tvSlot3.text = ""
                    tvSlot4.text = ""
                    tvSlot5.text = ""
                    tvSlot6.text = ""
                }
            }
        }
    }

    private fun showPaymentDownBottomSheet(isServiceDown: Boolean, message: String? = null) {
        val paymentDownBottomSheet = PaymentDownBottomSheet.createInstance(isServiceDown, message)
        paymentDownBottomSheet.show(supportFragmentManager, PaymentDownBottomSheet.TAG)
    }

    override fun onButtonClicked() {
        finish()
    }

    private fun showLivelinessExpiredDialog() {
        var dialog: BukuDialog? = null
        dialog = BukuDialog(
            context = this,
            title = getString(R.string.pin_change_session_expired),
            subTitle = getString(R.string.reverify),
            image = R.drawable.ic_transaction_failed,
            isLoader = false,
            btnRightListener = {
                dialog?.dismiss()
                finish()
                checkIfFixedTerminal { openForgotPinUrl(true) }
            },
            btnRightText = getString(R.string.back),
            btnLeftListener = {},
            btnLeftText = ""
        )
        Utils.showDialogIfActivityAlive(this, dialog)
    }

    private fun openForgotPinUrl(isInitialScreen: Boolean) {
        openActivity(WebviewActivity::class.java) {
            var url = PaymentRemoteConfig.getPaymentConfigs().forgotPinUrl + "?landing=BUKUAGEN"
            if (isInitialScreen) url = url.plus("&entryPoint=initial_screen")
            putString(ClassConstants.WEBVIEW_URL, url)
            putBoolean(ClassConstants.HIDE_TOOLBAR, true)
        }
    }

    private fun checkIfFixedTerminal(action: () -> Unit) {
        if (Utils.isFixedTerminal()) {
            openActivity(PaymentPinFixedTerminalActivity::class.java) {
                putString(
                    PaymentPinFixedTerminalActivity.ENTRY_POINT,
                    PaymentPinFixedTerminalActivity.UPDATE_PIN
                )
            }
        } else {
            action()
        }
    }
}
