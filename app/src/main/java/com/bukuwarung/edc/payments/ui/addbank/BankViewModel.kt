package com.bukuwarung.edc.payments.ui.addbank

import Resource
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.edc.card.transfermoney.usecase.EdcBankUseCase
import com.bukuwarung.edc.payments.data.model.Bank
import com.bukuwarung.edc.payments.data.model.BankAccount
import com.bukuwarung.edc.payments.data.model.request.BankValidationRequest
import com.bukuwarung.edc.payments.usecase.PaymentUseCase
import com.bukuwarung.edc.settings.ui.setting.MiniatmDebugSettingActivity
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.errorMessage
import com.bukuwarung.network.interceptors.NoConnectivityException
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@HiltViewModel
class BankViewModel @Inject constructor(
    private val paymentUseCase: PaymentUseCase,
    private val edcBankUseCase: EdcBankUseCase
) : ViewModel() {

    private val _banks = MutableLiveData<Resource<List<Bank>>>()

    val banks: LiveData<Resource<List<Bank>>>
        get() = _banks

    private val _accountVerification = MutableLiveData<Resource<BankAccount>>()

    val accountVerification: LiveData<Resource<BankAccount>>
        get() = _accountVerification

    fun getBanks() = viewModelScope.launch(Dispatchers.IO) {
        _banks.postValue(Resource.loading(null))
        try {
            paymentUseCase.getBanks().let {
                if (it.isSuccessful) {
                    _banks.postValue(Resource.success(it.body()))
                } else {
                    _banks.postValue(Resource.error(it.errorMessage(), null))
                }
            }
        } catch (e: Exception) {
            _banks.postValue(Resource.error(e.message.toString(), null))
        }
    }

    fun getEdcBanks(accountId: String, useCase: String, statuses: String) =
        viewModelScope.launch(Dispatchers.IO) {
            _banks.postValue(Resource.loading(null))
            try {
                edcBankUseCase.getEdcBankList(accountId, useCase, statuses)?.let {
                    if (it.isSuccessful) {
                        val bankList = arrayListOf<Bank>()
                        it?.body()?.banks?.forEach {
                            val oldBank = Bank(
                                bankCode = it?.bankCode.orEmpty(),
                                bankName = it?.bankName.orEmpty(),
                                logo = it?.logoUrl,
                                bankSCode = it?.bankSwiftCode,
                                bankId = it?.id,
                                status = it?.status,
                                paymentBankCode = it?.paymentBankCode,
                                metadata = Bank.Metadata(
                                    message = Bank.MessageMetadata(
                                        messageId = it?.metadata?.message?.messageId
                                    )
                                )
                            )
                            // for faster testing of abnormal and reversal flow testing, it can be disabled from debug settings
                            if (Utils.getBooleanConfig(
                                    MiniatmDebugSettingActivity.TEST_FIXED_BANK
                                )
                            ) {
                                if (oldBank.bankCode.contains("126") || oldBank.bankName.contains(
                                        "BRI",
                                        true
                                    )
                                ) {
                                    bankList.add(oldBank)
                                }
                            } else {
                                bankList.add(oldBank)
                            }
                        }
                        _banks.postValue(Resource.success(bankList))
                    } else {
                        _banks.postValue(Resource.error(it.errorMessage(), null))
                    }
                }
            } catch (e: NoConnectivityException) {
                _banks.postValue(Resource.noInternet(null))
            } catch (e: Exception) {
                _banks.postValue(Resource.error(e.message.toString(), null))
            }
        }

    fun verifyAccountNumber(selectedBank: Bank, accountNumber: String) =
        viewModelScope.launch(Dispatchers.IO) {
            _accountVerification.postValue(Resource.loading(null))
            try {
                paymentUseCase.validateBankAccount(
                    Utils.getPaymentAccountId(),
                    BankValidationRequest(selectedBank.bankCode, accountNumber)
                ).let {
                    when (it.isSuccessful) {
                        true -> {
                            _accountVerification.postValue(Resource.success(it.body()))
                        }

                        false -> {
                            _accountVerification.postValue(Resource.error(it.errorMessage(), null))
                        }
                    }
                }
            } catch (e: Exception) {
                _accountVerification.postValue(Resource.error(e.message.toString(), null))
            }
        }
}
