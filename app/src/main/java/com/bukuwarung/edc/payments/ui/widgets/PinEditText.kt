package com.bukuwarung.edc.payments.ui.widgets

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.text.Editable
import android.text.TextWatcher
import android.util.AttributeSet
import android.util.Log
import android.view.inputmethod.InputMethodManager
import androidx.appcompat.widget.AppCompatEditText
import com.bukuwarung.edc.R

class PinEditText : AppCompatEditText {

    private val circlePaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val filledCirclePaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val circleRadius = 32f
    private val circleSpacing = 32f
    private var currentCircle = 0
    private val totalCircle = 6
    private var pin = ""

    constructor(context: Context) : super(context) {
        init()
    }

    constructor(context: Context, attributeSet: AttributeSet) : super(context, attributeSet) {
        init()
    }

    constructor(context: Context, attributeSet: AttributeSet, defInt: Int) : super(
        context,
        attributeSet,
        defInt
    ) {
        init()
    }

    private lateinit var pinEnterCompletedListener: PinEnterCompletedListener

    fun setPinEnterCompletedListener(listener: PinEnterCompletedListener) {
        pinEnterCompletedListener = listener
    }

    private fun init() {
        addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
                Log.DEBUG
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                if (s != null) {
                    digitEntered(s)
                }
            }

            override fun afterTextChanged(s: Editable?) {
                Log.DEBUG
            }
        })
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        circlePaint.color = resources.getColor(R.color.pin_unfilled_color)
        circlePaint.strokeWidth = 2f
        circlePaint.style = Paint.Style.FILL_AND_STROKE

        filledCirclePaint.color = resources.getColor(R.color.colorPrimary)
        filledCirclePaint.style = Paint.Style.FILL

        val width = width.toFloat()
        val height = height.toFloat()
        val widthTaken =
            (2 * circleRadius) * totalCircle / 2 + circleSpacing * (totalCircle / 2 - 1)
        val startX = width / 2 - widthTaken

        for (i in 0 until totalCircle) {
            val x = startX + i * (circleSpacing + 2 * circleRadius)
            val y = height / 2f
            val color = if (i < currentCircle) filledCirclePaint else circlePaint
            canvas.drawCircle(x, y, circleRadius, color)
        }
    }

    private fun showCustomKeyboard() {
        // Hide the system keyboard
        val imm = context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(windowToken, 0)
    }

    // This method can be called to show the custom keyboard
    fun showCustomKeyboardIfFocused() {
        if (isFocused) {
            showCustomKeyboard()
        }
    }

    fun disableClick() {
        this.isEnabled = false
    }

    fun clearAll() {
        pin = ""
        currentCircle = 0
        invalidate()
    }

    fun back() {
        if (currentCircle > 0) {
            pin = pin.subSequence(0, pin.length - 1).toString()
            currentCircle--
            invalidate()
        }
    }

    fun digitEntered(digit: CharSequence) {
        if (currentCircle < totalCircle) {
            currentCircle++
            pin += digit
            if (pin.length == totalCircle) {
                pinEnterCompletedListener.enterListener(pin)
            }
            invalidate()
        }
    }

    fun pinpadKeyPress(len: Int) {
        currentCircle = len
        pin = "1".repeat(len)
        invalidate()
    }

    fun getCharacterCount(): Int = currentCircle

    fun getPinEntered(): String = pin

    interface PinEnterCompletedListener {
        fun enterListener(pin: String)
    }
}
