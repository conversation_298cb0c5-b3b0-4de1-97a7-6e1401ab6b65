package com.bukuwarung.edc.payments.constant

object PaymentAnalyticsConst {

    const val PAYMENT_AMOUNT_SELECTION_PAGE_VISITED = "payment_amount_selection_page_visited"
    const val PAYMENT_AMOUNT_INPUTTED = "payment_amount_inputted"
    const val PAYMENT_METHOD_SELECTION_PAGE_VISITED = "payment_method_selection_page_visited"
    const val PAYMENT_SEND_PAYMENT_METHOD_SELECTED = "payment_send_payment_method_selected"
    const val PAYMENT_SEND_CREATED = "payment_send_created"
    const val PAYMENT_CREATE_PIN = "payment_create_pin"
    const val PAYMENT_SAVE_PIN = "payment_save_pin"
    const val PAYMENT_EDIT_PIN_CLICKED = "payment_edit_pin_clicked"
    const val PAYMENT_FORGET_PIN_CLICKED = "payment_forget_pin_clicked"
    const val PAYMENT_EDIT_PIN_OTP_INPUTTED = "payment_edit_pin_otp_inputted"
    const val PAYMENT_FORGET_PIN_INPUTTED = "payment_forget_pin_otp_inputted"

    const val WALLET_AMOUNT_SELECTED = "wallet_amount_selected"
    const val WALLET_TOP_UP_SUCCESS = "wallet_top_up_success"
    const val EVENT_PAYMENT_FORGET_PIN_OTP_INPUTTED = "payment_forget_pin_otp_inputted"
    const val EVENT_PAYMENT_EDIT_PIN_OTP = "payment_edit_pin_otp"
    const val EVENT_PAYMENT_CREATE_PIN_OTP = "payment_create_pin_otp"
    const val EVENT_PAYMENT_SAVE_PIN_CONFIRM = "payment_save_pin_confirm"
    const val EVENT_KOMISI_AGEN_HISTORY_VIEW = "komisi_agen_history_view"

    const val START_DATE = "start_date"
    const val END_DATE = "end_date"
    const val SERIAL_NUMBER_SELECTED = "serial_number_selected"
}
