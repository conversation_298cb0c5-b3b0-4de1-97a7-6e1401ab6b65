package com.bukuwarung.edc.payments.ui.widgets

import android.content.Context
import android.graphics.Rect
import android.text.Editable
import android.text.Spannable
import android.text.SpannableString
import android.text.TextWatcher
import android.text.style.ForegroundColorSpan
import android.util.AttributeSet
import androidx.core.content.ContextCompat
import com.bukuwarung.edc.R
import com.bukuwarung.edc.util.orNil
import com.google.android.material.textfield.TextInputEditText
import java.text.DecimalFormat
import java.text.NumberFormat
import java.util.Locale

/**
 * Currency EditText specific for Indonesia
 * IMPROVE this view for easier maintenance and generify it for other currencies
 */
class CurrencyEditText(context: Context, attrs: AttributeSet?) : TextInputEditText(context, attrs) {
    private val locale = Locale("ID", "id")
    private val numberFormat = NumberFormat.getCurrencyInstance(locale)
    private val fractionDecimalFormat = (NumberFormat.getNumberInstance(locale) as DecimalFormat)

    private val currencySymbolPrefix = numberFormat.currency.getSymbol(locale)

    private var textWatcher: CurrencyTextWatcher = CurrencyTextWatcher(this)

    private var onNumberChangedAction: (Long?, String) -> Unit = { number, str -> }

    private var separateCurrencyColor = false
    private var currencyColor: Int = ContextCompat.getColor(context, R.color.black_80)

    init {
        initView(attrs)
    }

    private fun initView(attrs: AttributeSet?) {
        context.theme.obtainStyledAttributes(
            attrs,
            R.styleable.CurrencyEditText,
            0,
            0
        ).apply {
            try {
                separateCurrencyColor =
                    getBoolean(R.styleable.CurrencyEditText_separateCurrencyColor, false)
                if (separateCurrencyColor) {
                    currencyColor = getColor(
                        R.styleable.CurrencyEditText_currencyColor,
                        currencyColor
                    )
                }
            } finally {
                recycle()
            }
        }
    }

    override fun onFocusChanged(focused: Boolean, direction: Int, previouslyFocusedRect: Rect?) {
        super.onFocusChanged(focused, direction, previouslyFocusedRect)
        if (focused) {
            removeTextChangedListener(textWatcher)
            addTextChangedListener(textWatcher)
            if (text.toString().isEmpty()) setText(currencySymbolPrefix)
        } else {
            removeTextChangedListener(textWatcher)
            if (text.toString() == currencySymbolPrefix) setText("")
        }
    }

    fun getDigitsString(): String = text.toString().filter { it.isDigit() }

    fun getNumberValue(): Long {
        val numbersOnly = text.toString().filter { it.isDigit() }
        return if (numbersOnly.isEmpty()) {
            0
        } else {
            fractionDecimalFormat.parse(numbersOnly)!!.toLong()
        }
    }

    /**
     * Returns true if user amount input is empty.
     * Removes extra characters and checks if any digit is entered by the user.
     */
    fun isInputEmpty(): Boolean {
        val numbersOnly = text.toString().filter { it.isDigit() }
        return numbersOnly.isBlank()
    }

    fun setNumberChangedAction(action: (Long?, String) -> Unit) {
        onNumberChangedAction = action
    }

    fun setAmountInEditText(parsedNumber: Long) {
        if (separateCurrencyColor) {
            val spannableString = SpannableString(
                "$currencySymbolPrefix ${fractionDecimalFormat.format(parsedNumber)}"
            )
            val span = ForegroundColorSpan(currencyColor)
            spannableString.setSpan(
                span,
                currencySymbolPrefix.length,
                spannableString.length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            this.setText(spannableString)
        } else {
            this.setText("$currencySymbolPrefix ${fractionDecimalFormat.format(parsedNumber)}")
        }
        this.setSelection(this.length())
    }

    inner class CurrencyTextWatcher(private val editText: TextInputEditText) : TextWatcher {
        var numberValue: Long? = null

        override fun afterTextChanged(s: Editable?) {
            var newValue = s.toString()

            if (newValue.isNotBlank() && newValue != "0" && newValue != "R" && !newValue.contains(
                    currencySymbolPrefix
                )
            ) {
                newValue = currencySymbolPrefix + newValue
            }

            if (newValue.length < currencySymbolPrefix.length) {
                editText.setText(currencySymbolPrefix)
                editText.setSelection(currencySymbolPrefix.length)
                return
            }

            val numbersOnly = newValue.filter { it.isDigit() }

            if (numbersOnly.isEmpty()) {
                numberValue = null
                onNumberChangedAction(numberValue, editText.text.toString())
                return
            }

            editText.removeTextChangedListener(this)
            try {
                val startLength = editText.text?.length
                val selectionStartIndex = editText.selectionStart

                val parsedNumber = fractionDecimalFormat.parse(numbersOnly)!!
                numberValue = parsedNumber.toLong()
                setAmountInEditText(numberValue.orNil)

                val endLength = editText.text?.length!!
                val selection = selectionStartIndex + (endLength - startLength!!)
                if (selection > 0 && selection <= editText.text!!.length) {
                    editText.setSelection(selection)
                } else {
                    editText.setSelection(editText.text!!.length - 1)
                }
                onNumberChangedAction(numberValue, editText.text.toString())
            } catch (e: Exception) {
                e.printStackTrace()
            }
            editText.addTextChangedListener(this)
        }

        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
        }

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
        }
    }
}
