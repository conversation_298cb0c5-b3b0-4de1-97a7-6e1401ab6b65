package com.bukuwarung.edc.payments.data.model

import com.bukuwarung.edc.payments.constant.PaymentConst

data class PaymentFilterDtoX(
    var typeFilters: ArrayList<String> = arrayListOf(),
    var statusFilters: ArrayList<String> = arrayListOf(),
    var dateFilters: DateFilters = DateFilters(),
    var searchQuery: String = "",
    var sorting: String = "",
    var bukuOrigin: String? = null
)

data class DateFilters(
    var presetValue: PaymentConst.DatePreset? = null,
    var startDate: Long? = null,
    var endDate: Long? = null
)

fun getDefaultFilters(type: PaymentConst.HistoryTabs): PaymentFilterDtoX = when (type) {
    PaymentConst.HistoryTabs.ALL -> PaymentFilterDtoX()
    PaymentConst.HistoryTabs.PPOB -> PaymentFilterDtoX(
        arrayListOf(PaymentConst.TYPE_DIGITAL_PRODUCT)
    )

    PaymentConst.HistoryTabs.PEMBAYARAN -> PaymentFilterDtoX(
        arrayListOf(PaymentConst.TYPE_PEMBAYARAN)
    )

    PaymentConst.HistoryTabs.SALDO -> PaymentFilterDtoX(
        arrayListOf(PaymentConst.TYPE_SALDO_ALL)
    )

    PaymentConst.HistoryTabs.SALDOBONUS -> PaymentFilterDtoX(
        arrayListOf(PaymentConst.TYPE_CASHBACK_ALL)
    )
}
