package com.bukuwarung.edc.payments.ui.addbank

import android.os.Bundle
import android.view.MenuItem
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.ActivityAddBankAccountBinding
import com.bukuwarung.edc.global.network.Status
import com.bukuwarung.edc.payments.data.model.Bank
import com.bukuwarung.edc.payments.data.model.BankAccount
import com.bukuwarung.edc.payments.ui.core.PaymentInputActivity
import com.bukuwarung.edc.payments.ui.core.PaymentInputActivity.Companion.BANK_ACCOUNT
import com.bukuwarung.edc.payments.ui.widgets.BankAccountView
import com.bukuwarung.edc.util.afterTextChanged
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.showView
import com.bumptech.glide.Glide
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class AddBankAccountActivity : AppCompatActivity() {

    companion object {
        const val SELECTED_BANK = "selected_bank"
    }

    private lateinit var binding: ActivityAddBankAccountBinding

    private val bankViewModel: BankViewModel by viewModels()

    private val selectedBank by lazy { intent?.getParcelableExtra(SELECTED_BANK) as? Bank }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityAddBankAccountBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        with(binding) {
            btnVerify.setOnClickListener {
                btnVerify.text = ""
                pbVerifyAccount.showView()
                selectedBank?.let {
                    bankViewModel.verifyAccountNumber(
                        it,
                        etAccountNumber.text.toString()
                    )
                }
            }
            etAccountNumber.afterTextChanged {
                tvAccountError.text = ""
                btnVerify.isEnabled = it.isNotBlank()
            }
        }

        showBankDetail()

        bankViewModel.accountVerification.observe(this) {
            when (it.status) {
                Status.SUCCESS -> {
                    binding.pbVerifyAccount.hideView()
                    binding.btnVerify.isEnabled = true
                    binding.btnVerify.text = getString(R.string.verify)
                    handleVerificationResponse(it.data)
                }

                Status.ERROR -> {
                    binding.pbVerifyAccount.hideView()
                    binding.btnVerify.isEnabled = true
                    binding.btnVerify.text = getString(R.string.verify)
                    binding.tvAccountError.text = it.message
                    binding.tvAccountError.showView()
                }

                Status.LOADING -> {
                    binding.btnVerify.isEnabled = false
                    binding.pbVerifyAccount.showView()
                    binding.btnVerify.text = ""
                }

                else -> {}
            }
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> onBackPressedDispatcher.onBackPressed()
        }
        return super.onOptionsItemSelected(item)
    }

    private fun handleVerificationResponse(bankAccount: BankAccount?) {
        bankAccount?.let {
            if (it.isDisabled.isTrue) {
                showAccountDetail(it, BankAccountView.BankStatus.BLOCKED, it.message)
            } else {
                it.logo = selectedBank?.logo
                openActivity(PaymentInputActivity::class.java) {
                    putParcelable(BANK_ACCOUNT, it)
                }
            }
        }
    }

    private fun showBankDetail() {
        selectedBank?.let {
            binding.layoutSelectedBank.apply {
                Glide.with(this@AddBankAccountActivity)
                    .load(it.logo)
                    .placeholder(R.drawable.ic_bank)
                    .error(R.drawable.ic_bank)
                    .into(ivBankLogo)
                tvBankName.text = it.bankName
                tvBankError.hideView()
            }
        }
    }

    private fun showAccountDetail(
        bankAccount: BankAccount,
        statusCode: BankAccountView.BankStatus,
        errorMessage: String?
    ) {
        binding.bankAccountView.apply {
            setBankView(bankAccount, statusCode, errorMessage)
            showView()
        }
    }
}
