package com.bukuwarung.edc.payments.ui.category

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.edc.databinding.ItemPaymentCategoryInfoBinding
import com.bukuwarung.edc.payments.data.model.PaymentCategoryItem
import javax.inject.Inject

class PaymentCategoryInfoAdapter @Inject constructor(private var list: List<PaymentCategoryItem>) :
    RecyclerView.Adapter<PaymentCategoryInfoAdapter.PaymentCategoryInfoViewHolder>() {

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): PaymentCategoryInfoViewHolder {
        val itemBinding = ItemPaymentCategoryInfoBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return PaymentCategoryInfoViewHolder(itemBinding)
    }

    override fun onBindViewHolder(holder: PaymentCategoryInfoViewHolder, position: Int) {
        holder.bind(list[position])
    }

    override fun getItemCount(): Int = list.size

    inner class PaymentCategoryInfoViewHolder(private val binding: ItemPaymentCategoryInfoBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(category: PaymentCategoryItem) {
            binding.tvCategoryName.text = category.name
            binding.tvCategoryDescription.text = category.description
        }
    }
}
