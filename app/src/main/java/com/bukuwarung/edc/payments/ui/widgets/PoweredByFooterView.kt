package com.bukuwarung.edc.payments.ui.widgets

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.PoweredByFooterViewBinding
import com.bukuwarung.edc.payments.constant.PaymentRemoteConfig
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.textHTML

class PoweredByFooterView(context: Context, attrs: AttributeSet) :
    ConstraintLayout(context, attrs) {

    private val binding =
        PoweredByFooterViewBinding.inflate(LayoutInflater.from(context), this, true)

    init {
        val poweredByText = PaymentRemoteConfig.getAppText().poweredByFooter
        if (poweredByText.isNullOrEmpty()) {
            binding.tvPoweredBy.hideView()
        } else {
            binding.tvPoweredBy.apply {
                textHTML(poweredByText)
                setTextColor(ContextCompat.getColor(context, R.color.black_60))
            }
        }
    }
}
