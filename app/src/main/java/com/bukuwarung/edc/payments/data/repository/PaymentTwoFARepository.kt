package com.bukuwarung.edc.payments.data.repository

import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.global.Constant.COUNTRY_CODE
import com.bukuwarung.edc.login.data.model.LoginRequest
import com.bukuwarung.edc.payments.data.datasource.PaymentTwoFARemoteDataSource
import com.bukuwarung.edc.payments.data.model.request.OtpVerifyRequest
import com.bukuwarung.edc.payments.data.model.request.PinForgetRequest
import com.bukuwarung.edc.payments.data.model.request.PinSetupRequest
import com.bukuwarung.edc.payments.data.model.request.PinUpdateRequest
import com.bukuwarung.edc.payments.data.model.request.PinVerifyRequest
import com.bukuwarung.edc.payments.data.model.response.OtpResponse
import com.bukuwarung.edc.payments.data.model.response.PinVerifyResponse
import com.bukuwarung.edc.settings.data.model.PinChangeResponse
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.get
import javax.inject.Inject
import retrofit2.Response

class PaymentTwoFARepository @Inject constructor(
    private val remoteDataSource: PaymentTwoFARemoteDataSource
) {

    private fun getDeviceId() = Constant.DEVICE_ID

    suspend fun requestOTPForPIN(otpChannel: String, authAction: String): Response<OtpResponse> {
        val pinOTPRequest = LoginRequest(
            action = authAction,
            countryCode = COUNTRY_CODE,
            method = otpChannel,
            deviceId = getDeviceId(),
            clientId = Constant.CLIENT_ID,
            clientSecret = Constant.CLIENT_SECRET
        )
        return remoteDataSource.requestOTPForPIN(pinOTPRequest, Utils.getUserId())
    }

    suspend fun verifyOTP(otp: String, authAction: String): Response<OtpResponse> {
        val otpVerificationRequest = OtpVerifyRequest().apply {
            action = authAction
            countryCode = COUNTRY_CODE
            this.otp = otp
            phone = Utils.getUserId()
            userId = Utils.getUserId()
            deviceId = getDeviceId()
            clientId = Constant.CLIENT_ID
            clientSecret = Constant.CLIENT_SECRET
        }
        return remoteDataSource.verifyOTP(
            Utils.sharedPreferences.get("op_token", ""),
            otpVerificationRequest
        )
    }

    suspend fun verifyPIN(pin: String): Response<PinVerifyResponse> {
        val pinVerificationRequest = PinVerifyRequest().apply {
            this.pin = pin
            deviceId = getDeviceId()
        }
        return remoteDataSource.verifyPin(
            pinVerificationRequest
        )
    }

    suspend fun checkPinChangeRequest(): Response<PinChangeResponse> =
        remoteDataSource.checkPinChangeRequest(Utils.getUserId())

    suspend fun checkPinLength() = remoteDataSource.checkPinLength(Utils.getUserId())

    suspend fun createPinV3(pinSetupRequest: PinSetupRequest) =
        remoteDataSource.createPinV3(Utils.sharedPreferences.get("op_token", ""), pinSetupRequest)

    suspend fun forgotPinV3(pinForgotRequest: PinForgetRequest) =
        remoteDataSource.forgotPinV3(Utils.sharedPreferences.get("op_token", ""), pinForgotRequest)

    suspend fun updatePinV3(pinUpdateRequest: PinUpdateRequest) =
        remoteDataSource.updatePinV3(Utils.sharedPreferences.get("op_token", ""), pinUpdateRequest)
}
