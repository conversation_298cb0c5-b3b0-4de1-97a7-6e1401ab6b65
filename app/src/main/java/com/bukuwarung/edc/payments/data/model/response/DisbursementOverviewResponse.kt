package com.bukuwarung.edc.payments.data.model.response

import com.bukuwarung.edc.payments.data.model.DestinationBankInformation
import com.bukuwarung.edc.payments.data.model.LoyaltyDiscount
import com.bukuwarung.edc.payments.data.model.VirtualAccount
import com.google.gson.annotations.SerializedName
import java.math.BigDecimal

data class DisbursementOverviewResponse(
    @SerializedName("received_amount")
    val amount: BigDecimal?,
    @SerializedName("fee")
    val fee: Double?,
    @SerializedName("discount_fee")
    val discountFee: Double?,
    @SerializedName("discount_fee_text")
    val discountFeeText: String? = null,
    @SerializedName("total_transfer")
    val totalTransfer: BigDecimal?,
    @SerializedName("virtual_account")
    val virtualAccount: VirtualAccount?,
    @SerializedName("remaining_free_quota")
    val remainingFreeQuota: Int?,
    @SerializedName("saldo_cashback_amount")
    val saldoCashbackAmount: Double?,
    @SerializedName("destination_bank_information")
    val destinationBankInformation: DestinationBankInformation? = null,
    @SerializedName("loyalty")
    val loyaltyDiscount: LoyaltyDiscount? = null
)
