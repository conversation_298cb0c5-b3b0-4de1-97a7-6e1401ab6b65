package com.bukuwarung.edc.payments.ui.history.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.edc.databinding.ItemDeviceFilterBinding
import com.bukuwarung.edc.payments.data.model.DeviceDetails
import com.bukuwarung.edc.util.singleClick
import javax.inject.Inject

class DeviceFilterAdapter @Inject constructor(
    private var selectedDevice: DeviceDetails?,
    private val deviceFilters: List<DeviceDetails>,
    private val callback: Callback
) : RecyclerView.Adapter<DeviceFilterAdapter.DateViewHolder>() {

    interface Callback {
        fun onDeviceFilterSelected(deviceFilter: DeviceDetails)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DateViewHolder {
        val itemBinding =
            ItemDeviceFilterBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return DateViewHolder(itemBinding)
    }

    override fun onBindViewHolder(holder: DateViewHolder, position: Int) {
        holder.bind(deviceFilters[position])
    }

    override fun getItemCount() = deviceFilters.size

    private fun checkDeviceFilter(selectedDevice: DeviceDetails?) {
        this.selectedDevice = selectedDevice
        notifyDataSetChanged()
    }

    inner class DateViewHolder(val binding: ItemDeviceFilterBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(deviceFilter: DeviceDetails) {
            with(binding) {
                tvTitle.text = deviceFilter.deviceName
                val isChecked = deviceFilter.deviceName.equals(selectedDevice?.deviceName, true)
                rbDevice.isChecked = isChecked
                clRadioButton.singleClick {
                    if (isChecked) return@singleClick
                    checkDeviceFilter(deviceFilter)
                    callback.onDeviceFilterSelected(deviceFilter)
                }
            }
        }
    }
}
