package com.bukuwarung.edc.payments.ui.history.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.edc.databinding.ItemDateHeaderBinding
import com.bukuwarung.edc.databinding.ItemListPaymentHistoryBinding
import com.bukuwarung.edc.payments.data.model.LinkedOrdersData
import com.bukuwarung.edc.payments.data.model.OrderHistoryData
import com.bukuwarung.edc.payments.util.OrderListCallback
import com.bukuwarung.edc.util.Utils
import javax.inject.Inject

class OrderHistoryPagingDataAdapter @Inject constructor(
    private val callback: OrderListCallback,
    private val linkedOrdersMap: HashMap<String, LinkedOrdersData>
) : PagingDataAdapter<OrderHistoryData, RecyclerView.ViewHolder>(DIFF_CALLBACK) {

    companion object {
        private const val VIEW_TYPE_ORDER = 0
        private const val VIEW_TYPE_HEADER = 1

        private val DIFF_CALLBACK = object : DiffUtil.ItemCallback<OrderHistoryData>() {
            override fun areItemsTheSame(
                oldItem: OrderHistoryData,
                newItem: OrderHistoryData
            ): Boolean {
                Utils.safeLet(oldItem.orderData, newItem.orderData) { oldOrder, newOrder ->
                    return oldOrder.orderId == newOrder.orderId
                }
                return false
            }

            override fun areContentsTheSame(
                oldItem: OrderHistoryData,
                newItem: OrderHistoryData
            ): Boolean {
                Utils.safeLet(oldItem.orderData, newItem.orderData) { oldOrder, newOrder ->
                    return oldOrder == newOrder
                }
                return false
            }
        }
    }

    var searchText: String = ""

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        return if (viewType == VIEW_TYPE_ORDER) {
            OrderHistoryAdapter.PaymentHistoryViewHolder(
                binding = ItemListPaymentHistoryBinding.inflate(inflater, parent, false),
                callback = callback,
                searchText = searchText,
                linkedOrdersMap = linkedOrdersMap
            )
        } else {
            OrderHistoryAdapter.DateHeaderHolder(
                ItemDateHeaderBinding.inflate(inflater, parent, false)
            )
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val item = getItem(position)
        if (holder is OrderHistoryAdapter.PaymentHistoryViewHolder) {
            item?.orderData?.let { holder.bind(it) }
        } else if (holder is OrderHistoryAdapter.DateHeaderHolder) {
            holder.bind(item)
        }
    }

    override fun getItemViewType(position: Int): Int =
        if (getItem(position)?.orderData != null) VIEW_TYPE_ORDER else VIEW_TYPE_HEADER

    fun isHeaderItem(position: Int): Boolean = getItem(position)?.formattedDate != null
}
