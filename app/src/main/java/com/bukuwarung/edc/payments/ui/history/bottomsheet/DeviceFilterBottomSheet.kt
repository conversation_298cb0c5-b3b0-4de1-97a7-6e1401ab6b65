package com.bukuwarung.edc.payments.ui.history.bottomsheet

import android.content.res.Resources
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.edc.databinding.BottomsheetDeviceFilterBinding
import com.bukuwarung.edc.global.base.BaseBottomSheetDialogFragment
import com.bukuwarung.edc.payments.data.model.DeviceDetails
import com.bukuwarung.edc.payments.ui.history.adapter.DeviceFilterAdapter
import com.bukuwarung.edc.util.getClassTag
import com.bukuwarung.edc.util.singleClick
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog

class DeviceFilterBottomSheet(
    private val deviceList: List<DeviceDetails>?,
    private val selectedDevice: DeviceDetails?,
    private val action: (DeviceDetails?) -> Unit
) : BaseBottomSheetDialogFragment(),
    DeviceFilterAdapter.Callback {

    var newSelectedDevice: DeviceDetails? = selectedDevice

    companion object {
        fun createInstance(
            fr: FragmentManager,
            deviceList: List<DeviceDetails>?,
            selectedDevice: DeviceDetails?,
            action: (DeviceDetails?) -> Unit
        ) = DeviceFilterBottomSheet(deviceList, selectedDevice, action).show(fr, getClassTag())
    }

    private var _binding: BottomsheetDeviceFilterBinding? = null
    private val binding get() = _binding!!

    override fun onDeviceFilterSelected(deviceFilter: DeviceDetails) {
        newSelectedDevice = deviceFilter
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = BottomsheetDeviceFilterBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        dialog?.setOnShowListener {
            val dialog = it as BottomSheetDialog
            val bottomSheet =
                dialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
            bottomSheet?.let { _ ->
                dialog.behavior.state = BottomSheetBehavior.STATE_EXPANDED
                dialog.behavior.peekHeight = Resources.getSystem().displayMetrics.heightPixels - 100
            }
        }

        binding.apply {
            ivClose.singleClick { dismiss() }
            btnConfirmFilter.singleClick {
                action.invoke(newSelectedDevice ?: selectedDevice)
                dismiss()
            }
            rvDeviceFilters.apply {
                layoutManager = LinearLayoutManager(requireContext())
                adapter = DeviceFilterAdapter(
                    selectedDevice,
                    deviceList.orEmpty(),
                    this@DeviceFilterBottomSheet
                )
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
