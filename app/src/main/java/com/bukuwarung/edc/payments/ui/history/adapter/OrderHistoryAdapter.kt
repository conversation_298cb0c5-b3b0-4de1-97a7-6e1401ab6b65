package com.bukuwarung.edc.payments.ui.history.adapter

import android.graphics.Color
import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import android.graphics.drawable.GradientDrawable
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.Filter
import android.widget.Filterable
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.widget.TextViewCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.ItemDateHeaderBinding
import com.bukuwarung.edc.databinding.ItemListPaymentHistoryBinding
import com.bukuwarung.edc.databinding.ItemLoadMoreBinding
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.payments.data.model.LinkedOrdersData
import com.bukuwarung.edc.payments.data.model.OrderHistoryData
import com.bukuwarung.edc.payments.data.model.PaymentHistory
import com.bukuwarung.edc.payments.ui.widgets.AccordionText
import com.bukuwarung.edc.payments.util.OrderListCallback
import com.bukuwarung.edc.payments.util.PaymentAuxilliary
import com.bukuwarung.edc.util.DateTimeUtils
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.getColorCompat
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.isNotNullOrEmpty
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.orNil
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.textHTML
import javax.inject.Inject
import kotlin.math.abs

class OrderHistoryAdapter @Inject constructor(
    private val callback: OrderListCallback,
    private val linkedOrdersMap: HashMap<String, LinkedOrdersData>
) : RecyclerView.Adapter<RecyclerView.ViewHolder>(),
    Filterable {

    companion object {
        enum class ViewType(val value: Int) {
            ORDER(value = 0),
            DATE_HEADER(value = 1),
            SEE_ALL_ORDERS(value = 2)
        }
    }

    private var searchText = ""
    var filteredOrders = arrayListOf<OrderHistoryData>()
    private var allOrders = arrayListOf<OrderHistoryData>()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        return when (viewType) {
            ViewType.ORDER.value -> {
                PaymentHistoryViewHolder(
                    binding = ItemListPaymentHistoryBinding.inflate(inflater, parent, false),
                    callback = callback,
                    searchText = searchText,
                    linkedOrdersMap = linkedOrdersMap
                )
            }

            ViewType.DATE_HEADER.value -> {
                DateHeaderHolder(ItemDateHeaderBinding.inflate(inflater, parent, false))
            }

            ViewType.SEE_ALL_ORDERS.value -> {
                LoadMoreViewHolder(ItemLoadMoreBinding.inflate(inflater, parent, false))
            }

            else -> throw UnsupportedOperationException(
                "OrderHistoryAdapter: Unsupported ViewType: $viewType"
            )
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is PaymentHistoryViewHolder -> holder.bind(filteredOrders[position].orderData!!)
            is DateHeaderHolder -> holder.bind(filteredOrders[position])
            is LoadMoreViewHolder -> holder.bind()
        }
    }

    override fun getItemCount() = filteredOrders.size

    fun setData(list: ArrayList<OrderHistoryData>) {
        this.filteredOrders = list
        this.allOrders = list
        notifyDataSetChanged()
    }

    override fun getItemViewType(position: Int): Int = filteredOrders[position].viewType.value

    class PaymentHistoryViewHolder(
        private val binding: ItemListPaymentHistoryBinding,
        private val callback: OrderListCallback,
        private val searchText: String?,
        private val linkedOrdersMap: HashMap<String, LinkedOrdersData>
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(item: PaymentHistory) {
            binding.apply {
                vwClickListener.setOnClickListener { callback.openOrderDetail(item) }
                updateTextWithQuery(tvOrderTitle, item.displayName, searchText)

                tvDate.text = item.formattedDate()
                val statusData = PaymentAuxilliary.getPaymentStatusLabelData(
                    item.type,
                    item.status,
                    binding.tvStatus.context
                )

                if (statusData != null) {
                    (statusData[PaymentAuxilliary.TEXT_MESSAGE] as String?)?.let {
                        binding.tvStatus.text = it
                    }
                    (statusData[PaymentAuxilliary.ICON_RES] as Int?)?.let {
                        ivOrderIcon.setImageResource(
                            it
                        )
                    }
                    (statusData[PaymentAuxilliary.TEXT_STYLE] as Int?)?.let {
                        TextViewCompat.setTextAppearance(binding.tvStatus, it)
                    }
                } else {
                    binding.tvStatus.text = ""
                    ivOrderIcon.setImageResource(android.R.color.transparent)
                }
                setPaymentAmount(item.type, item.status, item.amount ?: 0.0)

                val edcTag = item.tags?.get(PaymentConst.TAG_BUKU_ORIGIN)
                val tag = item.tags?.get(PaymentConst.TAG_PRICE_LABEL)
                when {
                    edcTag != null -> {
                        tvEdcTag.showView()
                        tvTag.hideView()
                    }

                    tag != null -> {
                        tvEdcTag.hideView()
                        tvTag.showView()
                        tvTag.textHTML(tag.displayText)
                        tvTag.setTextColor(Color.parseColor(tag.textColor.orEmpty()))
                        val bgShape = tvTag.background as? GradientDrawable
                        bgShape?.setColorFilter(
                            PorterDuffColorFilter(
                                Color.parseColor(tag.backgroundColor.orEmpty()),
                                PorterDuff.Mode.SRC_ATOP
                            )
                        )
                    }

                    else -> {
                        tvEdcTag.hideView()
                        tvTag.hideView()
                    }
                }

                val cashbackDetails = item.tags?.get(PaymentConst.TAG_HISTORY_DETAIL)
                cashbackDetails?.let {
                    tvTransactionDesc.showView()
                    tvTransactionDesc.text = it.displayText
                    if (it.textColor.isNotNullOrEmpty()) {
                        tvTransactionDesc.setTextColor(Color.parseColor(it.textColor.orEmpty()))
                    } else {
                        tvTransactionDesc.setTextColor(
                            ContextCompat.getColor(root.context, R.color.blue_90)
                        )
                    }
                } ?: run {
                    tvTransactionDesc.hideView()
                }

                if (item.isLinkedOrder) {
                    clOrderContainer.setBackgroundColor(
                        root.context.getColorCompat(R.color.f8f8f8)
                    )
                } else {
                    clOrderContainer.setBackgroundColor(
                        root.context.getColorCompat(R.color.white)
                    )
                }
                handleLinkedOrders(item, binding, linkedOrdersMap)
            }
        }

        private fun updateTextWithQuery(view: TextView, displayName: String?, searchText: String?) {
            Utils.safeLet(displayName, searchText) { parentText, highLightedText ->
                if (parentText.contains(highLightedText, ignoreCase = true)) {
                    val lowerParent = parentText.lowercase()
                    val lowerHighlighted = highLightedText.lowercase()
                    val startPos: Int = lowerParent.indexOf(lowerHighlighted)
                    val endPos: Int = startPos + lowerHighlighted.length
                    val spanString: Spannable = SpannableString(parentText)
                    spanString.setSpan(
                        ForegroundColorSpan(
                            ContextCompat.getColor(view.context, R.color.colorPrimary)
                        ),
                        startPos,
                        endPos,
                        Spannable.SPAN_EXCLUSIVE_INCLUSIVE
                    )
                    view.text = spanString
                } else {
                    view.text = displayName
                }
            } ?: run {
                view.text = displayName
            }
        }

        private fun setPaymentAmount(type: String?, status: String?, balance: Double) {
            binding.tvAmount.text = Utils.formatAmount(abs(balance))
            val statusData = PaymentAuxilliary.getPaymentAmountData(
                type,
                status
            )
            if (statusData != null) {
                (statusData[PaymentAuxilliary.TEXT_STYLE] as Int?)?.let {
                    TextViewCompat.setTextAppearance(binding.tvAmount, it)
                }
            }
        }

        private fun handleLinkedOrders(
            item: PaymentHistory,
            binding: ItemListPaymentHistoryBinding,
            linkedOrdersMap: HashMap<String, LinkedOrdersData>
        ) {
            val linkedOrdersData = linkedOrdersMap[item.orderId] ?: LinkedOrdersData()
            with(binding) {
                if (item.linkedOrderCount.orNil > 0) {
                    tvLinkedOrdersCount.text =
                        root.context.getString(R.string.x_other_item, item.linkedOrderCount)
                    // Linked orders are visible
                    atLinkedOrders.setView(
                        AccordionText.AccordionTextData(
                            expandedStateLabel = root.context.getString(R.string.label_close),
                            collapsedStateLabel = root.context.getString(R.string.click_details),
                            expandedState = linkedOrdersData.linkedOrdersVisibility,
                            loading = linkedOrdersData.linkedOrdersLoading
                        )
                    )
                    rvLinkedOrders.visibility =
                        linkedOrdersData.linkedOrdersVisibility.asVisibility()

                    atLinkedOrders.setOnClickListener { _ ->
                        when {
                            linkedOrdersData.linkedOrdersLoading -> return@setOnClickListener
                            linkedOrdersData.linkedOrdersVisibility -> {
                                linkedOrdersData.linkedOrdersVisibility = false
                                atLinkedOrders.switchToCollapseView()
                                rvLinkedOrders.hideView()
                            }

                            else -> {
                                linkedOrdersData.linkedOrdersVisibility = true
                                // Fetch data if required
                                if (linkedOrdersData.linkedOrders == null) {
                                    linkedOrdersData.linkedOrdersLoading = true
                                    atLinkedOrders.switchToLoadingView()
                                    item.orderId?.let { it ->
                                        callback.fetchLinkedOrders(
                                            it,
                                            adapterPosition
                                        )
                                    }
                                } else {
                                    // We already have the data
                                    atLinkedOrders.switchToExpandedView()
                                    rvLinkedOrders.showView()
                                }
                            }
                        }
                    }
                    rvLinkedOrders.apply {
                        adapter = OrderHistoryAdapter(callback, linkedOrdersMap).apply {
                            this.filteredOrders = linkedOrdersData.linkedOrders ?: arrayListOf()
                        }
                        layoutManager = LinearLayoutManager(root.context)
                    }
                    grLinkedOrders.showView()
                } else {
                    grLinkedOrders.hideView()
                }
            }
        }
    }

    class DateHeaderHolder(private val binding: ItemDateHeaderBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(orderHistoryData: OrderHistoryData?) {
            if (DateTimeUtils.isToday(
                    orderHistoryData?.timestamp,
                    DateTimeUtils.YYYY_MM_DD_T_HH_MM_SS
                )
            ) {
                binding.tvDateHeader.text = binding.root.context.getString(R.string.today)
            } else {
                binding.tvDateHeader.text = orderHistoryData?.formattedDate
            }
        }
    }

    inner class LoadMoreViewHolder(private val binding: ItemLoadMoreBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind() {
            binding.seeAllTxt.setOnClickListener { callback.seeAllOrders() }
        }
    }

    override fun getFilter(): Filter {
        return object : Filter() {
            override fun performFiltering(constraint: CharSequence?): FilterResults {
                val search = constraint.toString()
                searchText = search

                val filterResults = FilterResults()
                filterResults.values = if (search.isNotEmpty()) {
                    allOrders.filter {
                        it.orderData?.displayName?.lowercase()
                            ?.contains(search.lowercase()).isTrue
                    }
                } else {
                    allOrders
                }
                return filterResults
            }

            override fun publishResults(constraint: CharSequence?, results: FilterResults?) {
                results?.let {
                    filteredOrders = it.values as ArrayList<OrderHistoryData>
                }
                notifyDataSetChanged()
                callback.filterResultCount(if (allOrders.size == itemCount) 0 else itemCount)
            }
        }
    }
}
