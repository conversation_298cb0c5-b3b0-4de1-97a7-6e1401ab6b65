package com.bukuwarung.edc.payments.ui.saldo

import Resource
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.analytic.BtAnalytics.bwLog
import com.bukuwarung.edc.payments.constant.PaymentRemoteConfig
import com.bukuwarung.edc.payments.data.model.ExistingTopupSaldoResponse
import com.bukuwarung.edc.payments.data.model.OrderResponse
import com.bukuwarung.edc.payments.data.model.SaldoAdminFeeResponse
import com.bukuwarung.edc.payments.data.model.SaldoResponse
import com.bukuwarung.edc.payments.data.model.TopupSaldoRequest
import com.bukuwarung.edc.payments.usecase.FinproUseCase
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.errorMessage
import com.bukuwarung.network.utils.ResourceState
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@HiltViewModel
class SaldoViewModel @Inject constructor(private val finproUseCase: FinproUseCase) : ViewModel() {

    private val _saldo = MutableLiveData<ResourceState<SaldoResponse>>()
    val saldo: LiveData<ResourceState<SaldoResponse>>
        get() = _saldo

    private val _existingTopup = MutableLiveData<Resource<List<ExistingTopupSaldoResponse>>>()
    val existingTopup: LiveData<Resource<List<ExistingTopupSaldoResponse>>>
        get() = _existingTopup

    private val _saldoAdminFee = MutableLiveData<ResourceState<SaldoAdminFeeResponse>>()
    val saldoAdminFee: LiveData<ResourceState<SaldoAdminFeeResponse>>
        get() = _saldoAdminFee

    private val _topupRequest = MutableLiveData<Resource<OrderResponse>>()
    val topupRequest: LiveData<Resource<OrderResponse>>
        get() = _topupRequest

    var topupAmount = 0.0

    fun init(fetchAdminFee: Boolean = true) {
        viewModelScope.launch(Dispatchers.IO) {
            checkSaldoBalance()
            if (fetchAdminFee) getAdminFee()
            checkExisting()
        }
    }

    fun onAmountChanged(amount: Double) {
        this.topupAmount = amount
    }

    fun isBelowMinRequired(): Boolean =
        topupAmount < PaymentRemoteConfig.getMinimumTopupSaldoAmount()

    fun isAboveMaxLimits(): Boolean = Utils.safeLet(
        (_saldo.value as? ResourceState.Success)?.data?.limit,
        (_saldo.value as? ResourceState.Success)?.data?.amount
    ) { maxTopUp, currentBalance ->
        topupAmount + currentBalance > maxTopUp
    } ?: false

    private suspend fun getAdminFee() {
        _saldoAdminFee.postValue(ResourceState.Loading())
        delay(1000)
        _saldoAdminFee.postValue(finproUseCase.getSaldoAdminFee())
    }

    fun checkSaldoBalance() {
        viewModelScope.launch {
            _saldo.postValue(ResourceState.Loading())
            _saldo.postValue(finproUseCase.getSaldo())
        }
    }

    private suspend fun checkExisting() {
        _existingTopup.postValue(Resource.loading(null))
        try {
            finproUseCase.getExistingSaldoTopup().let {
                when (it) {
                    is ResourceState.Success -> {
                        _existingTopup.postValue(Resource.success(it.data))
                    }

                    is ResourceState.Failure -> {
                        _existingTopup.postValue(Resource.error(it.message ?: "", null))
                    }

                    is ResourceState.Loading -> {
                    }
                }
            }
        } catch (e: Exception) {
            _existingTopup.postValue(Resource.error(e.message.toString(), null))
            bwLog(e)
        }
    }

    fun createSaldoTopup() = viewModelScope.launch(Dispatchers.IO) {
        val request = TopupSaldoRequest(
            cancelUnpaidTopupId = _existingTopup.value?.data?.firstOrNull()?.id,
            amount = topupAmount,
            accountId = Utils.getPaymentAccountId(),
            paymentMethods = listOf()
        )
        _topupRequest.postValue(Resource.loading(null))
        try {
            finproUseCase.createSaldoTopup(request).let {
                if (it.isSuccessful) {
                    _topupRequest.postValue(Resource.success(it.body()))
                } else {
                    _topupRequest.postValue(Resource.error(it.errorMessage(), null))
                }
            }
        } catch (e: Exception) {
            _topupRequest.postValue(Resource.error(e.message.toString(), null))
        }
    }
}
