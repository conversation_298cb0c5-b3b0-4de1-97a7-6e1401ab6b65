package com.bukuwarung.edc.payments.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class FavouriteDetail(

    @SerializedName("alias")
    var alias: String? = null,

    @SerializedName("details")
    var details: Details? = null,

    @SerializedName("id")
    var id: String? = null,

    @SerializedName("category")
    var category: String? = null
) : Parcelable
