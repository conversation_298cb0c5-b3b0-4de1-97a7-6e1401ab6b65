package com.bukuwarung.edc.card.cashwithdrawal.repository

import com.bukuwarung.edc.card.cashwithdrawal.api.CashWithdrawalApi
import com.bukuwarung.edc.card.cashwithdrawal.model.SettlementBankAccount
import com.bukuwarung.edc.card.cashwithdrawal.model.SettlementBankAccountResponse
import javax.inject.Inject
import retrofit2.Response

class CashWithdrawalRepo @Inject constructor(private val api: CashWithdrawalApi) {

    suspend fun addSettlementBankAccount(
        paymentAccountId: String,
        terminalId: String,
        bankAccount: SettlementBankAccount
    ): Response<SettlementBankAccount> =
        api.addSettlementBankAccount(paymentAccountId, terminalId, bankAccount)

    suspend fun getSettlementBankList(
        terminalId: String,
        isPrimary: Boolean?,
        isCashBankWithdrawal: Boolean?
    ): Response<SettlementBankAccountResponse> =
        api.getSettlementBankList(terminalId, isPrimary, isCashBankWithdrawal)

    suspend fun setPrimarySettlementBankAccount(bankAccountId: String): Response<Unit> =
        api.setPrimarySettlementBankAccount(bankAccountId)

    suspend fun deleteSettlementBankAccount(bankAccountId: String): Response<Unit> =
        api.deleteSettlementBankAccount(bankAccountId)
}
