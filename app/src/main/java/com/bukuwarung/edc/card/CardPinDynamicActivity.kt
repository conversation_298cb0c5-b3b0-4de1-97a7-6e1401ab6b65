package com.bukuwarung.edc.card

import Resource
import android.annotation.SuppressLint
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.RemoteException
import android.util.Log
import android.view.Gravity
import android.view.View
import androidx.activity.OnBackPressedCallback
import androidx.activity.viewModels
import androidx.transition.Slide
import androidx.transition.Transition
import androidx.transition.TransitionManager
import com.bukuwarung.edc.R
import com.bukuwarung.edc.ZohoChat
import com.bukuwarung.edc.ZohoChatEntryPoint
import com.bukuwarung.edc.card.constant.CardAnalyticsConstants
import com.bukuwarung.edc.card.constant.PrintConst
import com.bukuwarung.edc.card.data.model.CardReceiptResponse
import com.bukuwarung.edc.card.data.model.CheckBalanceRequest
import com.bukuwarung.edc.card.data.model.PinCardErrorResponse
import com.bukuwarung.edc.card.domain.model.consts.Constants
import com.bukuwarung.edc.card.domain.model.consts.Constants.EMV_CALLBACK_REQUEST_INPUT_PIN
import com.bukuwarung.edc.card.domain.model.consts.Constants.EMV_CALLBACK_REQUEST_ONLINE
import com.bukuwarung.edc.card.domain.model.consts.Constants.EMV_CALLBACK_TRANSACTION_RESULT
import com.bukuwarung.edc.card.transfermoney.constants.CheckBalancePinConfig.IS_FROM_FIREBASE
import com.bukuwarung.edc.card.transfermoney.model.BankDetails
import com.bukuwarung.edc.card.transfermoney.model.TransferMoneyData
import com.bukuwarung.edc.card.transfermoney.model.TransferMoneyRequestResponseBody
import com.bukuwarung.edc.card.transfermoney.ui.ConfirmTransferActivity
import com.bukuwarung.edc.card.ui.CheckCardViewModel
import com.bukuwarung.edc.card.ui.EdcCardViewModel
import com.bukuwarung.edc.card.ui.ErrorMapping
import com.bukuwarung.edc.card.ui.ErrorMapping.bcaNotSupported
import com.bukuwarung.edc.card.ui.ErrorMapping.invalidMerchant
import com.bukuwarung.edc.card.ui.ErrorMapping.terminalIdOrDeviceSerialBlockedCodes
import com.bukuwarung.edc.card.ui.checkbalance.BalanceInfoActivity
import com.bukuwarung.edc.databinding.ActivityPaymentPinDynamicBinding
import com.bukuwarung.edc.databinding.LayoutOverlayBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.global.base.BaseCardActivity
import com.bukuwarung.edc.global.enums.TransactionType
import com.bukuwarung.edc.global.network.Status
import com.bukuwarung.edc.homepage.ui.home.HomePageActivity
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.payments.ui.addbank.SelectBankActivity.Companion.IS_MONEY_TRANSFER
import com.bukuwarung.edc.payments.ui.dialog.CardErrorDialog
import com.bukuwarung.edc.payments.ui.widgets.PinEditText
import com.bukuwarung.edc.settings.ui.setting.MiniatmDebugSettingActivity
import com.bukuwarung.edc.util.KycUtil
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.get
import com.bukuwarung.edc.util.goToDestination
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.orDefault
import com.bukuwarung.edc.util.put
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick
import com.vfi.smartpos.deviceservice.constdefine.ConstIPBOC
import com.vfi.smartpos.deviceservice.constdefine.ConstPBOCHandler.onConfirmCardInfo.info.KEY_EXPIRED_DATE_String
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class CardPinDynamicActivity : BaseCardActivity() {

    private lateinit var binding: ActivityPaymentPinDynamicBinding
    private lateinit var overlayLayout: LayoutOverlayBinding

    private var isFromFirebase: Boolean = false
    private var firstTimePinInvalidError = true
    private var data: Bundle? = null
    private var isEligibleToSendRequest: Boolean = true
    private var isRedirectedFromTransferPosting = false

    private var isExceededInvalidPinAttempt = false
    private var pinBlock = ""

    private var isErrorScreenShown = false
    private var cardErrorDialog: CardErrorDialog? = null

    private var hasPinad = false

    private val cardViewModel: CheckCardViewModel by viewModels()
    private val isMoneyTransfer by lazy {
        intent?.getBooleanExtra(IS_MONEY_TRANSFER, false) ?: false
    }

    private val bankAndAmountDetails by lazy {
        intent?.getParcelableExtra("BANK_AMOUNT_DETAILS") as? TransferMoneyData
    }
    private val bundle by lazy { intent?.getBundleExtra(DATA) }
    private val accountType by lazy {
        bundle?.getString(ACCOUNT_TYPE).orDefault(PaymentConst.TYPE_SAVINGS)
    }
    private val cardNumber by lazy { bundle?.getString(PAN).orEmpty() }

    private val transactionType by lazy {
        bundle?.getString(Constant.INTENT_KEY_TRANSACTION_TYPE).orEmpty()
    }

    private val cardExpiry by lazy { bundle?.getString(KEY_EXPIRED_DATE_String).orEmpty() }

    private val pinCardError by lazy {
        intent?.getParcelableExtra("PIN_CARD_ERROR") as? PinCardErrorResponse
    }
    private val handler = Handler(Looper.getMainLooper())

    private val cardEntryMode by lazy { bundle?.getInt("CARD_ENTRY_MODE") }
    private val balanceCheckEmv = "BALANCE_CHECK_EMV"

    private val inActiveRunnable = Runnable {
        val map = HashMap<String, String>()
        map[CardAnalyticsConstants.TRANSACTION_TYPE] = if (isMoneyTransfer) {
            CardAnalyticsConstants.TRANSFER
        } else {
            CardAnalyticsConstants.BALANCE_CHECK
        }
        map[CardAnalyticsConstants.PAGE] = CardAnalyticsConstants.CARD_PIN_SCREEN
        Analytics.trackEvent(CardAnalyticsConstants.EVENT_TIMEOUT, map)

        if (cardErrorDialog != null && cardErrorDialog!!.isShowing) {
            cardErrorDialog?.dismiss()
            cardErrorDialog = null
        }
        cardErrorDialog = ErrorMapping.showErrorDialog(
            context = this,
            errorCode = Constants.CARD_TRANSACTION_TIMEOUT
        ) { checkCardAndShowDialog() }
        cardErrorDialog?.show()
        Log.d("Error", "Inactive for 60 sec")
    }

    // TODO: need to delete this if not used
    private val errorNoAction = Runnable {
        isErrorScreenShown = true
        edcCardViewModel.onEventReceived(
            EdcCardViewModel.Event.OnStartCheckCard(
                5,
                true
            )
        )
    }

    companion object {
        // TODO: need to delete this if not used
        const val MAX_PIN_LENGTH = 6

        const val KEYBOARD_ANIMATION_DURATION = 600L
        const val ACCOUNT_TYPE = "account_type"
        const val API_RESPONSE = "api_response"
        const val DATA = "data"
        const val PAN = "PAN"
        const val INACTIVE_TIMER = 80 * 1000L

        // TODO: need to delete this if not used
        const val ERROR_NO_ACTION_TIMER = 5 * 1000L

        const val PIN_INVALID_ERROR_CODE = "55"
        val inValidAttemptErrorCode = arrayListOf("1", "01", "38", "57", "75")
    }

    init {
        isFromFirebase = Utils.sharedPreferences.get(IS_FROM_FIREBASE, false)
        firstTimePinInvalidError = Utils.sharedPreferences.get(Utils.FIRST_TIME_INVALID_PIN, true)
    }

    override fun setViewBinding() {
        binding = ActivityPaymentPinDynamicBinding.inflate(layoutInflater)
        overlayLayout = LayoutOverlayBinding.bind(binding.root)
        setContentView(binding.root)
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun setupView() {
        onBackPressedDispatcher.addCallback(object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                trackBackPressEvent(CardAnalyticsConstants.ANDROID_BACK_BUTTON)
                if (isExceededInvalidPinAttempt) {
                    checkCardAndShowDialog()
                } else {
                    isEnabled = false
                    onBackPressed()
                }
            }
        })

        binding.toolbar.tvTitle.text = getString(R.string.enter_pin_toolbar_title)
        binding.toolbar.btnBack.hideView()

        if (pinCardError?.code?.isNotBlank() == true) {
            isRedirectedFromTransferPosting = true
            firstTimePinInvalidError =
                Utils.sharedPreferences.get(Utils.FIRST_TIME_INVALID_PIN, true)

            hasPinad =
                inValidAttemptErrorCode.contains(pinCardError?.code) ||
                (
                    PIN_INVALID_ERROR_CODE.contains(pinCardError?.code!!) &&
                        !firstTimePinInvalidError
                    )

            stopTimer()
            cardViewModel.updateTransferMoneyInquire(
                Resource.error(
                    pinCardError?.message.orEmpty(),
                    pinCardError
                )
            )
        }
        binding.secureInfoHeading.text = getString(R.string.dont_share_your_pin)
        binding.secureInfoBody.text = getString(R.string.keep_your_pin_secret)

        toggleCustomKeyboard(show = true)

        binding.pin.setPinEnterCompletedListener(object : PinEditText.PinEnterCompletedListener {
            override fun enterListener(pin: String) {
            }
        })

        binding.pin.setOnTouchListener { _, _ ->
            resetTimer()
            binding.tvPinError.hideView()
            binding.pin.showCustomKeyboardIfFocused()
            toggleCustomKeyboard(show = true)
            true
        }
        binding.btnReturnMain.singleClick {
            binding.tvPinError.hideView()
            checkCardAndShowDialog()
        }
    }

    private fun initPinpad() {
        try {
            if (!hasPinad) {
                hasPinad = true
                val pinKeys = binding.dynamicKeyboard.buttonCoordinates
                edcCardViewModel.initPinpad(cardNumber, pinKeys)
            }
        } catch (e: RemoteException) {
            e.printStackTrace()
        }
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        if (hasFocus && !hasPinad) {
            initPinpad()
        }
    }

    private fun afterKeyboardEnteredPressed(pinBlockVal: String) {
        val map = HashMap<String, String>()

        map[CardAnalyticsConstants.TRANSACTION_TYPE] = if (isMoneyTransfer) {
            CardAnalyticsConstants.TRANSFER
        } else {
            CardAnalyticsConstants.BALANCE_CHECK
        }
        Analytics.trackEvent(CardAnalyticsConstants.EVENT_PIN_VERIFICATION_REQUEST, map)
        overlayLayout.progressOverlay.showView()
        resetTimer()

        val emvIntent = Bundle()
        pinBlock = pinBlockVal
        bundle?.putString("PIN_BLOCK", pinBlock)

        if (isMoneyTransfer) {
            emvIntent.putByte(ConstIPBOC.startEMV.intent.KEY_transProcessCode_byte, 0x39.toByte())
            bankAndAmountDetails?.amount?.let {
                emvIntent.putLong(
                    ConstIPBOC.startEMV.intent.KEY_authAmount_long,
                    (it.toString() + "00").toLong()
                )
            }
            startTransferInquiry(pinBlock)
        } else {
            if (Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_DEVICE)) {
                pinBlock = Utils.getDummyPinblock()
                doBalanceCheck()
            } else if (cardEntryMode == Constants.CARD_ENTRY_MODE_MAG) {
                doBalanceCheck()
            } else {
                Log.d("BALANCE_CHECK_EMV", "start emv transaction..")
                emvIntent.putLong(ConstIPBOC.startEMV.intent.KEY_authAmount_long, 0)
                emvIntent.putByte(
                    ConstIPBOC.startEMV.intent.KEY_transProcessCode_byte,
                    0x30.toByte()
                )
                edcCardViewModel.onEventReceived(
                    EdcCardViewModel.Event.OnStartEmvTransaction(emvIntent, 60)
                )
            }
        }
    }

    private fun toggleCustomKeyboard(show: Boolean) {
        val transition: Transition = Slide(Gravity.BOTTOM)
        transition.duration = KEYBOARD_ANIMATION_DURATION
        transition.addTarget(binding.dynamicKeyboard)
        TransitionManager.beginDelayedTransition(binding.root, transition)
        binding.dynamicKeyboard.visibility = if (show) View.VISIBLE else View.GONE
        // TODO: need to delete this if not used
//        if (show) {
//            initPinpad()
//        } else {
//            ServiceHelper.getInstance().pinpad.endPinInputCustomView()
//        }
    }

    private fun observeEmvCallbacks() {
        edcCardViewModel.emvCallbackResult.observe(this) {
            Log.d(balanceCheckEmv, "emvCallbackResult changed $it")

            if (it.cardStatus == Constants.CARD_STATUS_VALID && it.data != null) {
                Log.d(balanceCheckEmv, "EMV_CALLBACK_CONFIRM_CARD $it.data")
                edcCardViewModel.stopCheckCard()
                edcCardViewModel.onEventReceived(EdcCardViewModel.Event.OnCardReadConfirm(true))
                data = it.data
            } else if (it.operation == EMV_CALLBACK_REQUEST_INPUT_PIN && it.inputPin) {
                Log.d(balanceCheckEmv, "EMV_CALLBACK_REQUEST_INPUT_PIN $pinBlock")
                edcCardViewModel.onEventReceived(EdcCardViewModel.Event.OnImportPin(pinBlock))
            } else if (it.operation == EMV_CALLBACK_REQUEST_ONLINE) {
                Log.d(balanceCheckEmv, "EMV_CALLBACK_REQUEST_ONLINE")
                doBalanceCheck()
            } else if (it.operation == EMV_CALLBACK_TRANSACTION_RESULT) {
                // todo need error messages to show correct value
                checkCardAndShowDialog()
            }
        }
    }

    private fun startTransferInquiry(pinBlock: String) {
        Log.d("TRANSFER_INQUIRY", "cardNumber = $cardNumber")

        val posEntryMode = if (cardEntryMode == Constants.CARD_ENTRY_MODE_MAG) {
            Constants.POS_ENTRY_MODE_MAG_CARD
        } else {
            Constants.POS_ENTRY_MODE_IC_CARD
        }
        val track2Data = bundle?.getString("TRACK2")?.let { Utils.getTrack2(it) }
        bundle?.putString("bank_code", bankAndAmountDetails?.bank?.bankCode)

        val transferMoneyRequest = TransferMoneyRequestResponseBody(
            track2Data = track2Data,
            accountType = accountType,
            cardNumber = cardNumber,
            terminalId = Utils.getTerminalId(),
            destDetails = BankDetails(
                accountNumber = bankAndAmountDetails?.accountNumber,
                bankName = bankAndAmountDetails?.bank?.bankName,
                bankCode = bankAndAmountDetails?.bank?.bankCode
            ),
            iccData = "",
            invoiceNumber = "",
            amount = bankAndAmountDetails?.amount?.toString(),
            notes = bankAndAmountDetails?.optionalText,
            posEntryMode = posEntryMode,
            cardExpiry = cardExpiry,
            pinBlock = pinBlock
        )
        cardViewModel.inquireMoneyTransfer(
            TransactionType.valueOf(transactionType),
            transferMoneyRequest
        )
    }

    override fun subscribeState() {
        edcCardViewModel.checkCardResult.observe(this) {
            when (it?.status) {
                Constants.CARD_CHECK_STATUS_TIMEOUT -> {
                    edcCardViewModel.stopCheckCard()
                    goToDestination(HomePageActivity::class.java)
                }

                Constants.CARD_STATUS_VALID -> {
                    if (!isErrorScreenShown) {
                        edcCardViewModel.onEventReceived(
                            EdcCardViewModel.Event.OnStartCheckCard(1, false)
                        )
                        if (it.showErrorDialog) checkCardAndShowDialog()
                    } else {
                        stopNoActionTimer()
                    }
                }
            }
        }

        edcCardViewModel.pinpadResult.observe(this) {
            when (it?.status) {
                Constants.PINPAD_STATUS_INIT -> {
                    binding.dynamicKeyboard.initKeyboard(it.keyMap!!)
                    edcCardViewModel.startPinInput()
                    resetTimer()
                }

                Constants.PINPAD_STATUS_ERROR -> {
                    val errorCode = it.data?.getInt("error_code")
                    edcCardViewModel.stopPinInput()

                    if (errorCode == -4) { // TODO: need to extract magic number
                        binding.tvPinError.showView()
                        binding.btnReturnMain.hideView()
                        binding.tvPinError.text =
                            getString(R.string.pin_entered_is_less_than_six_digit)
                        hasPinad = false
                        initPinpad()
                    } else if (errorCode == -2) { // // TODO: need to extract magic number
                        handlePinTimeout()
                    }
                    stopTimer()
                }

                Constants.PINPAD_STATUS_CANCEL -> {
                    edcCardViewModel.stopPinInput()
                    stopTimer()
                    handleCancelTransaction()
                }

                Constants.PINPAD_STATUS_CONFIRM -> {
                    val pinBlockStr = it.data?.getString("pin_block").toString()
                    Log.d("card_pin", pinBlockStr)
                    pinBlock = pinBlockStr
                    edcCardViewModel.stopPinInput()
                    resetTimer()
                    afterKeyboardEnteredPressed(pinBlockStr)
                }

                Constants.PINPAD_STATUS_INPUT -> {
                    binding.tvPinError.hideView()
                    val len = it.data?.getInt("len")
                    binding.pin.pinpadKeyPress(len ?: 0)
                    resetTimer()
                }
            }
        }

        cardViewModel.cardBalance.observe(this) {
            when (it.status) {
                Status.SUCCESS -> {
                    stopTimer()
                    toggleCustomKeyboard(show = false)
                    overlayLayout.root.hideView()
                    binding.tvPinError.hideView()

                    Utils.sharedPreferences.put(Utils.FIRST_TIME_INVALID_PIN, true)
                    finish()
                    openActivity(BalanceInfoActivity::class.java) {
                        val response = it.data as? CardReceiptResponse
                        putParcelable(API_RESPONSE, response)
                        putBundle(DATA, bundle)
                    }
                }

                Status.ERROR -> {
                    stopTimer()
                    isEligibleToSendRequest = true
                    overlayLayout.progressOverlay.hideView()
                    val pinCardError = it.data as? PinCardErrorResponse
                    handlePinCardError(pinCardError, PrintConst.TRANSACTION_TYPE_CHECK_BALANCE)
                }

                Status.LOADING -> {
                    resetTimer()
                    overlayLayout.progressOverlay.showView()
                    binding.tvPinError.hideView()
                }

                Status.NO_INTERNET -> {
                    stopTimer()
                    overlayLayout.progressOverlay.hideView()
                    binding.tvPinError.hideView()
                }
            }
        }
        cardViewModel.transferMoneyInquire.observe(this) {
            when (it.status) {
                Status.SUCCESS -> {
                    stopTimer()
                    toggleCustomKeyboard(show = false)
                    overlayLayout.progressOverlay.hideView()
                    binding.tvPinError.hideView()
                    val data = it.data as? TransferMoneyRequestResponseBody
                    finish()
                    openActivity(ConfirmTransferActivity::class.java) {
                        data?.notes = bankAndAmountDetails?.optionalText.orEmpty()
                        // Need to pass accountType, track2Data, posEntryMode, pinBlock,iccData
                        putParcelable(API_RESPONSE, data)
                        bundle?.putParcelable("BANK_AMOUNT_DETAILS", bankAndAmountDetails)
                        bundle?.putBoolean(IS_MONEY_TRANSFER, isMoneyTransfer)
                        putBundle(DATA, bundle)
                    }
                }

                Status.ERROR -> {
                    stopTimer()
                    isEligibleToSendRequest = true
                    overlayLayout.progressOverlay.hideView()
                    val pinCardError = it.data as? PinCardErrorResponse
                    handlePinCardError(pinCardError, PrintConst.TRANSACTION_TYPE_TRANSFER)
                }

                Status.LOADING -> {
                    resetTimer()
                    overlayLayout.progressOverlay.showView()
                    binding.tvPinError.hideView()
                }

                Status.NO_INTERNET -> {
                    stopTimer()
                    overlayLayout.progressOverlay.hideView()
                    binding.tvPinError.hideView()
                }
            }
        }
        observeEmvCallbacks()
    }

    private fun handlePinCardError(pinCardError: PinCardErrorResponse?, transactionType: Int) {
        binding.pin.clearAll()
        Utils.clearIncompleteTransaction()
        toggleCustomKeyboard(show = true)
        firstTimePinInvalidError = Utils.sharedPreferences.get(Utils.FIRST_TIME_INVALID_PIN, true)
        val errorCode = pinCardError?.code
        stopTimer()
        firstTimePinInvalidError = Utils.sharedPreferences.get(Utils.FIRST_TIME_INVALID_PIN, true)

        when {
            pinCardError?.code?.equals(
                PIN_INVALID_ERROR_CODE
            ).isTrue && firstTimePinInvalidError -> {
                Utils.sharedPreferences.put(Utils.FIRST_TIME_INVALID_PIN, false)
                binding.tvPinError.showView()
                binding.btnReturnMain.hideView()
                binding.tvPinError.text =
                    getString(R.string.entered_pin_incorrect_try_again, " [RC $errorCode]")
                if (!isRedirectedFromTransferPosting) {
                    hasPinad = false
                    initPinpad()
                }
            }

            inValidAttemptErrorCode.contains(pinCardError?.code) -> {
                isExceededInvalidPinAttempt = true
                cardErrorDialog?.dismiss()
                cardErrorDialog = ErrorMapping.showErrorDialog(
                    context = this,
                    errorCode = pinCardError?.code,
                    stan = pinCardError?.stan,
                    transactionType = transactionType
                ) {
                    checkCardAndShowDialog()
                }
                cardErrorDialog?.show()
                binding.pin.disableClick()
                toggleCustomKeyboard(show = false)
            }

            else -> {
                edcCardViewModel.stopCheckCard()
                binding.tvPinError.hideView()
                binding.btnReturnMain.hideView()

                pinCardError?.code?.let { pinCardErrorCode ->
                    isErrorScreenShown = true
                    startNoActionErrorTimer()
                    if (cardErrorDialog != null && cardErrorDialog!!.isShowing) {
                        cardErrorDialog?.dismiss()
                        cardErrorDialog = null
                    }
                    cardErrorDialog = ErrorMapping.showErrorDialog(
                        context = this,
                        errorCode = pinCardErrorCode,
                        transactionType = transactionType,
                        stan = pinCardError.stan,
                        positiveListener = {
                            checkCardAndShowDialog()
                            if (terminalIdOrDeviceSerialBlockedCodes.contains(pinCardErrorCode)) {
                                goToDestination(HomePageActivity::class.java)
                                ZohoChat.openZohoChat(
                                    ZohoChatEntryPoint.TERMINAL_ID_OR_DEVICE_SERIAL_BLOCKED
                                )
                            }
                        },
                        dismissListener = {
                            checkCardAndShowDialog()
                            if (invalidMerchant.contains(pinCardErrorCode)) {
                                KycUtil.navigateToKycFlow(this)
                            } else if (bcaNotSupported.contains(pinCardErrorCode)) {
                                goToDestination(HomePageActivity::class.java)
                            } else if (terminalIdOrDeviceSerialBlockedCodes.contains(
                                    pinCardErrorCode
                                )
                            ) {
                                goToDestination(HomePageActivity::class.java)
                            }
                        }
                    )
                    cardErrorDialog?.show()
                }
            }
        }
    }

    private fun checkCardAndShowDialog() {
        checkForCardRemove(this)
    }

    override fun onPause() {
        super.onPause()
        stopTimer()
    }

    override fun onResume() {
        super.onResume()
        startTimer()
        // TODO: need to delete this if not used
//        initPinpad()
    }

    private fun startTimer() {
        handler.postDelayed(inActiveRunnable, INACTIVE_TIMER)
    }

    // TODO: need to delete this if not used
    private fun startNoActionErrorTimer() {
//        handler.postDelayed(errorNoAction, ERROR_NO_ACTION_TIMER)
    }

    private fun resetTimer() {
        stopTimer()
        startTimer()
    }

    private fun stopTimer() {
        try {
            handler.removeCallbacks(inActiveRunnable)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    // TODO: need to delete this if not used
    private fun stopNoActionTimer() {
//        handler.removeCallbacks(errorNoAction)
    }

    private fun doBalanceCheck() {
        Log.d(balanceCheckEmv, "EMV_CALLBACK_REQUEST_ONLINE")

        val track2Data = bundle?.getString("TRACK2")?.let { Utils.getTrack2(it) }

        val posEntryMode = if (cardEntryMode == Constants.CARD_ENTRY_MODE_MAG) {
            Constants.POS_ENTRY_MODE_MAG_CARD
        } else {
            Constants.POS_ENTRY_MODE_IC_CARD
        }
        val iccVal = if (cardEntryMode == Constants.CARD_ENTRY_MODE_MAG) {
            ""
        } else {
            edcCardViewModel.getEmvTagDataForTransaction(TransactionType.BALANCE_INQUIRY)
        }
        val checkBalanceRequest = CheckBalanceRequest(
            accountType = accountType,
            cardExpiry = cardExpiry,
            cardNumber = cardNumber,
            terminalId = Utils.getTerminalId(),
            pinBlock = pinBlock,
            iccData = iccVal,
            posEntryMode = posEntryMode,
            track2Data = track2Data
        )
        /*
         * transactions are incomplete unless backend iccData is verified by device
         * keep transaction for reversal in case of device shutdown/damaged iccdata
         */
        Utils.setIncompleteBalanceCheckTransaction(checkBalanceRequest)
        cardViewModel.fetchCardBalance(checkBalanceRequest)
    }

    override fun onCardRemove() {
        goToDestination(HomePageActivity::class.java)
    }

    private fun trackBackPressEvent(buttonType: String) {
        val eventProperties = HashMap<String, String>()
        eventProperties[CardAnalyticsConstants.BUTTON] = buttonType

        eventProperties[CardAnalyticsConstants.TRANSACTION_TYPE] = if (isMoneyTransfer) {
            CardAnalyticsConstants.TRANSFER
        } else {
            CardAnalyticsConstants.BALANCE_CHECK
        }
        eventProperties[CardAnalyticsConstants.PAGE] = CardAnalyticsConstants.CARD_PIN_SCREEN
        Analytics.trackEvent(CardAnalyticsConstants.EVENT_BACK_BUTTON_CLICK, eventProperties)
    }

    private fun handleCancelTransaction() {
        ErrorMapping.showErrorDialog(
            context = this,
            errorCode = Constants.CARD_TRANSACTION_CANCEL
        ) {
            checkCardAndShowDialog()
        }.also {
            it.show()
        }
        Log.d("Error", "transaction cancelled")
    }

    private fun handlePinTimeout() {
        val map = HashMap<String, String>()

        map[CardAnalyticsConstants.TRANSACTION_TYPE] = if (isMoneyTransfer) {
            CardAnalyticsConstants.TRANSFER
        } else {
            CardAnalyticsConstants.BALANCE_CHECK
        }
        map[CardAnalyticsConstants.PAGE] = CardAnalyticsConstants.CARD_PIN_SCREEN
        Analytics.trackEvent(CardAnalyticsConstants.EVENT_TIMEOUT, map)

        if (cardErrorDialog != null && cardErrorDialog!!.isShowing) {
            cardErrorDialog?.dismiss()
            cardErrorDialog = null
        }
        cardErrorDialog = ErrorMapping.showErrorDialog(
            context = this,
            errorCode = Constants.CARD_TRANSACTION_TIMEOUT
        ) {
            stopNoActionTimer()
            checkCardAndShowDialog()
        }
        cardErrorDialog?.show()
        Log.d("Error", "Inactive for 60 sec")
    }
}
