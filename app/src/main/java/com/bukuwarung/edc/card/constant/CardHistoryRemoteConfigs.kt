package com.bukuwarung.edc.card.constant

import com.bukuwarung.edc.payments.data.model.DateFilter
import com.bukuwarung.edc.util.RemoteConfigUtils
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

object CardHistoryRemoteConfigs {

    private const val TRANSACTION_HISTORY_FILTER = "transaction_date_filter"

    private const val TRANSACTION_HISTORY_FILTER_FAILSAFE = """
        {
  "date": [
    {
      "label": "Hari ini",
      "presetValue": "TODAY"
    },
    {
      "label": "7 hari terakhir",
      "presetValue": "LAST_SEVEN_DAYS",
      "isChecked": true
    },
    {
      "label": "Bulan ini",
      "presetValue": "THIS_MONTH"
    },
    {
      "label": "Atur tanggal",
      "presetValue": "CUSTOM_RANGE"
    }
  ]
}
    """

    fun getTransactionHistoryDateFilter(): ArrayList<DateFilter> {
        val type = object : TypeToken<ArrayList<DateFilter>>() {}.type
        return try {
            val json = RemoteConfigUtils.remoteConfig.getString(TRANSACTION_HISTORY_FILTER)
            val dateFilters = Gson().fromJson<ArrayList<DateFilter>>(json, type)
            dateFilters
        } catch (e: Exception) {
            val json = TRANSACTION_HISTORY_FILTER_FAILSAFE
            val dateFilters = Gson().fromJson<ArrayList<DateFilter>>(json, type)
            dateFilters
        }
    }
}
