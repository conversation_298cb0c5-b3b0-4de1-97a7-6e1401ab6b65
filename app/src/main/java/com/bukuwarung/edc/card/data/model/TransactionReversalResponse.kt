package com.bukuwarung.edc.card.data.model

import android.os.Parcelable
import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Keep
@Parcelize
data class TransactionReversalResponse(
    @SerializedName("rrn")
    val rrn: String?,
    @SerializedName("system_trace_audit_number")
    val systemTraceAuditNumber: String?,
    @SerializedName("transaction_date")
    val transactionDate: String?
) : Parcelable
