package com.bukuwarung.edc.card.transfermoney.ui

import android.os.Bundle
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import android.view.View
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.bukuwarung.bluetooth_printer.utils.hideView
import com.bukuwarung.edc.R
import com.bukuwarung.edc.ZohoChat
import com.bukuwarung.edc.app.config.VariantConfig
import com.bukuwarung.edc.card.constant.CardAnalyticsConstants
import com.bukuwarung.edc.card.transfermoney.model.TransferMoneyData
import com.bukuwarung.edc.databinding.ActivityAddAccountMoneyTransferBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.global.enums.TransactionType
import com.bukuwarung.edc.global.enums.toZohoEntryPoint
import com.bukuwarung.edc.payments.data.model.Bank
import com.bukuwarung.edc.payments.data.model.BankAccount
import com.bukuwarung.edc.payments.ui.addbank.AddBankAccountActivity
import com.bukuwarung.edc.payments.ui.addbank.SelectBankActivity.Companion.IS_MONEY_TRANSFER
import com.bukuwarung.edc.settings.ui.setting.MiniatmDebugSettingActivity
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.afterTextChanged
import com.bukuwarung.edc.util.getColorCompat
import com.bukuwarung.edc.util.openActivity
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class AddBankAccountMoneyTransferActivity :
    AppCompatActivity(R.layout.activity_add_account_money_transfer) {

    private lateinit var binding: ActivityAddAccountMoneyTransferBinding

    @Inject
    lateinit var variantConfig: VariantConfig

    private var bundle: Bundle? = null

    private val selectedBank by lazy {
        intent?.getParcelableExtra(AddBankAccountActivity.SELECTED_BANK) as? BankAccount
    }
    private var transactionType = TransactionType.TRANSFER_INQUIRY.type
    private val accountNumberFlow = MutableStateFlow<String>("")
    private val amountFlow = MutableStateFlow("0")
    private val optionalFlow = MutableStateFlow("")

    companion object {
        const val MIN_MONEY_TRANSFER_AMOUNT = 15000L
        const val MAX_MONEY_TRANSFER_AMOUNT = 9999999999L
        const val MAX_DIGIT_ACCOUNT_NUMBER = 28
    }

    private val validateBtn = combine(accountNumberFlow, amountFlow) { accountNumber, amount ->
        val validAccountNumber =
            accountNumber.isNotBlank() and (accountNumber.length <= MAX_DIGIT_ACCOUNT_NUMBER)
        val validAmount = amount.isNotBlank() and (
            amount.filter { it.isDigit() }.toLong().let {
                it in MIN_MONEY_TRANSFER_AMOUNT..MAX_MONEY_TRANSFER_AMOUNT
            }
            )
        return@combine validAmount && validAccountNumber
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityAddAccountMoneyTransferBinding.inflate(layoutInflater)
        setContentView(binding.root)

        onBackPressedDispatcher.addCallback(
            this@AddBankAccountMoneyTransferActivity,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    handleBackPressed(true)
                    finish()
                }
            }
        )
        if (intent.hasExtra("data")) {
            bundle = intent.getBundleExtra("data")
            transactionType = bundle!!.getString(Constant.INTENT_KEY_TRANSACTION_TYPE)!!
        }

        setupToolbar()
        showBankDetail()

        with(binding) {
            etAccountNumber.afterTextChanged {
                if (it.isBlank()) {
                    accountNumberFlow.value = "0"
                } else {
                    accountNumberFlow.value = it
                    performAccountNumberCheck(it)
                }
            }
            // to have faster reversal and abnormal flow testing. It can be disabled from debug settings
            if (Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_FIXED_BANK) &&
                transactionType == TransactionType.TRANSFER_INQUIRY.type
            ) {
                etAccountNumber.setText("*********")
            }
            etAmount.afterTextChanged {
                val amount = etAmount.getNumberValue()
                Log.d("--->amount", amount.toString())
                amountFlow.value = amount.toString()

                etAmount.setSelection(binding.etAmount.length())
                performAmountCheck(amount)
            }

            tietReferenceCode.afterTextChanged {
                optionalFlow.value = it
            }

            etAmount.setOnFocusChangeListener { _, hasFocus ->
                if (hasFocus) {
                    etAmount.setSelection(etAmount.length())
                }
            }
            etAmount.setSelection(etAmount.length())
            etAmount.requestFocus()
        }
        binding.btnNext.setOnClickListener {
            bundle?.putString("notes", optionalFlow.value)

            val moneyTransferData = selectedBank?.let { it1 ->
                TransferMoneyData(
                    bank = Bank(
                        bankCode = it1?.bankCode.orEmpty(),
                        bankName = it1?.bankName.orEmpty(),
                        logo = it1?.logo
                    ),
                    amount = amountFlow.value.toLong(),
                    optionalText = optionalFlow.value,
                    accountNumber = accountNumberFlow.value,
                    accountHolderName = it1.accountHolderName
                )
            }
            openActivity(MoneyTransferConfirmationActivity::class.java) {
                putBundle(
                    "data",
                    bundle
                )
                putParcelable("BANK_AMOUNT_DETAILS", moneyTransferData)
                putBoolean(IS_MONEY_TRANSFER, true)
            }
        }
        lifecycleScope.launch {
            validateBtn.collectLatest {
                binding.btnNext.isEnabled = it
            }
        }
    }

    private fun performAmountCheck(amount: Long) {
        if (amount < MIN_MONEY_TRANSFER_AMOUNT) {
            minAmountUI(
                true,
                getString(
                    R.string.Minimal,
                    Utils.formatAmount(15000.00)
                )
            )
        } else if (amount > MAX_MONEY_TRANSFER_AMOUNT) {
            minAmountUI(
                true,
                getString(
                    R.string.max_transfer_amount_10_digits
                )
            )
        } else {
            minAmountUI(false)
        }
    }

    private fun performAccountNumberCheck(accountNumber: String) {
        if (accountNumber.length > MAX_DIGIT_ACCOUNT_NUMBER) {
            checkAccountNumberUi(true)
        } else {
            checkAccountNumberUi(false)
        }
    }

    private fun minAmountUI(isMinAmount: Boolean, text: String? = null) {
        with(binding) {
            tvAmountLimitError.text = text
            tvAmountLimitError.visibility = if (isMinAmount) View.VISIBLE else View.GONE
            tvAmountLimitError.setTextColor(
                getColorCompat(if (isMinAmount) R.color.red_80 else R.color.black_60)
            )
            etAmount.setTextColor(
                getColorCompat(if (isMinAmount) R.color.red_80 else R.color.black_80)
            )
            vwDivider.setBackgroundColor(
                getColorCompat(if (isMinAmount) R.color.red_80 else R.color.black_10)
            )
        }
    }

    private fun checkAccountNumberUi(isMaxAccount: Boolean) {
        with(binding) {
            tvMaxAccError.text = getString(R.string.max_account_number_is_28_digit)
            tvMaxAccError.visibility = if (isMaxAccount) View.VISIBLE else View.GONE
            etAccountNumber.setTextColor(
                getColorCompat(if (isMaxAccount) R.color.red_80 else R.color.colorPrimary)
            )
            etAccountNumber.setBackgroundDrawable(
                ContextCompat.getDrawable(
                    etAccountNumber.context,
                    if (isMaxAccount) R.drawable.input_errorr else R.drawable.drawable_login_input
                )
            )
        }
    }

    private fun showBankDetail() {
        if (transactionType == TransactionType.CASH_WITHDRAWAL.type) {
            binding.tvAmount.text = getString(R.string.cash_withdrawal_amount)
            binding.etAccountNumber.hideView()
            binding.txtLabelAccountNumber.hideView()
            binding.tvReferenceCode.hideView()
            binding.tiLReferenceCode.hideView()
        }
        selectedBank?.let {
            binding.bankAccountView.setBankViewForCardTransaction(
                bankAccount = it
            )
            if (transactionType == TransactionType.CASH_WITHDRAWAL.type) {
                accountNumberFlow.value = it.accountNumber.toString()
                binding.etAccountNumber.setText(it.accountNumber.toString())
            }
        }
    }

    private fun handleBackPressed(isDeviceBackPressed: Boolean) {
        val eventProperties = HashMap<String, String>()
        eventProperties[CardAnalyticsConstants.BUTTON] =
            if (isDeviceBackPressed) {
                CardAnalyticsConstants.ANDROID_BACK_BUTTON
            } else {
                CardAnalyticsConstants.TOP_ARROW_BACK_BUTTON
            }
        eventProperties[CardAnalyticsConstants.TRANSACTION_TYPE] = CardAnalyticsConstants.TRANSFER
        eventProperties[CardAnalyticsConstants.PAGE] = CardAnalyticsConstants.INPUT_AMOUNT
        Analytics.trackEvent(CardAnalyticsConstants.EVENT_BACK_BUTTON_CLICK, eventProperties)
    }

    private fun setupToolbar() {
        binding.toolbar.apply {
            tvTitle.text = if (transactionType == TransactionType.TRANSFER_INQUIRY.type) {
                getString(R.string.destination_account)
            } else {
                getString(R.string.cash_withdrawal_amount)
            }

            btnBack.setOnClickListener {
                handleBackPressed(false)
                finish()
            }
            setSupportActionBar(widgetToolbar)
            supportActionBar?.setDisplayShowTitleEnabled(false)
        }
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        if (variantConfig.shouldShowAccountEducation) {
            menuInflater.inflate(R.menu.menu_help, menu)
        }
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == R.id.action_help) {
            ZohoChat.openZohoChat(transactionType.toZohoEntryPoint())
            return true
        }
        return super.onOptionsItemSelected(item)
    }
}
