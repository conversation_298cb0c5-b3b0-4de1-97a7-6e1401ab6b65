package com.bukuwarung.edc.card.domain.model

import com.bukuwarung.edc.card.domain.model.base.EMVTLVParam

class EmvApplicationParam : EMVTLVParam() {
    var aidType = 0

    init {
        /**
         * set the default value of some Tags
         * value null means the tag is optional
         */
        defaultTagValue = arrayOf(
            DefaultTagValue(TAG_ASI_DF01, "01"),
            DefaultTagValue(TAG_DF08_OPTIONAL, null),
            DefaultTagValue(TAG_DF26_OPTIONAL, null),
            DefaultTagValue(TAG_DF27_OPTIONAL, null),
            DefaultTagValue(TAG_DF25_OPTIONAL, null),
            DefaultTagValue(TAG_VER_NUM_9F09, null),
            DefaultTagValue(TAG_9F15_OPTIONAL, null),
            DefaultTagValue(TAG_DEFAULT_TDOL_97_OPTIONAL, null),
            DefaultTagValue(TAG_5F36_OPTIONAL, "02"),
            DefaultTagValue(TAG_9F01_OPTIONAL, null)
        )
    }

    override fun clean() {
        super.clean()
        if (defaultTagValue != null) {
            for (tagValue in defaultTagValue) {
                tagValue.available = true
            }
        }
    }

    companion object {
        /**
         * AID TAG list : reference https://verifone.cloud/filebrowser/download/1385
         */

        //  "Transaction Currency Code Indicates the currency code of the transaction according to ISO 4217"
        const val TAG_CURRENCY_CODE_TERM_5F2A = 0x5F2A

        //  "Transaction Currency Exponent Indicates
        //  the implied position of the decimal point from the right of the transaction amount
        //  represented according to ISO 4217"
        const val TAG_5F36_OPTIONAL = 0x5F36

        //  "Transaction Certificate Data Object List (TDOL) List of data objects (tag and length)
        //  to be used by the terminal in generating the TC Hash Value"
        const val TAG_DEFAULT_TDOL_97_OPTIONAL = 0x97

        //  "Acquirer Identifier Uniquely identifies the acquirer within each payment system"
        const val TAG_9F01_OPTIONAL = 0x9F01

        //  "Application Identifier (AID)
        //  terminal Identifies the application as described in ISO/IEC 7816-5"
        const val TAG_AID_9F06 = 0x9F06

        //  "Application Version Number Version number assigned by the payment system for the application"
        const val TAG_VER_NUM_9F09 = 0x9F09

        //  "Merchant Category Code Classifies the type of business being done by the merchant,
        //  represented according to ISO 8583:1993 for Card Acceptor Business Code"
        const val TAG_9F15_OPTIONAL = 0x9F15

        //  "Terminal Country Code Indicates
        //  the country of the terminal, represented according to ISO 3166"
        const val TAG_COUNTRY_CODE_TERM_9F1A = 0x9F1A

        //  "Terminal Floor Limit Indicates
        //  the floor limit in the terminal in conjunction with the AID"
        const val TAG_FLOOR_LIMIT_9F1B = 0x9F1B

        //  Terminal Capabilities Indicates
        //  the card data input, CVM, and security capabilities of the terminal
        const val TAG_APP_TERM_CAP_9F33 = 0x9F33

        //  Terminal Type Indicates the environment of the terminal,
        //  its communications capability, and its operational control
        const val TAG_APP_TERM_TYPE_9F35 = 0x9F35

        //  Additional Terminal Capabilities
        //  Indicates the data input and output capabilities of the terminal
        const val TAG_APP_TERM_ADD_CAP_9F40 = 0x9F40

        //  Application Select Identifier
        //  0-Terminal AID
        //  & CARD_NO AID Partial match 1-Terminal AID
        //  & CARD_NO AID All match
        const val TAG_ASI_DF01 =
            0xDF01
        const val TAG_DF08_OPTIONAL = 0xDF08 //  Terminal Priority
        const val TAG_TAC_DEFAULT_DF11 = 0xDF11 //  TAC - default
        const val TAG_TAC_ONLINE_DF12 = 0xDF12 //  TAC-online
        const val TAG_TAC_DENIAL_DF13 = 0xDF13 //  TAC-refuse

        //  "Default Dynamic Data Authentication Data Object List (DDOL)
        //  DDOL to be used for constructing the INTERNAL AUTHENTICATE command
        //  if the DDOL in the card is not present"
        const val TAG_DEFAULT_DDOL_DF14 = 0xDF14

        //  "Threshold Value for Biased Random Selection Value
        //  used in terminal risk management for random transaction selection"
        const val TAG_THRESHOLD_DF15 = 0xDF15

        //  "Maximum Target Percentage to be used for Biased Random Selection Value
        //  used in terminal risk management for random transaction selection"
        const val TAG_MAX_TARGET_PERCENTAGE_DF16 = 0xDF16

        //  "Target Percentage to be Used for Random Selection Value
        //  used in terminal risk management for random transaction selection"
        const val TAG_TARGET_PERCENTAGE_DF17 = 0xDF17
        const val TAG__DF18 = 0xDF18
        const val TAG_DF25_OPTIONAL = 0xDF25
        const val TAG_DF26_OPTIONAL = 0xDF26 //  international
        const val TAG_DF27_OPTIONAL = 0xDF27
    }
}
