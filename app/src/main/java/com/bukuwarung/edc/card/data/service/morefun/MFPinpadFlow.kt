package com.bukuwarung.edc.card.data.service.morefun

import android.graphics.Color
import android.os.Bundle
import android.util.Log
import com.bukuwarung.edc.card.data.model.PinpadButtonCoordinates
import com.bukuwarung.edc.card.domain.model.PinpadResult
import com.bukuwarung.edc.card.domain.model.consts.Constants
import com.bukuwarung.edc.device.DeviceHelper
import com.bukuwarung.edc.util.Utils
import com.morefun.yapi.ServiceResult
import com.morefun.yapi.device.pinpad.OnPinPadInputListener
import com.morefun.yapi.device.pinpad.PinAlgorithmMode
import com.morefun.yapi.device.pinpad.PinPadConstrants
import javax.inject.Inject
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.callbackFlow

class MFPinpadFlow @Inject constructor(private val edcDeviceService: MFDeviceServiceImpl) {

    fun initPinpad(cardNumber: String, pinpadButtonCoordinates: List<PinpadButtonCoordinates>) =
        callbackFlow {
            Log.d(TAG, "start check card")
            try {
                val padListener: OnPinPadInputListener = object : OnPinPadInputListener.Stub() {
                    override fun onInputResult(ret: Int, pinBlock: ByteArray?, ksn: String?) {
                        Log.d("TAG", "onConfirm")
                        val data = Bundle()
                        data.putString("pin_block", Utils.byte2HexStr(pinBlock))
                        val pinpadResult = PinpadResult(
                            status = Constants.PINPAD_STATUS_CONFIRM,
                            data = data
                        )
                        trySend(pinpadResult)
                    }

                    override fun onSendKey(keyCode: Byte) {
                        if (keyCode == ServiceResult.PinPad_Input_Cancel.toByte()) {
                            Log.d("TAG", "onCancel")
                            val pinpadResult = PinpadResult(
                                status = Constants.PINPAD_STATUS_CANCEL,
                                data = Bundle()
                            )
                            trySend(pinpadResult)
                        }
                    }
                }

                val panBlock = cardNumber.toByteArray()
                val bundle = Bundle()
                bundle.putBoolean(PinPadConstrants.COMMON_NEW_LAYOUT, false)
                bundle.putBoolean(PinPadConstrants.COMMON_SUPPORT_KEYVOICE, true)
                bundle.putBoolean(PinPadConstrants.COMMON_SUPPORT_BYPASS, false)
                bundle.putBoolean(PinPadConstrants.COMMON_IS_RANDOM, false)

                bundle.putIntArray(
                    PinPadConstrants.NUMBER_TEXT_COLOR,
                    intArrayOf(
                        Color.BLACK, Color.BLACK, Color.BLACK,
                        Color.BLACK, Color.BLACK, Color.BLACK,
                        Color.BLACK, Color.BLACK, Color.BLACK, Color.BLACK
                    )
                )

                bundle.putString(PinPadConstrants.TITLE_HEAD_CONTENT, "please input pin:")
                DeviceHelper.getPinpad().inputOnlinePin(
                    bundle,
                    panBlock,
                    PIN_KEY_INDEX,
                    PinAlgorithmMode.ISO9564FMT1,
                    padListener
                )
                val pinpadResult = PinpadResult(
                    status = Constants.PINPAD_STATUS_INIT,
                    data = Bundle()
                )
                // trySend(pinpadResult)
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                awaitClose {
                    Log.d(TAG, "pinpad flow close")
                }
            }
        }

    fun startPinpadPinInput() {
        // already triggered
    }

    companion object {
        const val TAG = "EMV-MFPinpadFlow"
        private const val PIN_KEY_INDEX = 1
    }
}
