package com.bukuwarung.edc.card.ui.edcdevices.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.analytic.BtAnalyticConstant.MF919
import com.bukuwarung.analytic.BtAnalyticConstant.MOREFUN
import com.bukuwarung.analytic.BtAnalyticConstant.PAX
import com.bukuwarung.analytic.BtAnalyticConstant.TIANYU
import com.bukuwarung.analytic.BtAnalyticConstant.VERIFONE
import com.bukuwarung.bluetooth_printer.activities.print.adapter.PrinterDataHolder
import com.bukuwarung.edc.R
import com.bukuwarung.edc.card.ui.edcdevices.model.DeviceItem
import com.bukuwarung.edc.databinding.ListItemDevicesBinding
import com.bukuwarung.edc.util.Utils.getDeviceNameBasedOnVendor
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick

class DeviceListAdapter(
    private val pairedDeviceList: List<PrinterDataHolder>?,
    private val clickListener: OnDeviceItemClickListener
) : RecyclerView.Adapter<DeviceListAdapter.DeviceViewHolder>() {

    private var deviceList: MutableList<DeviceItem> = mutableListOf()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DeviceViewHolder {
        val binding =
            ListItemDevicesBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return DeviceViewHolder(binding, clickListener)
    }

    override fun onBindViewHolder(holder: DeviceViewHolder, position: Int) {
        holder.bind(deviceList[position])
    }

    override fun getItemCount(): Int = deviceList.size

    fun setDeviceList(deviceList: MutableList<DeviceItem>) {
        this.deviceList = deviceList
        notifyDataSetChanged()
    }

    inner class DeviceViewHolder(
        private val binding: ListItemDevicesBinding,
        private val clickListener: OnDeviceItemClickListener
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind(deviceItem: DeviceItem) {
            binding.tvDeviceBrand.text = getDeviceNameBasedOnVendor(
                deviceItem.vendor.orEmpty(),
                deviceItem.serialNumber.orEmpty()
            )
            binding.tvTid.text = buildString {
                append("TID:")
                append(deviceItem.terminalId)
            }
            binding.tvSid.text = buildString {
                append("SN:")
                append(deviceItem.serialNumber)
            }
            pairedDeviceList?.let {
                val pairedDevice = it.find { printerDataHolder ->
                    val numericPart = printerDataHolder.name.filter { char -> char.isDigit() }
                    numericPart == deviceItem.serialNumber
                }
                if (pairedDevice != null) {
                    binding.tvConnectedStatus.text = "Perangkat yang terhubung saat ini"
                    binding.tvConnectedStatus.showView()
                } else {
                    binding.tvConnectedStatus.hideView()
                }
            }
            binding.root.singleClick {
                clickListener.onDeviceItemClick(deviceItem)
            }
            deviceItem.vendor?.let { name ->
                binding.ivTrx.setImageDrawable(
                    ContextCompat.getDrawable(
                        binding.ivTrx.context,
                        when {
                            (name.contains(MOREFUN, true)) -> {
                                if (deviceItem.serialNumber?.startsWith("98") == true) {
                                    R.drawable.ic_edc_mf919
                                } else {
                                    R.drawable.ic_edc_device_morefun
                                }
                            }

                            (name.contains(MF919, true)) -> R.drawable.ic_edc_mf919
                            (name.contains(TIANYU, true)) -> R.drawable.ic_edc_device
                            (name.contains(PAX, true)) -> R.drawable.ic_edc_device_pax
                            (name.contains(VERIFONE, true)) -> R.drawable.ic_edc_device_verifone
                            else -> R.drawable.ic_edc_device_verifone
                        }
                    )
                )
            }
        }
    }

    interface OnDeviceItemClickListener {
        fun onDeviceItemClick(deviceItem: DeviceItem)
    }
}
