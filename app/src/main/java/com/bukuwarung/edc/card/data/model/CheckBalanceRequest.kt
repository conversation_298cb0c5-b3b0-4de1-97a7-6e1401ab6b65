package com.bukuwarung.edc.card.data.model

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class CheckBalanceRequest(
    @SerializedName("account_type")
    val accountType: String?,
    @SerializedName("card_expiry")
    val cardExpiry: String?,
    @SerializedName("card_number")
    val cardNumber: String?,
    @SerializedName("icc_data")
    val iccData: String?,
    @SerializedName("pin_block")
    val pinBlock: String?,
    @SerializedName("pos_entry_mode")
    val posEntryMode: String? = null,
    @SerializedName("terminal_id")
    val terminalId: String?,
    @SerializedName("track_2_data")
    val track2Data: String?
)
