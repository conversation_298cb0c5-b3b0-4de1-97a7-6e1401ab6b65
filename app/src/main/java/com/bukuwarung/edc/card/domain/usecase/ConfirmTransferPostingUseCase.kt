package com.bukuwarung.edc.card.domain.usecase

import com.bukuwarung.edc.card.data.model.TransactionConfirmRequest
import com.bukuwarung.edc.card.data.repository.EdcTransactionRepository
import com.bukuwarung.edc.global.enums.TransactionType
import com.bukuwarung.edc.util.Utils
import javax.inject.Inject

class ConfirmTransferPostingUseCase @Inject constructor(
    private val edcTransactionRepository: EdcTransactionRepository
) {
    suspend operator fun invoke(
        accountId: String,
        transactionType: TransactionType,
        transactionConfirmRequest: TransactionConfirmRequest
    ) {
        val confirmTransactionResponse =
            if (transactionType ==
                TransactionType.CASH_WITHDRAWAL
            ) {
                edcTransactionRepository.confirmCashWithdrawalPostingTransaction(
                    accountId,
                    transactionConfirmRequest
                )
            } else {
                edcTransactionRepository.confirmTransferPostingTransaction(
                    accountId,
                    transactionConfirmRequest
                )
            }
        if (confirmTransactionResponse.isSuccessful) {
            Utils.clearIncompleteTransaction()
        }
    }
}
