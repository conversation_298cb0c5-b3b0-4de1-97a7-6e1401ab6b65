package com.bukuwarung.edc.card.domain.usecase

import com.bukuwarung.edc.card.data.model.TransactionReversalRequest
import com.bukuwarung.edc.card.data.repository.EdcTransactionRepository
import javax.inject.Inject

class SubmitReversalUseCase @Inject constructor(
    private val edcTransactionRepository: EdcTransactionRepository
) {
    suspend operator fun invoke(
        accountId: String,
        transactionReversalRequest: TransactionReversalRequest
    ) = edcTransactionRepository.submitTransactionReversal(accountId, transactionReversalRequest)
}
