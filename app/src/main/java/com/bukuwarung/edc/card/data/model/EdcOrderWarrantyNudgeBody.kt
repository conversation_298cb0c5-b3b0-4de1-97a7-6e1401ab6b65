package com.bukuwarung.edc.card.data.model

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class EdcOrderWarrantyNudgeBody(
    @SerializedName("is_visible")
    val isVisible: Boolean = false,
    @SerializedName("image_url")
    val imageUrl: String? = null,
    @SerializedName("redirection_url")
    val redirectionUrl: String? = null,
    val text: String? = null,
    @SerializedName("button_text")
    val buttonText: String? = null
)
