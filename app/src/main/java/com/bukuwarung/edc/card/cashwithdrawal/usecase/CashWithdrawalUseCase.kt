package com.bukuwarung.edc.card.cashwithdrawal.usecase

import com.bukuwarung.edc.card.cashwithdrawal.model.SettlementBankAccount
import com.bukuwarung.edc.card.cashwithdrawal.model.SettlementBankAccountResponse
import com.bukuwarung.edc.card.cashwithdrawal.repository.CashWithdrawalRepo
import javax.inject.Inject
import retrofit2.Response

class CashWithdrawalUseCase @Inject constructor(private val repo: CashWithdrawalRepo) {
    suspend fun addSettlementBankAccount(
        paymentAccountId: String,
        terminalId: String,
        bankAccount: SettlementBankAccount
    ): Response<SettlementBankAccount> =
        repo.addSettlementBankAccount(paymentAccountId, terminalId, bankAccount)

    suspend fun getSettlementBankList(
        terminalId: String,
        isPrimary: Boolean?,
        isCashBankWithdrawal: Boolean?
    ): Response<SettlementBankAccountResponse> =
        repo.getSettlementBankList(terminalId, isPrimary, isCashBankWithdrawal)

    suspend fun setPrimarySettlementBankAccount(bankAccountId: String): Response<Unit> =
        repo.setPrimarySettlementBankAccount(bankAccountId)

    suspend fun deleteSettlementBankAccount(bankAccountId: String): Response<Unit> =
        repo.deleteSettlementBankAccount(bankAccountId)
}
