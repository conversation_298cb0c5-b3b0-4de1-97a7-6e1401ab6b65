package com.bukuwarung.edc.card.cardhistory.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.edc.R
import com.bukuwarung.edc.app.config.AppConfig
import com.bukuwarung.edc.card.cardhistory.model.HistoryItem
import com.bukuwarung.edc.card.cardhistory.viewmodel.HistoryListItem
import com.bukuwarung.edc.card.data.model.EndUserStatusValues
import com.bukuwarung.edc.databinding.ItemListCardHistoryBinding
import com.bukuwarung.edc.databinding.LayoutSeparatorItemBinding
import com.bukuwarung.edc.global.enums.TransactionType
import com.bukuwarung.edc.order.orderhistory.enums.EdcOrderStatus
import com.bukuwarung.edc.order.orderhistory.enums.EdcPaymentStatus
import com.bukuwarung.edc.order.orderhistory.enums.HistoryType
import com.bukuwarung.edc.util.DateTimeUtils
import com.bukuwarung.edc.util.DateTimeUtils.DD_MMM_YYYY_HH_MM
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.isNotNullOrBlank
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick
import com.bukuwarung.edc.app.config.VariantConfig

class TransactionHistoryAdapter(
    private val context: Context,
    private val isEdcOrderHistory: String,
    private val onClickListenerCallback: (HistoryItem) -> Unit,
    private val activateEDCOnCLickListenerCallback: (HistoryItem) -> Unit
) : PagingDataAdapter<HistoryListItem, RecyclerView.ViewHolder>(
    Companion
) {

    companion object : DiffUtil.ItemCallback<HistoryListItem>() {

        private fun compareSeparator(oldItem: HistoryListItem, newItem: HistoryListItem): Boolean =
            (
                oldItem is HistoryListItem.HistoryItemSeparator &&
                    newItem is HistoryListItem.HistoryItemSeparator &&
                    oldItem.data == newItem.data
                )

        private fun compareHistoryItem(
            oldItem: HistoryListItem,
            newItem: HistoryListItem
        ): Boolean = (
            oldItem is HistoryListItem.HistoryItem && newItem is HistoryListItem.HistoryItem &&
                oldItem.historyItem.id == newItem.historyItem.id
            )

        private enum class ViewType {
            HISTORY_ITEM,
            SEPARATOR
        }

        override fun areItemsTheSame(oldItem: HistoryListItem, newItem: HistoryListItem): Boolean =
            compareHistoryItem(oldItem, newItem) || compareSeparator(oldItem, newItem)

        override fun areContentsTheSame(
            oldItem: HistoryListItem,
            newItem: HistoryListItem
        ): Boolean = oldItem == newItem
    }

    inner class SeparatorViewHolder(val binding: LayoutSeparatorItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(date: String) {
            val todayDate = DateTimeUtils.getFormattedDateTime(
                System.currentTimeMillis(),
                DateTimeUtils.DD_MMM_YYYY
            )
            binding.tvSeparatorDate.text = if (date == todayDate) "Hari ini" else date
        }
    }

    inner class TransactionIteMViewHolder(val binding: ItemListCardHistoryBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(data: HistoryItem) {
            if (isEdcOrderHistory == HistoryType.TRANSACTION.name) {
                binding.btnActivateEdc.hideView()
                when (data.endUserStatus) {
                    EndUserStatusValues.SUCCESS -> {
                        binding.tvTrxStatus.text =
                            context.getString(R.string.payment_status_completed)
                        binding.tvTrxStatus.setTextColor(
                            ContextCompat.getColor(
                                binding.tvTrxStatus.context,
                                R.color.green
                            )
                        )
                    }

                    EndUserStatusValues.PENDING_SETTLEMENT -> {
                        binding.tvTrxStatus.text = "Pencairan Diproses"
                        binding.tvTrxStatus.setTextColor(
                            ContextCompat.getColor(
                                binding.tvTrxStatus.context,
                                R.color.yellow
                            )
                        )
                    }

                    EndUserStatusValues.PENDING_REFRESH,
                    EndUserStatusValues.PENDING -> {
                        binding.tvTrxStatus.text = context.getString(R.string.pending)
                        binding.tvTrxStatus.setTextColor(
                            ContextCompat.getColor(
                                binding.tvTrxStatus.context,
                                R.color.yellow
                            )
                        )
                    }

                    else -> {
                        binding.tvTrxStatus.text = context.getString(R.string.failed)
                        binding.tvTrxStatus.setTextColor(
                            ContextCompat.getColor(
                                binding.tvTrxStatus.context,
                                R.color.red_error
                            )
                        )
                    }
                }
                if (data.type == TransactionType.BALANCE_INQUIRY.type) {
                    binding.tvTrxAmount.hideView()
                    binding.tvTrxName.text =
                        context.getString(R.string.filter_history_check_balance)
                    binding.ivTrx.setImageDrawable(
                        ContextCompat.getDrawable(
                            binding.ivTrx.context,
                            R.drawable.ic_check_balance
                        )
                    )
                } else if (data.type == TransactionType.TRANSFER_POSTING.type) {
                    binding.tvTrxAmount.showView()
                    binding.tvTrxAmount.text = Utils.formatAmount(data.amount)
                    binding.tvTrxName.text = context.getString(R.string.transfer)
                    binding.ivTrx.setImageDrawable(
                        ContextCompat.getDrawable(
                            binding.ivTrx.context,
                            R.drawable.ic_transfer
                        )
                    )
                } else {
                    binding.tvTrxAmount.showView()
                    binding.tvTrxAmount.text = Utils.formatAmount(data.amount)
                    binding.tvTrxName.text = context.getString(R.string.withdraw_cash)
                    binding.ivTrx.setImageDrawable(
                        ContextCompat.getDrawable(
                            binding.ivTrx.context,
                            R.drawable.ic_cash_withdrawal
                        )
                    )
                }
                binding.tvReferenceNumber.text =
                    context.getString(R.string.reference_number).plus(" ")
                        .plus(data.referenceNumber)
                binding.tvTrxTimestamp.text =
                    DateTimeUtils.getUTCTimeToLocalDateTime(
                        data.date,
                        DateTimeUtils.DD_MMM_YYYY_HH_MM
                    )
            } else {
                // If OrderAmount is null it's a EZA order
                // if OrderAmount is less than 100000 and vendor is MoreFun, it's a MoreFun insurance order

                val isMorefunInsurance =
                    data.vendor
                        ?.equals(
                            "MoreFun",
                            false
                        ).isTrue && (
                        data.orderAmount.isNotNullOrBlank() && (
                            data.orderAmount?.toDouble()
                                ?: 0.0
                            ) < 100000.0
                        )
                binding.tvReferenceNumber.text =
                    context.getString(R.string.reference_number).plus(" ")
                        .plus(data.referenceNumber)
                binding.tvReferenceNumber.visibility =
                    data.referenceNumber.isNotNullOrBlank().asVisibility()
                binding.tvTrxName.text =
                    context.getString(
                        when {
                            data.planType == "RENTAL" -> R.string.rent_edc
                            isMorefunInsurance -> R.string.extra_warranty
                            else -> AppConfig.current.variantConfig.transactionHistoryDeviceLabel
                        }
                    )

                binding.tvTrxAmount.visibility = data.orderAmount.isNotNullOrBlank().asVisibility()
                binding.tvTrxAmount.text = Utils.formatAmount(data.orderAmount?.toDouble())

                binding.tvTrxTimestamp.text =
                    DateTimeUtils.getUTCTimeToLocalDateTime(data.paymentDate, DD_MMM_YYYY_HH_MM)
                val image =
                    if (isMorefunInsurance) R.drawable.ic_warranty else R.drawable.ic_edc_buy_rent

                binding.ivTrx.setImageDrawable(
                    ContextCompat.getDrawable(
                        binding.ivTrx.context,
                        image
                    )
                )
                binding.tvSerialNumber.text = "SN:" + data.serialNumber.orEmpty()
                binding.tvSerialNumber.visibility =
                    data.serialNumber.isNotNullOrBlank().asVisibility()

                when {
                    data.paymentStatus.equals(EdcPaymentStatus.REFUNDED.name, true) -> {
                        // refunded successfully state
                        binding.btnActivateEdc.hideView()
                        binding.tvTrxStatus.text = context.getString(R.string.refund_successful)
                        binding.tvTrxStatus.setTextColor(
                            ContextCompat.getColor(
                                binding.tvTrxStatus.context,
                                R.color.green
                            )
                        )
                    }

                    data.paymentStatus.equals(EdcPaymentStatus.REFUNDING_FAILED.name, true) -> {
                        // refund failed state
                        binding.btnActivateEdc.hideView()
                        binding.tvTrxStatus.text = context.getString(R.string.refund_failed_1)
                        binding.tvTrxStatus.setTextColor(
                            ContextCompat.getColor(
                                binding.tvTrxStatus.context,
                                R.color.red_error
                            )
                        )
                    }

                    data.paymentStatus.equals(EdcPaymentStatus.REFUNDING.name, true) -> {
                        // refunding state
                        binding.btnActivateEdc.hideView()
                        binding.tvTrxStatus.text = context.getString(R.string.returns_processing)
                        binding.tvTrxStatus.setTextColor(
                            ContextCompat.getColor(
                                binding.tvTrxStatus.context,
                                R.color.yellow
                            )
                        )
                    }

                    data.paymentStatus.equals(EdcPaymentStatus.PAID.name, true) -> {
                        if (data.status == "Waiting_for_ops" && !isMorefunInsurance) {
                            binding.btnActivateEdc.showView()
                        } else {
                            binding.btnActivateEdc.hideView()
                        }

                        binding.tvTrxStatus.text = context.getString(R.string.paid_label)
                        binding.tvTrxStatus.setTextColor(
                            ContextCompat.getColor(
                                binding.tvTrxStatus.context,
                                R.color.green
                            )
                        )
                        // Activate EDC after successful payment.
                    }

                    data.status.equals(EdcOrderStatus.CANCELLED.name, true) -> {
                        binding.btnActivateEdc.hideView()
                        binding.tvTrxStatus.text = context.getString(R.string.cancelled_label)
                        binding.tvTrxStatus.setTextColor(
                            ContextCompat.getColor(
                                binding.tvTrxStatus.context,
                                R.color.red_error
                            )
                        )
                    }

                    data.paymentStatus.equals(
                        EdcOrderStatus.UNASSIGNED.name,
                        true
                    ) && data.paymentLinkExpired.isTrue -> {
                        binding.btnActivateEdc.hideView()
                        binding.tvTrxStatus.text = context.getString(R.string.expired_label)
                        binding.tvTrxStatus.setTextColor(
                            ContextCompat.getColor(
                                binding.tvTrxStatus.context,
                                R.color.red_error
                            )
                        )
                    }

                    else -> {
                        // pending
                        binding.btnActivateEdc.hideView()
                        binding.tvTrxStatus.text = context.getString(R.string.in_process)
                        binding.tvTrxStatus.setTextColor(
                            ContextCompat.getColor(
                                binding.tvTrxStatus.context,
                                R.color.yellow
                            )
                        )
                    }
                }
            }

            binding.root.singleClick {
                onClickListenerCallback.invoke(data)
            }

            binding.btnActivateEdc.setOnClickListener {
                activateEDCOnCLickListenerCallback.invoke(data)
            }
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        getItem(position)?.let {
            when (it) {
                is HistoryListItem.HistoryItem -> (holder as TransactionIteMViewHolder).bind(
                    it.historyItem
                )

                is HistoryListItem.HistoryItemSeparator -> (holder as SeparatorViewHolder).bind(
                    it.data
                )
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val view =
            ItemListCardHistoryBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        val separatorView =
            LayoutSeparatorItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        if (viewType == ViewType.HISTORY_ITEM.ordinal) {
            return TransactionIteMViewHolder(view)
        } else {
            return SeparatorViewHolder(separatorView)
        }
    }

    override fun getItemViewType(position: Int): Int = when (getItem(position)) {
        is HistoryListItem.HistoryItem -> ViewType.HISTORY_ITEM.ordinal
        is HistoryListItem.HistoryItemSeparator -> ViewType.SEPARATOR.ordinal
        else -> throw UnsupportedOperationException(Exception())
    }
}
