package com.bukuwarung.edc.card.domain.usecase.tms

import android.util.Log
import com.bukuwarung.analytic.BtAnalytics.bwLog
import com.bukuwarung.edc.card.data.model.Terminal
import com.bukuwarung.edc.card.data.repository.TerminalManagementRepository
import com.bukuwarung.edc.card.domain.model.TerminalSystemException
import com.bukuwarung.edc.card.domain.model.TmsError
import com.bukuwarung.edc.card.domain.model.TmsFailureType
import com.bukuwarung.edc.card.domain.model.TmsOperationResponse
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.errorMessage
import com.google.gson.Gson
import javax.inject.Inject
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.callbackFlow

class UpdateTerminalUseCase @Inject constructor(
    private val repository: TerminalManagementRepository
) {
    suspend operator fun invoke(serialNumber: String, terminal: Terminal) = callbackFlow {
        try {
            val response = repository.updateTerminal(serialNumber, terminal)
            if (response.isSuccessful) {
                trySend(TmsOperationResponse.Success(response.body()?.data as Terminal))
            } else {
                if (response.code() == 401) {
                    trySend(
                        TmsOperationResponse.Failure(TmsError(TmsFailureType.UNAUTHORIZED))
                    )
                } else {
                    val errorBody = response.errorBody()?.string()
                    if (errorBody != null && errorBody.contains("errorCode")) {
                        val exception: TerminalSystemException = try {
                            Gson().fromJson(errorBody, TerminalSystemException::class.java)
                        } catch (e: Exception) {
                            TerminalSystemException(false, "E20", response.errorMessage())
                        }
                        trySend(
                            TmsOperationResponse.Failure(
                                TmsError(TmsFailureType.fromCode(exception.errorCode))
                            )
                        )
                        bwLog("TerminalSystemException: ${exception.messages}")
                    } else {
                        Utils.trackDeviceActivationError(TmsFailureType.HEARTBEAT_FAILURE)
                        trySend(
                            TmsOperationResponse.Failure(TmsError(TmsFailureType.HEARTBEAT_FAILURE))
                        )
                    }
                }
                bwLog(Exception("HEARTBEAT_FAILURE $response"))
            }
        } catch (e: Exception) {
            Utils.trackDeviceActivationError(TmsFailureType.HEARTBEAT_FAILURE)
            trySend(
                TmsOperationResponse.Failure(TmsError(TmsFailureType.HEARTBEAT_FAILURE))
            )
            Log.d("UpdateTerminalUseCase", "HEARTBEAT_FAILURE  $e")
            bwLog("HEARTBEAT_FAILURE  $e")
        } finally {
            awaitClose {
                // flow closed
            }
        }
    }
}
