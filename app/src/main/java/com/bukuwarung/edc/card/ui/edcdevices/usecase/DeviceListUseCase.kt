package com.bukuwarung.edc.card.ui.edcdevices.usecase

import com.bukuwarung.edc.card.ui.edcdevices.model.DeviceListResponse
import com.bukuwarung.edc.card.ui.edcdevices.repo.DeviceListRepository
import com.bukuwarung.network.utils.ResourceState
import javax.inject.Inject

class DeviceListUseCase @Inject constructor(
    private val deviceListRepository: DeviceListRepository
) {
    suspend fun getDeviceList(devicePlan: String): ResourceState<DeviceListResponse> =
        deviceListRepository.getDevice(devicePlan)
}
