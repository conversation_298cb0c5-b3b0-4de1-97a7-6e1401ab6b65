package com.bukuwarung.edc.card.data.service.bluetooth

import android.os.CountDownTimer
import android.util.Log
import com.bukuwarung.edc.card.domain.model.EdcResponse
import com.bukuwarung.edc.settings.ui.setting.MiniatmDebugSettingActivity
import com.bukuwarung.edc.util.Utils
import javax.inject.Inject
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.callbackFlow

class CheckCardFlow @Inject constructor(
    private val edcDeviceService: ExternalCardReaderServiceImpl
) {

    fun stopCheckCard(): Boolean {
        Log.d(TAG, "check card stopCheckCard")
        try {
        } catch (e: Exception) {
            e.printStackTrace()
            return false
        }
        return true
    }

    fun checkCardAvailability(timeOut: Long) = callbackFlow {
        Log.d(TAG, "start check card")
        if (Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_DEVICE)) {
            trySend(EdcResponse.CardAvailable(false))
        }
        try {
            val timer = object : CountDownTimer(500, 100) {
                override fun onTick(millisUntilFinished: Long) {
                    trySend(EdcResponse.CardAvailable(true))
                }

                override fun onFinish() {
                    trySend(EdcResponse.CardAvailable(false))
                }
            }
            trySend(EdcResponse.CardAvailable(true))
            timer.start()
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            awaitClose {
                Log.d(TAG, "check card availability flow close")
            }
        }
    }

    companion object {
        const val TAG = "EDC_SEARCH_CARD"
    }
}
