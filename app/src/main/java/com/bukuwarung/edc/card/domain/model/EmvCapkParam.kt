package com.bukuwarung.edc.card.domain.model

import com.bukuwarung.edc.card.domain.model.base.EMVTLVParam

class EmvCapkParam : EMVTLVParam() {
    init {
        /**
         * default value of some tags
         * value null means the tag is optional
         */
        defaultTagValue = arrayOf(
            DefaultTagValue(0xDF06, "01"),
            DefaultTagValue(TAG_ALGORITHM_DF07, "01"),
            DefaultTagValue(TAG_EXPONENT_DF04, "03")
        )
    }

    override fun clean() {
        super.clean()
        if (defaultTagValue != null) {
            for (tagValue in defaultTagValue) {
                tagValue.available = true
            }
        }
    }

    companion object {
        /**
         * RID Tag list
         */
        const val TAG_RID_9F06 = 0x9F06
        const val TAG_INDEX_9F22 = 0x9F22
        const val TAG_EXPIRY_DATE_DF05 = 0xDF05
        const val TAG_ALGORITHM_DF07 = 0xDF07
        const val TAG_KEY_DF02 = 0xDF02
        const val TAG_EXPONENT_DF04 = 0xDF04
        const val TAG_HASH_DF03 = 0xDF03
    }
}
