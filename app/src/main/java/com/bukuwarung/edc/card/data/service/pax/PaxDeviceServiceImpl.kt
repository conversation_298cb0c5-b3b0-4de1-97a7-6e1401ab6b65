package com.bukuwarung.edc.card.data.service.pax

import PinBlockEncryptionUtils
import android.content.Context
import android.graphics.Bitmap
import android.os.Bundle
import android.util.Log
import com.bukuwarung.edc.card.data.model.PinpadButtonCoordinates
import com.bukuwarung.edc.card.data.service.verifone.VFDeviceServiceImpl
import com.bukuwarung.edc.card.domain.model.EdcResponse
import com.bukuwarung.edc.card.domain.model.ErrorData
import com.bukuwarung.edc.card.domain.model.ReceiptPrintCommand
import com.bukuwarung.edc.card.domain.service.EmvResponse
import com.bukuwarung.edc.card.domain.service.IEdcDeviceService
import com.bukuwarung.edc.card.domain.service.LoadKeyResponse
import com.bukuwarung.edc.card.domain.service.PinBlockResponse
import com.bukuwarung.edc.card.domain.service.PrinterResponse
import com.bukuwarung.edc.card.domain.service.TransactionValidationResult
import com.bukuwarung.edc.global.EdcApplication
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.isTrue
import com.pax.dal.entity.EPedType
import com.pax.p_service.aidl.IDeviceService
import com.pax.p_service.aidl.IEMV
import com.pax.p_service.aidl.IPinpad
import com.vfi.smartpos.deviceservice.aidl.PinpadKeyType
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class PaxDeviceServiceImpl @Inject constructor(val context: Context) : IEdcDeviceService {

    var device: IDeviceService? = null
    var pinpad: IPinpad? = null
    var emv: IEMV? = null

    private val iPed by lazy { EdcApplication.dal?.getPed(EPedType.INTERNAL) }

    companion object {
        const val TERMINAL_MASTER_KEY_ID = 97
        const val TERMINAL_WORK_KEY_ID = 1
    }

    override fun connectService(): Boolean = PaxServiceConnection(this).connect()

    override fun startCheckCard(timeOut: Int) = PaxCheckCardFlow(this).startCheckCard(timeOut)

    override fun stopCheckCard() = PaxCheckCardFlow(this).stopCheckCard()

    override fun abortEmv() = PaxDeviceEmvFlow(this).abortEmv()

    override fun doBeep(durationMills: Int) {
        device?.beeper?.startBeep(durationMills)
    }

    override fun updateAID(operation: Int, aidType: Int, aid: String?): Boolean =
        PaxDeviceEmvFlow(this).updateAID(operation, aidType, aid)

    override fun updateRID(operation: Int, rid: String?): Boolean =
        PaxDeviceEmvFlow(this).updateRID(operation, rid)

    override fun startInsertCardRead(emvIntent: Bundle, timeout: Int) = PaxDeviceEmvFlow(this)
        .startInsertCardRead(emvIntent, timeout) as Flow<EmvResponse>

    override fun startPrint(): Flow<PrinterResponse> {
        TODO("Not yet implemented")
    }

    override fun printReceipt(
        printCommand: ReceiptPrintCommand,
        header: ByteArray?,
        bukuAgen: ByteArray?,
        headerBitmap: Bitmap?,
        bukuAgenBitmap: Bitmap?,
        showFooterLogo: Boolean
    ) = PaxReceiptPrinterFlow(
        this,
        printCommand,
        header,
        bukuAgen,
        headerBitmap,
        bukuAgenBitmap,
        showFooterLogo
    ).printReceipt() as Flow<PrinterResponse>

    override fun printPaymentReceipt(paymentReceipt: String): Flow<PrinterResponse> {
        TODO("Not yet implemented")
    }

    override fun printTestSlip(): Flow<PrinterResponse> {
        TODO("Not yet implemented")
    }

    override fun getEmvTagData(tags: Array<String>): String =
        PaxDeviceEmvFlow(this).getEmvTagData(tags)

    override fun checkCardAvailability(timeOut: Long): Flow<EdcResponse.CardAvailable> =
        PaxCheckCardFlow(this).checkCardAvailability(timeOut)

    override fun inputOnlineResult(
        responseCode: String,
        field55IccData: String
    ): Flow<TransactionValidationResult> =
        PaxDeviceEmvFlow(this).inputOnlineResult(responseCode, field55IccData)
    /*
    masterKey -> TMK
    workingKey -> TWK/TPK
     */

    override fun loadKeys(masterKey: String, workKey: String): LoadKeyResponse {
        var isMasterKeyLoaded: Boolean? = false
        var isWorkingKeyLoaded: Boolean? = false
        try {
            isMasterKeyLoaded = device?.getPinpad(0)?.loadMainKey(
                VFDeviceServiceImpl.TERMINAL_MASTER_KEY_ID,
                Utils.hexStr2Byte(masterKey),
                null
            )
            Log.d("PIN_BLOCK", "loaded master_key: $isMasterKeyLoaded")
        } catch (e: Exception) {
            e.printStackTrace()
            return EdcResponse.Failure(ErrorData(-1, "Load master key failed"))
        }
        try {
            isWorkingKeyLoaded = device?.getPinpad(0)?.loadWorkKey(
                PinpadKeyType.PINKEY,
                VFDeviceServiceImpl.TERMINAL_MASTER_KEY_ID,
                VFDeviceServiceImpl.TERMINAL_WORK_KEY_ID,
                Utils.hexStr2Byte(workKey),
                null
            )
            Log.d("PIN_BLOCK", "loaded $isWorkingKeyLoaded workingKey:" + workKey)
        } catch (e: Exception) {
            e.printStackTrace()
            return EdcResponse.Failure(ErrorData(-1, "Load work key failed"))
        }
        return if (isWorkingKeyLoaded.isTrue && isMasterKeyLoaded.isTrue) {
            EdcResponse.Success(true)
        } else {
            EdcResponse.Failure(ErrorData(-1, "UNKNOWN"))
        }
    }

    override fun getPinBlock(panNumber: String, pin: String): PinBlockResponse {
        val pinBlock = PinBlockEncryptionUtils.encryptPinFormat0(
            pin,
            panNumber,
            Utils.getMasterKey(),
            Utils.getWorkKey()
        )
        return EdcResponse.Success(pinBlock)
    }

    override fun initPinpad(cardNumber: String, pinKeyCoordinate: List<PinpadButtonCoordinates>) =
        PaxPinpadFlow(this).initPinpad(cardNumber, pinKeyCoordinate)

    override fun startPinpadPinInput() {
        pinpad?.startPinInputCustomView()
    }

    override fun stopPinpadPinInput() {
        pinpad?.endPinInputCustomView()
    }

    override fun importCardConfirmResult(isConfirm: Boolean) =
        PaxDeviceEmvFlow(this).importCardConfirmResult(isConfirm)

    override fun importPin(pinBlock: String) = PaxDeviceEmvFlow(this).importPin(pinBlock)

    override fun loadMasterKey(masterKey: String): Boolean {
        var isMasterKeyLoaded: Boolean = false
        try {
            isMasterKeyLoaded = device?.getPinpad(0)?.loadMainKey(
                VFDeviceServiceImpl.TERMINAL_MASTER_KEY_ID,
                Utils.hexStr2Byte(masterKey),
                null
            ) == true
            Log.d("PIN_BLOCK", "loaded master_key: $isMasterKeyLoaded")
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return isMasterKeyLoaded
    }
}
