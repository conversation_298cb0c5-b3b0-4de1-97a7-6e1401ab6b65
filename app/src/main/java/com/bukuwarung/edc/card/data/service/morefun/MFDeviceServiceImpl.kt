package com.bukuwarung.edc.card.data.service.morefun

import PinBlockEncryptionUtils
import android.content.Context
import android.graphics.Bitmap
import android.os.Bundle
import android.os.RemoteException
import android.util.Log
import android.view.Gravity
import com.bukuwarung.cardreader.morefun.contant.Constants
import com.bukuwarung.edc.card.constant.ErrorStatus
import com.bukuwarung.edc.card.data.model.PinpadButtonCoordinates
import com.bukuwarung.edc.card.data.util.EmvUtil
import com.bukuwarung.edc.card.domain.model.EdcResponse
import com.bukuwarung.edc.card.domain.model.PrinterResult
import com.bukuwarung.edc.card.domain.model.ReceiptPrintCommand
import com.bukuwarung.edc.card.domain.service.EmvResponse
import com.bukuwarung.edc.card.domain.service.IEdcDeviceService
import com.bukuwarung.edc.card.domain.service.LoadKeyResponse
import com.bukuwarung.edc.card.domain.service.PinBlockResponse
import com.bukuwarung.edc.card.domain.service.PrinterResponse
import com.bukuwarung.edc.card.domain.service.TransactionValidationResult
import com.bukuwarung.edc.device.DeviceHelper
import com.bukuwarung.edc.util.HexUtil
import com.bukuwarung.edc.util.PrinterUtil
import com.bukuwarung.edc.util.PrinterUtil.makeLineText
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.Utils.bwLog
import com.morefun.yapi.ServiceResult
import com.morefun.yapi.device.ped.KeyType
import com.morefun.yapi.device.printer.FontFamily
import com.morefun.yapi.device.printer.MulPrintStrEntity
import com.morefun.yapi.device.printer.OnPrintListener
import com.morefun.yapi.device.printer.PrinterConfig
import com.vfi.smartpos.deviceservice.aidl.IDeviceService
import com.vfi.smartpos.deviceservice.aidl.IEMV
import com.vfi.smartpos.deviceservice.aidl.IPinpad
import com.vfi.smartpos.deviceservice.aidl.IPrinter
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow

@Singleton
class MFDeviceServiceImpl @Inject constructor(val context: Context) : IEdcDeviceService {

    val tag = "EMV-MFDeviceServiceImpl"

    var device: IDeviceService? = null
    var pinpad: IPinpad? = null
    var emv: IEMV? = null
    var printer: IPrinter? = null

    private val tekKeyIndex = 1
    private val mainKeyIndex = 1
    private val pinKeyIndex = 1
    private val macKeyIndex = 0x09
    private val tdkKeyIndex = 1

    override fun connectService(): Boolean = true

    override fun startCheckCard(timeOut: Int) =
        MFCheckCardFlow.getInstance(this).startCheckCard(timeOut)

    override fun startInsertCardRead(emvIntent: Bundle, timeout: Int) =
        MFEmvFlow.getInstance(this).startInsertCardRead(emvIntent, timeout) as Flow<EmvResponse>

    override fun startPrint(): Flow<PrinterResponse> {
        TODO("Not yet implemented")
    }

    override fun stopCheckCard() = MFCheckCardFlow.getInstance(this).stopCheckCard()
    override fun printReceipt(
        printCommand: ReceiptPrintCommand,
        header: ByteArray?,
        bukuAgen: ByteArray?,
        headerBitmap: Bitmap?,
        bukuAgenBitmap: Bitmap?,
        showFooterLogo: Boolean
    ) = UtilReceiptPrinterFlow(
        this,
        printCommand,
        header,
        bukuAgen,
        headerBitmap,
        bukuAgenBitmap,
        showFooterLogo
    ).printReceipt() as Flow<PrinterResponse>

    override fun printPaymentReceipt(paymentReceipt: String): Flow<PrinterResponse> {
        TODO("Not yet implemented")
    }

    override fun printTestSlip(): Flow<PrinterResponse> = callbackFlow {
        val config = Bundle()
        config.putInt(PrinterConfig.COMMON_GRAYLEVEL, PrinterConfig.PRINT_DENSITY_LIGHT)
        val list: MutableList<MulPrintStrEntity> = ArrayList()

        list.add(
            MulPrintStrEntity(
                makeLineText(
                    PrinterUtil.TextItem("Bukuwarung EDC App Test Print").setFont(FontFamily.MIDDLE)
                        .setPxSize(180F)
                        .setPaddingAlign(Gravity.FILL_HORIZONTAL)
                ),
                FontFamily.MIDDLE
            )
        )

        DeviceHelper.getPrinter().printStr(
            list,
            object : OnPrintListener.Stub() {
                @Throws(RemoteException::class)
                override fun onPrintResult(result: Int) {
                    if (result == ServiceResult.Success) {
                        val printerResult = PrinterResult(true, ErrorStatus.ERROR_NONE)
                        trySend(EdcResponse.Success(printerResult))
                    } else {
                        trySend(
                            EdcResponse.Success(
                                PrinterResult(
                                    false,
                                    ErrorStatus.findByMFErrorCode(result)
                                )
                            )
                        )
                    }
                }
            },
            config
        )
        awaitClose { /* Clean up if needed */ }
    }

    override fun loadKeys(masterKey: String, workKey: String): LoadKeyResponse {
        loadTEK()
        loadCipherMainKey(masterKey)
        loadWorkKey(workKey)
        return EdcResponse.Success(true)
    }

    override fun getPinBlock(panNumber: String, pin: String): PinBlockResponse {
        val pinBlock = PinBlockEncryptionUtils.encryptPinFormat0(
            pin,
            panNumber,
            Utils.getMasterKey(),
            Utils.getWorkKey()
        )
        return EdcResponse.Success(pinBlock)
    }

    override fun importPin(pinBlock: String) = MFEmvFlow(this).importPin(pinBlock)

    override fun inputOnlineResult(
        responseCode: String,
        field55IccData: String
    ): Flow<TransactionValidationResult> =
        MFEmvFlow.getInstance(this).inputOnlineResult(responseCode, field55IccData)

    override fun abortEmv() {
        try {
            DeviceHelper.getEmvHandler().endPBOC()
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }

    override fun getEmvTagData(tags: Array<String>): String = MFEmvFlow(this).getEmvTagData(tags)

    override fun checkCardAvailability(timeOut: Long): Flow<EdcResponse.CardAvailable> =
        MFCheckCardFlow.getInstance(this).checkCardAvailability(timeOut)

    override fun stopPinpadPinInput() {
        Log.e(tag, "stopPinpadPinInput Not yet implemented")
    }

    override fun startPinpadPinInput() {
        MFPinpadFlow(this).startPinpadPinInput()
    }

    override fun initPinpad(cardNumber: String, pinKeyCoordinate: List<PinpadButtonCoordinates>) =
        MFPinpadFlow(this).initPinpad(cardNumber, pinKeyCoordinate)

    override fun doBeep(durationMills: Int) {
        try {
//            DeviceHelper.getBeeper().beep(NORMAL_BEEP)
        } catch (e: RemoteException) {
            e.printStackTrace()
        }
    }

    override fun loadMasterKey(masterKey: String): Boolean {
        loadClearMainKey(masterKey)
        return true
    }

    override fun updateAID(operation: Int, aidType: Int, aid: String?): Boolean {
        DeviceHelper.getEmvHandler().clearAIDParam()
        val result = DeviceHelper.getEmvHandler()
            .addAidParam(HexUtil.hexStringToByte(Constants.NsiccsConfig.AID_STRING))
        val emvParamsResult =
            DeviceHelper.getEmvHandler().initTermConfig(EmvUtil.getInitialTermConfig())
        return (result == 0 && emvParamsResult == 0)
    }

    override fun updateRID(operation: Int, rid: String?): Boolean {
        val clearRidResult = DeviceHelper.getEmvHandler().clearCAPKParam()
        bwLog(msg = "[MFDeviceService] clearRidResult=$clearRidResult")
        for (rid in Constants.rids) {
            val result =
                DeviceHelper.getEmvHandler().addCAPKParam(HexUtil.hexStringToByte(rid))
            if (result != 0) {
                Log.d("card_reader", "updateCapkResult=false")
                return false
            } else {
                Log.d("card_reader", "updateCapkResult=true")
            }
        }
        return true
    }

    override fun importCardConfirmResult(isConfirm: Boolean) {
        try {
//            DeviceHelper.getEmvHandler().(isConfirm)
        } catch (e: RemoteException) {
            e.printStackTrace()
        }
    }

    private fun loadCipherMainKey(masterKeyStr: String) {
        try {
            val masterKey: ByteArray =
                HexUtil.hexStringToByte(masterKeyStr)
            val checkValue: ByteArray? = null

            val ret = DeviceHelper.getPed()
                .loadEncryptMainKey(tekKeyIndex, mainKeyIndex, masterKey, checkValue)
            Log.d("load_ley", "loadEncryptMainKey:$ret")
        } catch (e: RemoteException) {
            e.printStackTrace()
        }
    }

    private fun loadTEK() {
        try {
            val key = HexUtil.hexStringToByte("111111111111111111111111111111111111111111111111")
            val checkValue = HexUtil.hexStringToByte("82E13665")

            val ret = DeviceHelper.getPed().loadTEK(tekKeyIndex, key, checkValue)
            Log.d("load_tek", "loadKEK:$ret")
        } catch (e: RemoteException) {
        }
    }

    private fun loadClearMainKey(masterKey: String) {
        try {
            val key: ByteArray =
                HexUtil.hexStringToByte(masterKey)
//            val checkValue: ByteArray = HexUtil.hexStringToByte("82E13665")

            val ret = DeviceHelper.getPed().loadMainKey(mainKeyIndex, key, null)
            Log.d("load_ley", "loadClearMainKey:$ret")
        } catch (e: RemoteException) {
            e.printStackTrace()
        }
    }

    private fun loadWorkKey(workKeyStr: String) {
        try {
            val workKey: ByteArray =
                HexUtil.hexStringToByte(workKeyStr)
            val checkValue: ByteArray? = null

            val loadPinKey = DeviceHelper.getPed()
                .loadWorkKey(KeyType.PIN_KEY, mainKeyIndex, pinKeyIndex, workKey, checkValue)
            val loadMacKey = DeviceHelper.getPed()
                .loadWorkKey(KeyType.MAC_KEY, mainKeyIndex, macKeyIndex, workKey, checkValue)
            val loadTdkKey = DeviceHelper.getPed()
                .loadWorkKey(KeyType.TDK_KEY, mainKeyIndex, tdkKeyIndex, workKey, checkValue)

            Log.d("load_ley", "loadPinKey:$loadPinKey")
            Log.d("load_ley", "loadMacKey:$loadMacKey")
            Log.d("load_ley", "loadTdkKey:$loadTdkKey")
        } catch (e: RemoteException) {
            e.printStackTrace()
        }
    }

    private fun loadPlainWorkKey() {
        try {
            val workKey: ByteArray =
                HexUtil.hexStringToByte("111111111111111122222222222222223333333333333333")

            val loadPinKey = DeviceHelper.getPed()
                .loadPlainWorkKey(KeyType.PIN_KEY, pinKeyIndex, workKey, null)
            val loadMacKey = DeviceHelper.getPed()
                .loadPlainWorkKey(KeyType.MAC_KEY, macKeyIndex, workKey, null)
            val loadTdkKey = DeviceHelper.getPed()
                .loadPlainWorkKey(KeyType.TDK_KEY, tdkKeyIndex, workKey, null)

            Log.d("load_ley", "loadPinKey:$loadPinKey")
            Log.d("load_ley", "loadMacKey:$loadMacKey")
            Log.d("load_ley", "loadTdkKey:$loadTdkKey")
        } catch (e: RemoteException) {
            e.printStackTrace()
        }
    }
}
