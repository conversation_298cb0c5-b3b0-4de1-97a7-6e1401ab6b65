package com.bukuwarung.edc.card.data.service.morefun

import android.graphics.Bitmap
import android.os.Bundle
import android.view.Gravity
import com.bukuwarung.edc.app.config.AppConfig
import com.bukuwarung.edc.card.constant.ErrorStatus
import com.bukuwarung.edc.card.constant.PrintConst.TRANSACTION_TYPE_CHECK_BALANCE
import com.bukuwarung.edc.card.data.model.CardReceiptResponse
import com.bukuwarung.edc.card.data.model.EndUserStatusValues
import com.bukuwarung.edc.card.domain.model.EdcResponse
import com.bukuwarung.edc.card.domain.model.PrinterResult
import com.bukuwarung.edc.card.domain.model.ReceiptPrintCommand
import com.bukuwarung.edc.card.domain.model.consts.Constants.CARD_ENTRY_MODE_MAG
import com.bukuwarung.edc.card.ui.receipt.CardReceiptViewModel
import com.bukuwarung.edc.device.DeviceHelper
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.payments.util.PaymentAuxilliary
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.edc.util.DateTimeUtils
import com.bukuwarung.edc.util.PrinterUtil
import com.bukuwarung.edc.util.PrinterUtil.makeLineText
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.Utils.bwLog
import com.bukuwarung.edc.util.isNotNullOrBlank
import com.bukuwarung.edc.util.orDash
import com.bukuwarung.edc.util.orDefault
import com.bukuwarung.edc.util.orNil
import com.morefun.yapi.ServiceResult
import com.morefun.yapi.device.printer.FontFamily
import com.morefun.yapi.device.printer.MulPrintStrEntity
import com.morefun.yapi.device.printer.OnPrintListener
import com.vfi.smartpos.deviceservice.aidl.PrinterConfig
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.callbackFlow
import javax.inject.Inject

/*
 * temporary class to handle miniatm receipt
 * needs to be moved to generic printer
 */
class UtilReceiptPrinterFlow @Inject constructor(
    private val edcDeviceService: MFDeviceServiceImpl,
    private val printCommand: ReceiptPrintCommand,
    private val header: ByteArray?,
    private val bukuAgen: ByteArray?,
    private val headerBitmap: Bitmap?,
    private val bukuAgenBitmap: Bitmap?,
    private val showFooterLogo: Boolean
) {
    val printer = edcDeviceService.printer
    private val fontSizeMediumBold = 28
    private val variantConfig = AppConfig.current.variantConfig

    private val fmtAddText = Bundle().apply {
        putInt(PrinterConfig.addText.Alignment.BundleName, PrinterConfig.addText.Alignment.LEFT)
        putInt(
            PrinterConfig.addText.FontSize.BundleName,
            PrinterConfig.addText.FontSize.NORMAL_24_24
        )
    }

    private fun mediumCenterText(text: String) =
        MulPrintStrEntity(makeLineText(PrinterUtil.TextItem(text)), FontFamily.MIDDLE).setGravity(
            Gravity.CENTER
        )

    private fun mediumLeftText(text: String) =
        MulPrintStrEntity(makeLineText(PrinterUtil.TextItem(text)), FontFamily.MIDDLE).setGravity(
            Gravity.LEFT
        )

    private fun mediumCenterBoldText(text: String) =
        MulPrintStrEntity(text, fontSizeMediumBold).setGravity(Gravity.CENTER).setIsBold(2)

    private fun create2SectionText(section1: String, section2: String): MulPrintStrEntity =
        MulPrintStrEntity(
            makeLineText(
                PrinterUtil.TextItem(section1).setFont(FontFamily.MIDDLE).setPxSize(152f)
                    .setPaddingAlign(Gravity.FILL_HORIZONTAL),
                PrinterUtil.TextItem(section2).setFont(FontFamily.MIDDLE).setPxSize(180f)
            ),
            FontFamily.MIDDLE
        )

    val format = Bundle().apply {
        putInt(
            PrinterConfig.addText.FontSize.BundleName,
            PrinterConfig.addText.FontSize.NORMAL_24_24
        )
        putInt(PrinterConfig.addText.Alignment.BundleName, PrinterConfig.addText.Alignment.CENTER)
    }

    // non generic code, to be fixed later
    fun printReceipt() = callbackFlow {
        bwLog(TAG, "printCommand: $printCommand")
        bwLog(TAG, "receipt: ${printCommand.receipt}")
        bwLog(
            TAG,
            "category: ${printCommand.orderResponse?.items?.firstOrNull()?.beneficiary?.category}"
        )
        bwLog(TAG, "sku: ${printCommand.orderResponse?.items?.firstOrNull()?.sku}")
        val list: MutableList<MulPrintStrEntity> = mutableListOf()
        if (printCommand.receipt != null) {
            list.addAll(updatePrinterWithCardTrxResponse())
        }
        if (printCommand.orderResponse != null &&
            PaymentAuxilliary.isPpob(
                printCommand.orderResponse?.items?.firstOrNull()?.beneficiary?.category
            )
        ) {
            list.addAll(updatePrinterWithPpobResponse())
        }
        if (printCommand.orderResponse != null &&
            printCommand.orderResponse?.items?.firstOrNull()?.sku.equals(
                PaymentConst.TYPE_PAYMENT_OUT,
                false
            )
        ) {
            list.addAll(updatePrinterWithPaymentOutResponse())
        }

        val printerListener = object : OnPrintListener.Stub() {
            override fun onPrintResult(result: Int) {
                if (result == ServiceResult.Success) {
                    bwLog(
                        TAG,
                        "onFinish print complete receipt ${printCommand.printType} copy ${printCommand.copy?.name}"
                    )
                    val printerResult = PrinterResult(true, ErrorStatus.ERROR_NONE)
                    trySend(EdcResponse.Success(printerResult))
                    return
                } else {
                    bwLog("PrinterListener", "Printer error $result")
                    val printerResult = PrinterResult(false, ErrorStatus.findByMFErrorCode(result))
                    trySend(EdcResponse.Success(printerResult))
                }
            }
        }
        val config = Bundle()
        config.putInt(
            com.morefun.yapi.device.printer.PrinterConfig.COMMON_GRAYLEVEL,
            com.morefun.yapi.device.printer.PrinterConfig.PRINT_DENSITY_NORMAL
        )
        try {
            bwLog(TAG, "List: $list")
            DeviceHelper.getPrinter().printStr(list, printerListener, config)
        } catch (e: Exception) {
            bwLog(e = e)
        } finally {
            awaitClose { bwLog(TAG, "${printCommand.printType} receipt print flow closed") }
        }
    }

    private fun updatePrinterWithPpobResponse(): List<MulPrintStrEntity> {
        val list: MutableList<MulPrintStrEntity> = mutableListOf()
        val order = printCommand.orderResponse
        val item = order?.items?.firstOrNull()

        val amount = order?.paymentCollectionInfo?.paymentCollectionCashTransactionInfo?.amount
        val sellingPrice = when {
            amount.orNil > 0.0 -> amount
            item?.sellingPrice.orNil > 0.0 -> item?.sellingPrice
            else -> order?.amount
        }

        val date = DateTimeUtils.getLocalStringFromUtc(
            order?.createdAt,
            DateTimeUtils.DD_MMM_YYYY_HH_MM
        )

        // Add business name and phone number if available
        if (Utils.getBusinessName()
                .isNotBlank()
        ) {
            list.add(mediumCenterText(Utils.getBusinessName()))
        }
        if (Utils.getPhoneNumber().isNotBlank()) list.add(mediumCenterText(Utils.getPhoneNumber()))

        list.addAll(feedLine(2))
        list.add(create2SectionText("Tanggal", ": ${date.orDash}"))
        list.add(create2SectionText("Kode Pembayaran", ": ${order?.transactionId.orDash}"))
        list.add(create2SectionText("Pelanggan", ": ${item?.details?.customerName.orDash}"))
        list.add(create2SectionText("", ": ${item?.details?.customerNumber.orDash}"))

        list.add(mediumCenterText("-----------------------------------------------------"))

        when (item?.beneficiary?.category.orEmpty()) {
            PpobConst.CATEGORY_PULSA -> {
                list.add(
                    create2SectionText(
                        "Nomor HP",
                        ": ${item?.beneficiary?.phoneNumber.orDash}"
                    )
                )
                list.add(create2SectionText("Pulsa", ": ${item?.name.orDash}"))
                list.add(
                    create2SectionText(
                        "Serial Number",
                        ": ${item?.details?.serialNumber.orDash}"
                    )
                )
            }

            PpobConst.CATEGORY_PAKET_DATA -> {
                list.add(
                    create2SectionText(
                        "Nomor HP",
                        ": ${item?.beneficiary?.phoneNumber.orDash}"
                    )
                )
                list.add(create2SectionText("Paket Data", ": ${item?.name.orDash}"))
                list.add(
                    create2SectionText(
                        "Serial Number",
                        ": ${item?.details?.serialNumber.orDash}"
                    )
                )
            }

            PpobConst.CATEGORY_LISTRIK -> {
                if (item?.beneficiary?.code == PpobConst.CATEGORY_LISTRIK_POSTPAID ||
                    item?.beneficiary?.code == PpobConst.CATEGORY_PLN_POSTPAID
                ) {
                    list.add(
                        create2SectionText(
                            "ID Pelanggan",
                            ": ${item?.beneficiary?.accountNumber.orDash}"
                        )
                    )
                    list.add(create2SectionText("Periode", ": ${item?.details?.periode.orDash}"))
                    list.add(
                        create2SectionText(
                            "Total Lembar Tagihan",
                            ": ${item?.details?.totalLembarTagihan.orDash}"
                        )
                    )
                    list.add(create2SectionText("Tarif/Daya", ": ${item?.details?.tarif.orDash}"))
                    list.add(
                        create2SectionText(
                            "Total Tagihan",
                            ": ${Utils.formatAmount(order?.amount)}"
                        )
                    )
                } else {
                    list.add(create2SectionText("Token Listrik", ": ${item?.name.orDash}"))
                    list.add(
                        create2SectionText(
                            "ID Pelanggan",
                            ": ${item?.beneficiary?.accountNumber.orDash}"
                        )
                    )
                    list.add(
                        create2SectionText(
                            "Nama Pelanggan",
                            ": ${item?.details?.customerName.orDash}"
                        )
                    )
                    list.add(create2SectionText("Total kWh", ": ${item?.details?.totalKwh.orDash}"))
                    list.add(create2SectionText("Tarif/Daya", ": ${item?.details?.tarif.orDash}"))
                    list.add(
                        mediumCenterText("-----------------------------------------------------")
                    )
                    list.add(mediumCenterText("Kode Token"))
                    list.add(mediumCenterBoldText(item?.details?.token.orDash))
                }
            }

            PpobConst.CATEGORY_EWALLET -> {
                list.add(
                    create2SectionText(
                        "Nomor HP",
                        ": ${item?.beneficiary?.phoneNumber.orDash}"
                    )
                )
                list.add(
                    create2SectionText(
                        "Nama Pelanggan",
                        ": ${item?.details?.customerName.orDash}"
                    )
                )
                list.add(create2SectionText("Provider", ": ${item?.details?.billerName.orDash}"))
                list.add(create2SectionText("Nominal Top Up", ": ${item?.name.orDash}"))
                list.add(
                    create2SectionText(
                        "Serial Number",
                        ": ${item?.details?.serialNumber.orDash}"
                    )
                )
            }

            PpobConst.CATEGORY_BPJS -> {
                list.add(
                    create2SectionText(
                        "Nomor Kartu",
                        ": ${item?.details?.customerNumber.orDash}"
                    )
                )
                list.add(
                    create2SectionText(
                        "Nama Pelanggan",
                        ": ${item?.details?.customerName.orDash}"
                    )
                )
                list.add(
                    create2SectionText(
                        "Jumlah Keluarga",
                        ": ${item?.details?.memberCount.orDash}"
                    )
                )
                list.add(create2SectionText("Periode", ": ${item?.details?.period.orDash.orDash}"))
            }

            PpobConst.CATEGORY_INTERNET_DAN_TV_CABLE -> {
                list.add(
                    create2SectionText(
                        "Nama Pelanggan",
                        ": ${item?.details?.customerName.orDash}"
                    )
                )
                list.add(create2SectionText("Provider", ": ${order?.metadata?.billerName.orDash}"))
                list.add(create2SectionText("Periode", ": ${item?.details?.period.orDash}"))
                list.add(
                    create2SectionText(
                        "Total Tagihan",
                        ": ${Utils.formatAmount(order?.amount)}"
                    )
                )
            }

            PpobConst.CATEGORY_PDAM -> {
                list.add(create2SectionText("PDAM", ": ${order?.metadata?.billerName.orDash}"))
                list.add(
                    create2SectionText(
                        "Nomor HP",
                        ": ${item?.beneficiary?.phoneNumber.orDash}"
                    )
                )
                list.add(
                    create2SectionText(
                        "Nama Pelanggan",
                        ": ${item?.details?.customerName.orDash}"
                    )
                )
                list.add(create2SectionText("Periode", ": ${item?.details?.period.orDash}"))
                list.add(
                    create2SectionText(
                        "Total Tagihan",
                        ": ${Utils.formatAmount(order?.amount)}"
                    )
                )
            }

            PpobConst.CATEGORY_MULTIFINANCE -> {
                list.add(
                    create2SectionText(
                        "Nomor Kontak",
                        ": ${item?.details?.customerNumber.orDash}"
                    )
                )
                list.add(
                    create2SectionText(
                        "Nama Pelanggan",
                        ": ${item?.details?.customerName.orDash}"
                    )
                )
                list.add(
                    create2SectionText(
                        "Produk Angsuran",
                        ": ${order?.metadata?.billerName.orDash}"
                    )
                )
                list.add(create2SectionText("Periode", ": ${item?.details?.period.orDash}"))
                list.add(create2SectionText("Denda", ": ${item?.details?.fine.orDash}"))
            }

            PpobConst.CATEGORY_VEHICLE_TAX -> {
                list.add(
                    create2SectionText(
                        "Nama Pelanggan",
                        ": ${item?.details?.customerName.orDash}"
                    )
                )
                list.add(
                    create2SectionText(
                        "Nomor HP",
                        ": ${item?.beneficiary?.phoneNumber.orDash}"
                    )
                )
                list.add(
                    create2SectionText(
                        "Nomor Polisi",
                        ": ${item?.details?.policyNumber.orDash}"
                    )
                )
                list.add(
                    create2SectionText(
                        "Merek Kendaraan",
                        ": ${item?.details?.vehicleBrand.orDash}"
                    )
                )
                list.add(create2SectionText("Periode", ": ${item?.details?.period.orDash}"))
                list.add(
                    create2SectionText(
                        "Jenis Kendaraan",
                        ": ${item?.details?.vehicleName.orDash}"
                    )
                )
                list.add(
                    create2SectionText(
                        "Tipe Kendaraan",
                        ": ${item?.details?.vehicleType.orDash}"
                    )
                )
                list.add(
                    create2SectionText(
                        "Warna Kendaraan",
                        ": ${item?.details?.vehicleColor.orDash}"
                    )
                )
                list.add(
                    create2SectionText(
                        "Tahun Buat Kendaraan",
                        ": ${item?.details?.buildYear.orDash}"
                    )
                )
                list.add(
                    create2SectionText(
                        "Nomor Mesin",
                        ": ${item?.details?.machineNumber.orDash}"
                    )
                )
                list.add(
                    create2SectionText(
                        "Nomor Rangka/NIK/VIN",
                        ": ${item?.details?.frameNumber.orDash}"
                    )
                )
                list.add(create2SectionText("PKB", ": ${item?.details?.pkb.orDash}"))
                list.add(
                    create2SectionText(
                        "Total Tagihan",
                        ": ${Utils.formatAmount(order?.amount)}"
                    )
                )
            }
        }

        list.add(mediumCenterText("-----------------------------------------------------"))
        list.add(mediumCenterText("Total Pembayaran"))
        list.add(mediumCenterBoldText(Utils.formatAmount(sellingPrice)))
        list.add(mediumCenterText("-----------------------------------------------------"))
        list.add(mediumCenterText("Dibuat dengan MiniATM BukuAgen"))
        list.add(mediumCenterText("Bagian dari BukuWarung"))
        list.addAll(feedLine(3))

        return list
    }

    private fun updatePrinterWithPaymentOutResponse(): List<MulPrintStrEntity> {
        val list: MutableList<MulPrintStrEntity> = mutableListOf()
        val order = printCommand.orderResponse
        val item = order?.items?.firstOrNull()

        val total = order?.items?.get(0)?.sellingPrice.orNil + order?.agentFeeInfo?.amount.orNil
        val date = DateTimeUtils.getLocalStringFromUtc(
            order?.createdAt,
            DateTimeUtils.DD_MMM_YYYY_HH_MM
        )

        val businessName =
            Utils.getBusinessNameForSelectedSerialNumber().ifBlank { Utils.getBusinessName() }
        if (businessName.isNotBlank()) list.add(mediumCenterText(businessName))
        if (Utils.getBusinessAddress()
                .isNotBlank()
        ) {
            list.add(mediumCenterText(Utils.getBusinessAddress()))
        }

        list.addAll(feedLine(2))
        list.add(create2SectionText("Tanggal", ": ${date.orDash}"))
        list.add(create2SectionText("Kode Pembayaran", ": ${order?.transactionId.orDash}"))
        list.add(create2SectionText("Nama Pengirim", ": ${Utils.getBusinessName()}"))

        list.add(mediumCenterText("-----------------------------------------------------"))
        list.add(mediumCenterText("Detail Pengirim"))
        list.add(
            mediumCenterBoldText(
                "${order?.payments?.getOrNull(0)?.paymentMethod?.code} - ${Utils.getPhoneNumber()}"
            )
        )
        list.add(mediumCenterText("Rekening Tujuan"))
        list.add(mediumCenterBoldText(item?.beneficiary?.name.orDash))
        list.add(
            mediumCenterBoldText(
                "${item?.beneficiary?.code.orDash} - ${item?.beneficiary?.accountNumber.orDash}"
            )
        )
        list.add(mediumCenterText("Pembayaran Berhasil"))

        if (order?.agentFeeInfo?.amount != 0.0) {
            list.add(mediumCenterText("-----------------------------------------------------"))
            list.add(
                create2SectionText(
                    "Jumlah Pembayaran",
                    ": ${Utils.formatAmount(order?.items?.get(0)?.sellingPrice)}"
                )
            )
            list.add(
                create2SectionText(
                    "Biaya Layanan",
                    ": ${Utils.formatAmount(order?.agentFeeInfo?.amount)}"
                )
            )
        }

        list.add(mediumCenterText("-----------------------------------------------------"))
        list.add(mediumCenterText("Total Pembayaran"))
        list.add(mediumCenterBoldText(Utils.formatAmount(total)))
        list.add(mediumCenterText("-----------------------------------------------------"))
        list.add(mediumCenterText("Dibuat dengan MiniATM BukuAgen"))
        list.add(mediumCenterText("Bagian dari BukuWarung"))
        list.addAll(feedLine(3))

        return list
    }

    private fun feedLine(line: Int): List<MulPrintStrEntity> {
        val list: MutableList<MulPrintStrEntity> = mutableListOf()
        for (i in 0 until line) {
            list.add(mediumLeftText(" "))
        }
        return list
    }

    private fun updatePrinterWithCardTrxResponse(): List<MulPrintStrEntity> {
        val list: MutableList<MulPrintStrEntity> = mutableListOf()
        val receipt = printCommand.receipt

        val headerImageEntity = MulPrintStrEntity()
        headerImageEntity.bitmap = resizeBitmap(headerBitmap, 120, 48)
        headerImageEntity.setMarginX(120)
        headerImageEntity.setGravity(Gravity.CENTER)
        headerImageEntity.setUnderline(true)
        list.add(headerImageEntity)

        if (Utils.getBusinessName()
                .isNotBlank()
        ) {
            list.add(mediumCenterText(Utils.getBusinessName()))
        }
        if (Utils.getBusinessAddress()
                .isNotBlank()
        ) {
            list.add(mediumCenterText(Utils.getBusinessAddress()))
        }

        if (printCommand.destination ==
            CardReceiptViewModel.Destination.TRANSACTION_LISTING_FLOW.name
        ) {
            list.add(mediumCenterText("---RE-PRINT---"))
        }
        list.addAll(feedLine(1))

        val date = receipt?.transactionDate?.replace("/", "-")
        val dateTime =
            if (printCommand.destination ==
                CardReceiptViewModel.Destination.TRANSACTION_LISTING_FLOW.name
            ) {
                DateTimeUtils.getUTCTimeToLocalDateTime(
                    date.orEmpty(),
                    DateTimeUtils.DD_MMM_YYYY_HH_MM
                )
            } else {
                DateTimeUtils.getFormattedLocalDateTime(
                    date.orEmpty(),
                    DateTimeUtils.DD_MMM_YYYY_HH_MM
                )
            }
        list.add(create2SectionText("Waktu", ": $dateTime"))
        val terminalId = if (printCommand.terminalId.isNullOrBlank()) {
            Utils.getTerminalId()
        } else {
            printCommand.terminalId
        }
        val merchantId = printCommand.receipt?.merchantId ?: Utils.getMerchantId()
        list.add(create2SectionText("Terminal ID", ": $terminalId"))
        list.add(create2SectionText("Merchant ID", ": ${merchantId}"))
        list.add(
            create2SectionText(
                "Trace/RC",
                ": ${receipt?.systemTraceAuditNumber}/${receipt?.responseCode.orDefault("00")}"
            )
        )
        list.add(create2SectionText("Ref No", ": ${receipt?.rrn}"))
        list.add(mediumCenterText("- - - - - - - - - - - - - - - - - - - - - - - - - - - - -"))

        // Add account type
        val accountTypeTranslation = when (printCommand.accountType) {
            PaymentConst.TYPE_SAVINGS -> "DEBIT TABUNGAN"
            PaymentConst.TYPE_CHECKING -> "GIRO"
            else -> ""
        }
        list.add(create2SectionText("Jenis Kartu", ": $accountTypeTranslation"))
        val cardEntryMode = if (printCommand.cardEntryMode == CARD_ENTRY_MODE_MAG) {
            "Magnetic"
        } else {
            "Chip"
        }
        list.add(create2SectionText(cardEntryMode, ": ${printCommand.maskedPan}"))

        if (printCommand.printType == TRANSACTION_TYPE_CHECK_BALANCE) {
            list.addAll(feedLine(1))
            list.add(mediumCenterText("Saldo"))
            list.add(
                mediumCenterBoldText(
                    Utils.formatAmount(receipt?.balanceInformation?.balance?.toDouble())
                )
            )
            list.addAll(feedLine(1))
        } else {
            list.add(
                create2SectionText(
                    "Rek. Asal",
                    ": ${receipt?.sourceDetails?.bankName.orEmpty()}-${
                        Utils.maskSensitiveInfo(
                            receipt?.sourceDetails?.name.orEmpty()
                        )
                    }-${
                        Utils.maskSensitiveInfo(receipt?.sourceDetails?.accountNumber.orEmpty())
                    }"
                )
            )
            if (!isCashWithdrawal(receipt)) {
                list.add(
                    create2SectionText(
                        "Rek. Tujuan",
                        ": ${receipt?.destDetails?.name.orEmpty()}-${receipt?.destDetails?.bankName.orEmpty()} - ${receipt?.destDetails?.accountNumber}"
                    )
                )
            }

            if (printCommand.notes.isNotNullOrBlank()) {
                list.add(create2SectionText("Berita", ": ${printCommand.notes}"))
            }

            val status = when (receipt?.endUserStatus) {
                EndUserStatusValues.SUCCESS -> {
                    "Transaksi Berhasil"
                }

                EndUserStatusValues.PENDING_SETTLEMENT -> {
                    "Pencairan Diproses"
                }

                EndUserStatusValues.PENDING_REFRESH -> {
                    if (isCashWithdrawal(receipt)) {
                        "Transaksi Pending"
                    } else {
                        "Transaksi Diproses"
                    }
                }

                EndUserStatusValues.PENDING -> {
                    "Transaksi Pending"
                }

                else -> {
                    ""
                }
            }
            list.addAll(feedLine(1))
            list.add(mediumCenterText(status))
            list.add(mediumCenterBoldText(Utils.formatAmount(receipt?.amount)))
            list.addAll(feedLine(1))

            when (receipt?.endUserStatus) {
                EndUserStatusValues.PENDING_SETTLEMENT -> {
                    if (variantConfig.usesMiniAtmReceiptLogic) {
                        list.add(
                            mediumCenterText(
                                "Pencairan uang diproses. Jangan khawatir, transaksi akan sukses."
                            )
                        )
                    } else {
                        list.add(
                            mediumCenterText(
                                "Dana diterima otomatis dalam 1 hari kerja mengikuti jadwal operasional bank."
                            )
                        )
                    }
                    list.addAll(feedLine(1))
                }

                EndUserStatusValues.PENDING_REFRESH, EndUserStatusValues.PENDING -> {
                    if (variantConfig.usesMiniAtmReceiptLogic) {
                        if (isCashWithdrawal(receipt)) {
                            list.add(
                                mediumCenterText(
                                    "Transaksi Pending. Cek berkala ke Agen untuk mengetahui status akhir transaksi Anda."
                                )
                            )
                        } else {
                            list.add(
                                mediumCenterText(
                                    "Transaksi diproses. Cek berkala ke Agen untuk mengetahui status akhir transaksi Anda."
                                )
                            )
                        }
                    } else {
                        list.add(
                            mediumCenterText(
                                "Status diperbarui otomatis dalam 1 hari kerja mengikuti jadwal operasional bank."
                            )
                        )
                    }
                    list.addAll(feedLine(1))
                }
            }
        }

        list.add(mediumCenterText("-----------------------------------------------------"))

        if (variantConfig.usesMiniAtmReceiptLogic.not()) {
            val footerImageEntity = MulPrintStrEntity()
            if (showFooterLogo) {
                footerImageEntity.bitmap = resizeBitmap(bukuAgenBitmap, 80, 40)
            }
            footerImageEntity.setMarginX(60)
            footerImageEntity.setGravity(Gravity.CENTER)
            footerImageEntity.setUnderline(true)
            list.add(footerImageEntity)
            list.add(mediumCenterText("**${printCommand.copy?.message}**"))
        } else {
            list.addAll(feedLine(1))
        }
        list.add(mediumCenterText("SIMPAN RESI INI SEBAGAI BUKTI TRANSAKSI YANG SAH"))
        list.addAll(feedLine(3))
        return list
    }

    private fun keyValueString(key: String, value: String): String {
        var result = ""
        if (key.length <= 14) {
            result += key
            repeat(14 - key.length) { result += " " }
        } else {
            val firstHalfString = key.split("\n").first()
            if (firstHalfString.length <= 14) {
                result += firstHalfString
                repeat(14 - firstHalfString.length) { result += " " }
            }
        }
        if (value.length > 18) {
            result += value.substring(0, 18)
            result += "\n"
            result += keyValueString(
                key = if (key.length > 14) key.split("\n")[1] else "",
                value = value.substring(18)
            )
        } else {
            result += value
            if (key.length > 14) {
                result += "\n"
                result += key.split("\n")[1]
            }
        }
        return result
    }

    private fun resizeBitmap(original: Bitmap?, width: Int, height: Int): Bitmap? {
        original?.let {
            return Bitmap.createScaledBitmap(original, width, height, true)
        } ?: return null
    }

    private fun isCashWithdrawal(receipt: CardReceiptResponse? = null): Boolean =
        receipt?.transactionType == "CASH_WITHDRAWAL" ||
            receipt?.transactionType == "CASH_WITHDRAWAL_POSTING"

    companion object {
        const val TAG = "EDC_PRINT"
    }
}
