package com.bukuwarung.edc.card.ui.edcdevices.di

import com.bukuwarung.edc.card.ui.edcdevices.api.DeviceListApi
import com.bukuwarung.edc.card.ui.edcdevices.repo.DeviceListRepository
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import retrofit2.Retrofit

@Module
@InstallIn(SingletonComponent::class)
class DeviceModule {

    @Provides
    fun provideDeviceListRepository(deviceListApi: DeviceListApi): DeviceListRepository =
        DeviceListRepository(deviceListApi)

    @Provides
    fun provideDeviceListApi(@Named("normal") retrofit: Retrofit): DeviceListApi =
        retrofit.create(DeviceListApi::class.java)
}
