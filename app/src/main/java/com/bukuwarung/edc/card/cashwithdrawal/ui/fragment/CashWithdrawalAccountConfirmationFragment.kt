package com.bukuwarung.edc.card.cashwithdrawal.ui.fragment

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.bukuwarung.edc.card.cashwithdrawal.model.SettlementBankAccount
import com.bukuwarung.edc.databinding.FragmentCashWithdrawalAccountConfirmationBinding
import com.bukuwarung.edc.homepage.ui.home.HomePageActivity
import com.bukuwarung.edc.util.Utils.getSettlementBankAccountFromJson
import com.bukuwarung.edc.util.Utils.setConfirmCashWithdrawalAccount
import com.bukuwarung.edc.util.goToDestination
import com.bukuwarung.edc.util.singleClick

class CashWithdrawalAccountConfirmationFragment : Fragment() {
    private var _binding: FragmentCashWithdrawalAccountConfirmationBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding =
            FragmentCashWithdrawalAccountConfirmationBinding.inflate(inflater, container, false)
        return binding.root
    }

    companion object {
        fun newInstance(bankAccount: SettlementBankAccount?) =
            CashWithdrawalAccountConfirmationFragment().apply {
                arguments = Bundle().apply {
                    putParcelable("bankAccount", bankAccount)
                }
            }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val settlementBankAccount = getSettlementBankAccountFromJson()
        Log.d("--->settlementBa", settlementBankAccount.toString())
        Log.d(
            "--->argumentse",
            arguments?.getParcelable<SettlementBankAccount>("bankAccount").toString()
        )
        binding.tbCashWithdrawalAccountConfirmation.tvTitle.text = "Tarik Tunai Via Kartu"
        binding.tvAccountNumber.text = settlementBankAccount?.accountNumber
        binding.tvBankDetails.text =
            if (settlementBankAccount?.bankName.isNullOrEmpty()) {
                settlementBankAccount?.beneficiaryName
            } else {
                settlementBankAccount?.bankName.plus(
                    "-"
                ).plus(settlementBankAccount?.beneficiaryName)
            }
        binding.tbCashWithdrawalAccountConfirmation.btnBack.singleClick {
            removeFragment()
            activity?.goToDestination(HomePageActivity::class.java)
        }
        binding.btnConfirm.setOnClickListener {
            setConfirmCashWithdrawalAccount()
            removeFragment()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    private fun removeFragment() {
        parentFragmentManager.beginTransaction().remove(this).commit()
    }
}
