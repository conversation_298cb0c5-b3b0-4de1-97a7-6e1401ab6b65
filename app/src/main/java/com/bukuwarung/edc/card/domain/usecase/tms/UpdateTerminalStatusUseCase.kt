package com.bukuwarung.edc.card.domain.usecase.tms

import com.bukuwarung.edc.card.data.model.Terminal
import com.bukuwarung.edc.card.data.repository.TerminalManagementRepository
import javax.inject.Inject

class UpdateTerminalStatusUseCase @Inject constructor(
    private val repository: TerminalManagementRepository
) {
    suspend operator fun invoke(
        serialNumber: String,
        status: String,
        appDeviceId: String
    ): Terminal {
        val response = repository.updateTerminalStatus(serialNumber, status, appDeviceId)
        if (response.isSuccessful) {
            return response.body()?.data as Terminal
        } else {
            throw Exception("Failed to update terminal status: ${response.message()}")
        }
    }
}
