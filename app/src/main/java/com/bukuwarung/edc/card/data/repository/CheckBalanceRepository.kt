package com.bukuwarung.edc.card.data.repository

import com.bukuwarung.edc.card.data.datasource.CheckBalanceDataSource
import com.bukuwarung.edc.card.data.model.CheckBalanceRequest
import javax.inject.Inject

class CheckBalanceRepository @Inject constructor(
    private val checkBalanceDataSource: CheckBalanceDataSource
) {

    suspend fun fetchCardBalance(
        serialNumber: String,
        accountId: String,
        checkBalanceRequest: CheckBalanceRequest
    ) = checkBalanceDataSource.fetchCardBalance(serialNumber, accountId, checkBalanceRequest)
}
