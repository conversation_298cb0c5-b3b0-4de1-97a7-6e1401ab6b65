package com.bukuwarung.edc.card

import Resource
import android.annotation.SuppressLint
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.RemoteException
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import androidx.activity.OnBackPressedCallback
import androidx.activity.viewModels
import com.bukuwarung.bluetooth_printer.BluetoothDevices
import com.bukuwarung.cardreader.CardReaderHelper
import com.bukuwarung.cardreader.contant.CardConstants
import com.bukuwarung.cardreader.contant.CardConstants.EmvIntentConst.KEY_AUTH_AMOUNT_STRING
import com.bukuwarung.cardreader.contant.CardConstants.EmvIntentConst.KEY_FORCE_ONLINE_BOOLEAN
import com.bukuwarung.cardreader.contant.CardConstants.EmvIntentConst.KEY_TRANSACTION_TIMEOUT_BYTE
import com.bukuwarung.cardreader.contant.CardConstants.EmvIntentConst.KEY_TRANS_PROCESS_CODE_BYTE
import com.bukuwarung.cardreader.contant.CardConstants.ResultBundleConst.KEY_CARD_ENTRY_MODE_INT
import com.bukuwarung.cardreader.contant.CardConstants.ResultBundleConst.KEY_PAN_STRING
import com.bukuwarung.cardreader.contant.CardConstants.ResultBundleConst.KEY_PIN_BLOCK
import com.bukuwarung.cardreader.dto.CardReaderResult
import com.bukuwarung.cardreader.tianyu.contant.ResponseCode
import com.bukuwarung.edc.R
import com.bukuwarung.edc.ZohoChat
import com.bukuwarung.edc.ZohoChatEntryPoint
import com.bukuwarung.edc.app.config.VariantConfig
import com.bukuwarung.edc.card.constant.CardAnalyticsConstants
import com.bukuwarung.edc.card.constant.PrintConst
import com.bukuwarung.edc.card.data.model.CardReceiptResponse
import com.bukuwarung.edc.card.data.model.CheckBalanceRequest
import com.bukuwarung.edc.card.data.model.PinCardErrorResponse
import com.bukuwarung.edc.card.domain.model.consts.Constants
import com.bukuwarung.edc.card.transfermoney.constants.CheckBalancePinConfig.IS_FROM_FIREBASE
import com.bukuwarung.edc.card.transfermoney.model.BankDetails
import com.bukuwarung.edc.card.transfermoney.model.TransferMoneyData
import com.bukuwarung.edc.card.transfermoney.model.TransferMoneyRequestResponseBody
import com.bukuwarung.edc.card.transfermoney.ui.ConfirmTransferActivity
import com.bukuwarung.edc.card.ui.CheckCardViewModel
import com.bukuwarung.edc.card.ui.ErrorMapping
import com.bukuwarung.edc.card.ui.ErrorMapping.bcaNotSupported
import com.bukuwarung.edc.card.ui.ErrorMapping.invalidMerchant
import com.bukuwarung.edc.card.ui.ErrorMapping.terminalIdOrDeviceSerialBlockedCodes
import com.bukuwarung.edc.card.ui.checkbalance.BalanceInfoActivity
import com.bukuwarung.edc.databinding.ActivityExternalPinpadBinding
import com.bukuwarung.edc.databinding.LayoutOverlayBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.global.base.BaseCardActivity
import com.bukuwarung.edc.global.enums.TransactionType
import com.bukuwarung.edc.global.enums.toZohoEntryPoint
import com.bukuwarung.edc.global.network.Status
import com.bukuwarung.edc.homepage.constant.HomePageAnalyticsConstants.SLEEP_MODE
import com.bukuwarung.edc.homepage.ui.home.HomePageActivity
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.payments.data.model.CardErrorType
import com.bukuwarung.edc.payments.ui.addbank.SelectBankActivity.Companion.IS_MONEY_TRANSFER
import com.bukuwarung.edc.payments.ui.dialog.CardErrorDialog
import com.bukuwarung.edc.util.KycUtil
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.Utils.bwLog
import com.bukuwarung.edc.util.get
import com.bukuwarung.edc.util.goToDestination
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.orDefault
import com.bukuwarung.edc.util.put
import com.bukuwarung.edc.util.showView
import com.vfi.smartpos.deviceservice.constdefine.ConstPBOCHandler.onConfirmCardInfo.info.KEY_EXPIRED_DATE_String
import dagger.hilt.android.AndroidEntryPoint
import java.util.Locale.getDefault
import javax.inject.Inject

@AndroidEntryPoint
class ExternalPinpadActivity : BaseCardActivity() {

    private lateinit var binding: ActivityExternalPinpadBinding
    private lateinit var overlayLayout: LayoutOverlayBinding

    private var isFromFirebase: Boolean = false
    private var firstTimePinInvalidError = true
    private var isExternalPinPad: Boolean = true
    private var isEligibleToSendRequest: Boolean = true
    private var isExceededInvalidPinAttempt = false
    private var isErrorScreenShown = false
    private var hasPinad = false

    private var accountType = ""
    private var cardNumber = ""
    private var iccVal = ""
    private var cardExpiry = ""
    private var pinBlock = ""

    private var cardEntryMode = Constants.CARD_ENTRY_MODE_IC
    private var cardErrorDialog: CardErrorDialog? = null

    private val cardViewModel: CheckCardViewModel by viewModels()

    private val isMoneyTransfer by lazy {
        intent?.getBooleanExtra(IS_MONEY_TRANSFER, false) ?: false
    }
    private val transactionType by lazy {
        bundle?.getString(Constant.INTENT_KEY_TRANSACTION_TYPE).orEmpty()
    }
    private val bankAndAmountDetails by lazy {
        intent?.getParcelableExtra("BANK_AMOUNT_DETAILS") as? TransferMoneyData
    }
    private val bundle by lazy { intent?.getBundleExtra(DATA) }

    private val pinCardError by lazy {
        intent?.getParcelableExtra("PIN_CARD_ERROR") as? PinCardErrorResponse
    }

    private val handler = Handler(Looper.getMainLooper())

    private val inActiveRunnable = Runnable {
        Log.d("Error", "Inactive for 60 sec")
    }

    @Inject
    lateinit var variantConfig: VariantConfig

    companion object {
        // TODO: need to delete this if not used
        const val MAX_PIN_LENGTH = 6
        const val KEYBOARD_ANIMATION_DURATION = 600L
        const val ACCOUNT_TYPE = "account_type"
        const val API_RESPONSE = "api_response"
        const val DATA = "data"
        const val PAN = "PAN"
        const val INACTIVE_TIMER = 100 * 1000L
        const val ERROR_NO_ACTION_TIMER = 5 * 1000L // TODO: need to delete this if not used
        val inValidAttemptErrorCode = arrayListOf("1", "01", "38", "57", "75")
        const val PIN_INVALID_ERROR_CODE = "55"
        private val BALANCE_CHECK_EMV = "BALANCE_CHECK_EMV"
    }

    init {
        isFromFirebase = Utils.sharedPreferences.get(IS_FROM_FIREBASE, false)
        firstTimePinInvalidError = Utils.sharedPreferences.get(Utils.FIRST_TIME_INVALID_PIN, true)
        isExternalPinPad = true
    }

    override fun setViewBinding() {
        binding = ActivityExternalPinpadBinding.inflate(layoutInflater)
        overlayLayout = LayoutOverlayBinding.bind(binding.root)
        setContentView(binding.root)
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun setupView() {
        binding.layoutExtPinpad.root.showView()
        onBackPressedDispatcher.addCallback(object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                trackBackPressEvent(CardAnalyticsConstants.ANDROID_BACK_BUTTON)
                if (isExceededInvalidPinAttempt) {
                    checkCardAndShowDialog()
                } else {
                    isEnabled = false
                    onBackPressed()
                }
            }
        })
        setupToolbar()

        if (pinCardError?.code?.isNotBlank() == true) {
            hasPinad = true
            cardViewModel.updateTransferMoneyInquire(
                Resource.error(
                    pinCardError?.message.orEmpty(),
                    pinCardError
                )
            )
        }
        binding.secureInfoHeading.text = getString(R.string.dont_share_your_pin)
        binding.secureInfoBody.text = getString(R.string.keep_your_pin_secret)
    }

    private fun initPinpad() {
        try {
            if (!hasPinad) {
                hasPinad = true
                startTransaction()
            }
        } catch (e: RemoteException) {
            e.printStackTrace()
        }
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        if (hasFocus && !hasPinad) {
            initPinpad()
        }
    }

    private fun startTransaction() {
        if (CardReaderHelper.getInstance() != null) {
            if (!CardReaderHelper.getInstance().isDeviceConnected()) {
                showCardReaderInSleepmodeError()
                return
            }
            startCardReadingThread()
        } else {
            showCardReaderDisconnectedError()
        }
    }

    // Starts a new thread to handle card reading
    private fun startCardReadingThread() {
        object : Thread() {
            override fun run() {
                try {
                    name = "CardThread"
                    val emvIntent = Bundle()
                    emvIntent.putString(
                        KEY_AUTH_AMOUNT_STRING,
                        if (isMoneyTransfer) {
                            bankAndAmountDetails?.amount?.toString() + "00"
                        } else {
                            "0"
                        }
                    )
                    emvIntent.putByte(
                        KEY_TRANS_PROCESS_CODE_BYTE,
                        if (isMoneyTransfer) 0x40.toByte() else 0x30.toByte()
                    )
                    emvIntent.putByte(KEY_TRANSACTION_TIMEOUT_BYTE, 0x80.toByte())
                    emvIntent.putBoolean(KEY_FORCE_ONLINE_BOOLEAN, true)

                    trackCardReadingEvent(CardAnalyticsConstants.EVENT_CARD_READING_START, null)
                    val result: CardReaderResult =
                        CardReaderHelper.getInstance().readCard(emvIntent)
                    trackCardReadingEvent(
                        CardAnalyticsConstants.EVENT_CARD_READING_COMPLETED,
                        result
                    )
                    if (result != null && result.response == ResponseCode.SUCCESS) {
                        pinBlock = result.data.getString(KEY_PIN_BLOCK) ?: ""
                        bundle?.let { Utils.copyBundle(result.data, it) }
                        when {
                            Utils.hasEmptyPin(pinBlock) -> {
                                runOnUiThread {
                                    handlePinLengthError()
                                }
                            }

                            Utils.isTransactionCancelled(pinBlock) -> {
                                runOnUiThread {
                                    handleCancelTransaction()
                                }
                            }

                            else -> {
                                if (pinBlock.isEmpty()) {
                                    runOnUiThread {
                                        handlePinLengthError()
                                    }
                                } else {
                                    runOnUiThread {
                                        afterKeyboardEnteredPressed(pinBlock)
                                    }
                                }
                            }
                        }
                    } else if (result.response == ResponseCode.ERROR_CANCEL) {
                        runOnUiThread {
                            handleCancelTransaction()
                        }
                    } else if (result.response == ResponseCode.ERROR_PIN_LENGTH) {
                        runOnUiThread {
                            handlePinLengthError()
                        }
                    } else if (result.response == ResponseCode.ERROR_READING_CARD) {
                        runOnUiThread {
                            handleErrorInCardReading()
                        }
                    } else if (result.response == ResponseCode.ERROR_TIMEOUT) {
                        runOnUiThread {
                            handlePinTimeout()
                        }
                        Log.d("card_reader", "timeout")
                    } else {
                        runOnUiThread {
                            try {
                                val props = HashMap<String, String>()
                                props["error_code"] =
                                    result.data.getString("error").orDefault("unknown_code")
                                Analytics.trackEventMobile("error_reading_card", props)
                            } catch (e: Exception) {
                                e.printStackTrace()
                            }
                            handleUnknownError(result.data.getString("error"))
                        }
                    }
                } catch (e: Exception) {
                    bwLog(e = e)
                    runOnUiThread {
                        if (!isFinishing && !isDestroyed) {
                            showCardReaderInSleepmodeError()
                        }
                    }
                }
            }
        }.start()
    }

    private fun trackCardReadingEvent(eventName: String, result: CardReaderResult?) {
        try {
            val map = HashMap<String, String>()
            map[CardAnalyticsConstants.TRANSACTION_TYPE] =
                Utils.getTransactionTypeAnalytics(transactionType)

            if (eventName == CardAnalyticsConstants.EVENT_CARD_READING_START) {
                Analytics.trackEvent(CardAnalyticsConstants.EVENT_CARD_READING_START, map)
            } else if (eventName == CardAnalyticsConstants.EVENT_CARD_READING_COMPLETED) {
                try {
                    cardEntryMode =
                        result?.data?.getInt(
                            CardConstants.ResultBundleConst.KEY_CARD_ENTRY_MODE_INT
                        )!!
                    map[CardAnalyticsConstants.CARD_TYPE] = cardEntryMode.toString()
                } catch (e: Exception) {
                    e.printStackTrace()
                }
                map[CardAnalyticsConstants.CARD_READING_STATUS] =
                    if (result?.response ==
                        ResponseCode.SUCCESS
                    ) {
                        CardAnalyticsConstants.SUCCESS
                    } else {
                        CardAnalyticsConstants.FAIL
                    }

                if (result?.response != ResponseCode.SUCCESS) {
                    map[CardAnalyticsConstants.FAIL_REASON] =
                        result?.data?.getString("error").orDefault("unknown_code")
                }
                map["response"] = result?.response?.name ?: ""

                try {
                    map["type_card"] = "${result?.data?.getInt(KEY_CARD_ENTRY_MODE_INT)}"
                    val prefix = result?.data?.getString(KEY_PAN_STRING)
                        ?.let { if (it.length >= 6) it.substring(0, 6) else it }
                    map["prefix"] = "$prefix"
                } catch (e: Exception) {
                    e.printStackTrace()
                }
                Analytics.trackEvent(CardAnalyticsConstants.EVENT_CARD_READING_COMPLETED, map)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun showCardReaderDisconnectedError() {
        val dialog = CardErrorDialog(
            context = this,
            errorType = CardErrorType.BLUETOOTH_DEVICE_SWITCHED_OFF
        ) {
            startTransaction()
        }
        Utils.showDialogIfActivityAlive(this, dialog)
    }

    private fun showCardReaderInSleepmodeError() {
        Analytics.trackEventMobile(SLEEP_MODE)
        val dialog = CardErrorDialog(
            context = this,
            errorType = CardErrorType.BLUETOOTH_DEVICE_IN_SLEEPMODE
        ) {
            BluetoothDevices.getPairedCardReader()?.let {
                CardReaderHelper.getInstance().connectToDevice(it)
            }
            startTransaction()
        }
        Utils.showDialogIfActivityAlive(this, dialog)
    }

    private fun afterKeyboardEnteredPressed(pinBlockVal: String) {
        val map = HashMap<String, String>()
        map[CardAnalyticsConstants.TRANSACTION_TYPE] =
            Utils.getTransactionTypeAnalytics(transactionType)
        Analytics.trackEvent(
            CardAnalyticsConstants.EVENT_PIN_VERIFICATION_REQUEST,
            map
        )
        overlayLayout.progressOverlay.showView()
        stopTimer()
        accountType = bundle?.getString(ACCOUNT_TYPE).orDefault(PaymentConst.TYPE_SAVINGS)
        cardNumber = bundle?.getString(PAN).orEmpty()
        iccVal = bundle?.getString("icData").orEmpty()
        cardExpiry = bundle?.getString(KEY_EXPIRED_DATE_String).orEmpty()
        bundle?.putString("PIN_BLOCK", pinBlock)

        if (isMoneyTransfer) {
            startTransferInquiry(pinBlock)
        } else {
            doBalanceCheck()
        }
    }

    private fun startTransferInquiry(pinBlock: String) {
        Log.d("TRANSFER_INQUIRY", "cardNumber = $cardNumber")

        val posEntryMode = if (cardEntryMode == Constants.CARD_ENTRY_MODE_MAG) {
            Constants.POS_ENTRY_MODE_MAG_CARD
        } else {
            Constants.POS_ENTRY_MODE_IC_CARD
        }
        val track2Data = bundle?.getString("TRACK2")?.let { Utils.getTrack2(it) }
        bundle?.putString("bank_code", bankAndAmountDetails?.bank?.bankCode)

        val transferMoneyRequest = TransferMoneyRequestResponseBody(
            track2Data = track2Data,
            accountType = accountType,
            cardNumber = cardNumber,
            terminalId = Utils.getTerminalId(),
            destDetails = BankDetails(
                accountNumber = bankAndAmountDetails?.accountNumber,
                bankName = bankAndAmountDetails?.bank?.bankName,
                bankCode = bankAndAmountDetails?.bank?.bankCode
            ),
            iccData = "",
            invoiceNumber = "",
            amount = bankAndAmountDetails?.amount?.toString(),
            notes = bankAndAmountDetails?.optionalText,
            posEntryMode = posEntryMode,
            cardExpiry = cardExpiry,
            pinBlock = pinBlock
        )
        cardViewModel.inquireMoneyTransfer(
            TransactionType.valueOf(transactionType),
            transferMoneyRequest
        )
    }

    override fun subscribeState() {
        cardViewModel.cardBalance.observe(this) {
            when (it.status) {
                Status.SUCCESS -> {
                    overlayLayout.root.hideView()
                    binding.layoutExtPinpad.tvPinpadError.hideView()
                    Utils.sharedPreferences.put(Utils.FIRST_TIME_INVALID_PIN, true)
                    finish()
                    openActivity(BalanceInfoActivity::class.java) {
                        val response = it.data as? CardReceiptResponse
                        putParcelable(API_RESPONSE, response)
                        putBundle(DATA, bundle)
                    }
                }

                Status.ERROR -> {
                    isEligibleToSendRequest = true
                    overlayLayout.progressOverlay.hideView()
                    val pinCardError = it.data as? PinCardErrorResponse
                    handlePinCardError(pinCardError, PrintConst.TRANSACTION_TYPE_CHECK_BALANCE)
                }

                Status.LOADING -> {
                    overlayLayout.progressOverlay.showView()
                    binding.layoutExtPinpad.tvPinpadError.hideView()
                }

                Status.NO_INTERNET -> {
                    overlayLayout.progressOverlay.hideView()
                    binding.layoutExtPinpad.tvPinpadError.hideView()
                }
            }
        }
        cardViewModel.transferMoneyInquire.observe(this) {
            when (it.status) {
                Status.SUCCESS -> {
                    overlayLayout.progressOverlay.hideView()
                    binding.layoutExtPinpad.tvPinpadError.hideView()
                    val data = it.data as? TransferMoneyRequestResponseBody
                    finish()
                    openActivity(ConfirmTransferActivity::class.java) {
                        data?.notes = bankAndAmountDetails?.optionalText.orEmpty()
                        // Need to pass accountType, track2Data, posEntryMode, pinBlock,iccData
                        putParcelable(API_RESPONSE, data)
                        bundle?.putParcelable("BANK_AMOUNT_DETAILS", bankAndAmountDetails)
                        bundle?.putBoolean(IS_MONEY_TRANSFER, isMoneyTransfer)
                        putBundle(DATA, bundle)
                    }
                }

                Status.ERROR -> {
                    isEligibleToSendRequest = true
                    overlayLayout.progressOverlay.hideView()
                    val pinCardError = it.data as? PinCardErrorResponse
                    handlePinCardError(pinCardError, PrintConst.TRANSACTION_TYPE_TRANSFER)
                    try {
                        val status: Boolean =
                            CardReaderHelper.getInstance()
                                ?.confirmTransaction("Transaksi Gagal") == true
                        Log.d("TRANSFER_INQUIRY", "Transaction Failed: $status")
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }

                Status.LOADING -> {
                    overlayLayout.progressOverlay.showView()
                    binding.layoutExtPinpad.tvPinpadError.hideView()
                }

                Status.NO_INTERNET -> {
                    overlayLayout.progressOverlay.hideView()
                    binding.layoutExtPinpad.tvPinpadError.hideView()
                }
            }
        }
    }

    private fun handlePinCardError(pinCardError: PinCardErrorResponse?, transactionType: Int) {
        Utils.clearIncompleteTransaction()
        val errorCode = pinCardError?.code
        firstTimePinInvalidError = Utils.sharedPreferences.get(Utils.FIRST_TIME_INVALID_PIN, true)

        when {
            errorCode?.equals(PIN_INVALID_ERROR_CODE).isTrue && firstTimePinInvalidError -> {
                Utils.sharedPreferences.put(Utils.FIRST_TIME_INVALID_PIN, false)
                showInCorrectPinError(
                    errorCode,
                    CardErrorType.INCORRECT_PIN_FIRST_ATTEMPT,
                    transactionType,
                    pinCardError?.stan
                )
                initPinpad()
            }

            terminalIdOrDeviceSerialBlockedCodes.contains(errorCode) -> {
                cardErrorDialog = ErrorMapping.showErrorDialog(
                    context = this,
                    errorCode = errorCode,
                    transactionType = transactionType,
                    stan = pinCardError?.stan,
                    positiveListener = {
                        if (terminalIdOrDeviceSerialBlockedCodes.contains(errorCode)) {
                            goToDestination(HomePageActivity::class.java)
                            ZohoChat.openZohoChat(
                                ZohoChatEntryPoint.TERMINAL_ID_OR_DEVICE_SERIAL_BLOCKED
                            )
                        }
                    },
                    dismissListener = {
                        if (terminalIdOrDeviceSerialBlockedCodes.contains(errorCode)) {
                            goToDestination(HomePageActivity::class.java)
                        }
                    }
                )
                cardErrorDialog?.show()
            }

            inValidAttemptErrorCode.contains(pinCardError?.code) -> {
                isExceededInvalidPinAttempt = true
                showInCorrectPinError(
                    errorCode,
                    CardErrorType.CARD_BLOCKED,
                    transactionType,
                    pinCardError?.stan
                )
            }

            else -> {
                stopTimer()
                edcCardViewModel.stopCheckCard()
                pinCardError?.code?.let { errorCode ->
                    showInCorrectPinError(
                        errorCode,
                        ErrorMapping.returnErrorType(errorCode),
                        transactionType,
                        pinCardError?.stan
                    )
                }
            }
        }
    }

    private fun showInCorrectPinError(
        errorCode: String?,
        errorType: CardErrorType,
        transactionType: Int,
        stan: String?
    ) {
        isErrorScreenShown = true
        if (cardErrorDialog != null && cardErrorDialog!!.isShowing) {
            cardErrorDialog?.dismiss()
            cardErrorDialog = null
        }
        cardErrorDialog = CardErrorDialog(
            context = this,
            errorCode = errorCode,
            transactionType = transactionType,
            errorType = errorType,
            stan = stan
        ) {
            if (errorType == CardErrorType.INCORRECT_PIN_FIRST_ATTEMPT) {
                startTransaction()
            } else {
                checkCardAndShowDialog()
                if (invalidMerchant.contains(errorCode)) {
                    KycUtil.navigateToKycFlow(this)
                } else if (bcaNotSupported.contains(errorCode)) {
                    goToDestination(HomePageActivity::class.java)
                }
            }
        }
        Utils.showDialogIfActivityAlive(this, cardErrorDialog)
    }

    private fun checkCardAndShowDialog() {
        checkForCardRemove(this)
    }

    override fun onPause() {
        super.onPause()
        stopTimer()
    }

    override fun onResume() {
        super.onResume()
        startTimer()
    }

    private fun startTimer() {
        handler.postDelayed(inActiveRunnable, INACTIVE_TIMER)
    }

    // TODO: need to delete this if not used
    private fun resetTimer() {
        stopTimer()
        startTimer()
    }

    private fun stopTimer() {
        handler.removeCallbacks(inActiveRunnable)
    }

    private fun doBalanceCheck() {
        Log.d(BALANCE_CHECK_EMV, "EMV_CALLBACK_REQUEST_ONLINE")
        val track2Data = bundle?.getString("TRACK2")?.let { Utils.getTrack2(it) }
        val posEntryMode =
            if (cardEntryMode ==
                Constants.CARD_ENTRY_MODE_MAG
            ) {
                Constants.POS_ENTRY_MODE_MAG_CARD
            } else {
                Constants.POS_ENTRY_MODE_IC_CARD
            }
        val checkBalanceRequest = CheckBalanceRequest(
            accountType = accountType,
            cardExpiry = cardExpiry,
            cardNumber = cardNumber,
            terminalId = Utils.getTerminalId(),
            pinBlock = pinBlock,
            iccData = iccVal,
            posEntryMode = posEntryMode,
            track2Data = track2Data
        )
        /*
         * transactions are incomplete unless backend iccData is verified by device
         * keep transaction for reversal in case of device shutdown/damaged iccdata
         */
        Utils.setIncompleteBalanceCheckTransaction(checkBalanceRequest)
        cardViewModel.fetchCardBalance(checkBalanceRequest)
    }

    override fun onCardRemove() {
        goToDestination(HomePageActivity::class.java)
    }

    private fun trackBackPressEvent(buttonType: String) {
        val eventProperties = HashMap<String, String>()
        eventProperties[CardAnalyticsConstants.BUTTON] = buttonType
        eventProperties[CardAnalyticsConstants.TRANSACTION_TYPE] =
            Utils.getTransactionTypeAnalytics(transactionType)
        eventProperties[CardAnalyticsConstants.PAGE] = CardAnalyticsConstants.CARD_PIN_SCREEN
        Analytics.trackEvent(CardAnalyticsConstants.EVENT_BACK_BUTTON_CLICK, eventProperties)
    }

    private fun handleErrorInCardReading() {
        val dialog = CardErrorDialog(this, CardErrorType.UNABLE_TO_READ) { errorDialog ->
            startTransaction()
        }
        Utils.showDialogIfActivityAlive(this, dialog)
    }

    private fun handlePinTimeout() {
        val map = HashMap<String, String>()
        map[CardAnalyticsConstants.TRANSACTION_TYPE] =
            Utils.getTransactionTypeAnalytics(transactionType)
        map[CardAnalyticsConstants.PAGE] = CardAnalyticsConstants.CARD_PIN_SCREEN
        Analytics.trackEvent(CardAnalyticsConstants.EVENT_TIMEOUT, map)
        if (cardErrorDialog != null && cardErrorDialog!!.isShowing) {
            cardErrorDialog?.dismiss()
            cardErrorDialog = null
        }
        cardErrorDialog = ErrorMapping.showErrorDialog(
            context = this,
            errorCode = Constants.CARD_TRANSACTION_TIMEOUT
        ) {
            checkCardAndShowDialog()
        }
        Utils.showDialogIfActivityAlive(this, cardErrorDialog)
        Log.d("Error", "Inactive for 60 sec")
    }

    private fun handleCancelTransaction() {
        val transactionCancelledDialog: CardErrorDialog = ErrorMapping.showErrorDialog(
            context = this,
            errorCode = Constants.CARD_TRANSACTION_CANCEL
        ) {
            checkCardAndShowDialog()
        }
        Utils.showDialogIfActivityAlive(this, transactionCancelledDialog)
        val status: Boolean =
            CardReaderHelper.getInstance()?.confirmTransaction("Transaction cancelled") == true
        Log.d("EXTERNAL_PINPAD", "Transaction cancelled: $status")
        Log.d("Error", "transaction cancelled")
    }

    private fun handlePinLengthError() {
        // PIN yang Anda masukkan kurang dari 6 digit. Silakan coba lagi.
        val errorDialog = CardErrorDialog(
            context = this,
            errorType = CardErrorType.INVALID_PIN_LENGTH
        ) {
            startCardReadingThread()
        }
        Utils.showDialogIfActivityAlive(this, errorDialog)
        Log.d("EXTERNAL_PINPAD", "Transaction pin length wrong")
        Log.d("Error", "transaction pin length")
    }

    private fun handleUnknownError(errorCode: String?) {
        val errorDialog = CardErrorDialog(
            context = this,
            errorType = CardErrorType.UNABLE_TO_READ,
            errorCode = "E0" + errorCode?.uppercase(getDefault())
        ) {
            Utils.setLoadedConfig(false)
            goToDestination(HomePageActivity::class.java)
        }
        Utils.showDialogIfActivityAlive(this, errorDialog)
        Log.d("EXTERNAL_PINPAD", "errorCode: $errorCode")
    }

    override fun onBackPressed() {
    }

    private fun setupToolbar() {
        binding.toolbar.apply {
            tvTitle.text = getTitleByTransactionType(transactionType)
            btnBack.hideView()

            setSupportActionBar(widgetToolbar)
            supportActionBar?.setDisplayShowTitleEnabled(false)
        }
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        if (variantConfig.shouldShowExternalPinpadHelpButton) {
            menuInflater.inflate(R.menu.menu_help, menu)
        }
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == R.id.action_help) {
            ZohoChat.openZohoChat(transactionType.toZohoEntryPoint())
            return true
        }
        return super.onOptionsItemSelected(item)
    }
}
