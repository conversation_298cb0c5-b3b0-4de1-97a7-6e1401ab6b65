package com.bukuwarung.edc.card.ui

import android.content.Context
import android.nfc.NfcAdapter
import android.os.Build
import android.os.Bundle
import android.os.CountDownTimer
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.edc.card.data.model.PinpadButtonCoordinates
import com.bukuwarung.edc.card.domain.model.CardReaderResult
import com.bukuwarung.edc.card.domain.model.CheckCardResult
import com.bukuwarung.edc.card.domain.model.EdcResponse
import com.bukuwarung.edc.card.domain.model.PinpadResult
import com.bukuwarung.edc.card.domain.model.consts.Constants
import com.bukuwarung.edc.card.domain.model.consts.Constants.CARD_ENTRY_MODE_NONE
import com.bukuwarung.edc.card.domain.usecase.EdcDeviceUseCase
import com.bukuwarung.edc.device.DeviceHelper
import com.bukuwarung.edc.global.enums.TransactionType
import com.bukuwarung.edc.settings.ui.setting.MiniatmDebugSettingActivity
import com.bukuwarung.edc.util.NfcUtils
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.Utils.bwLog
import com.bukuwarung.edc.util.isNotNullOrEmpty
import com.vfi.smartpos.deviceservice.constdefine.ConstIPBOC
import com.vfi.smartpos.deviceservice.constdefine.ConstPBOCHandler.onConfirmCardInfo.info.KEY_EXPIRED_DATE_String
import com.vfi.smartpos.deviceservice.constdefine.ConstPBOCHandler.onConfirmCardInfo.info.KEY_PAN_String
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import kotlinx.coroutines.launch

@HiltViewModel
class EdcCardViewModel @Inject constructor(
    @ApplicationContext private val applicationContext: Context,
    private val deviceUseCase: EdcDeviceUseCase
) : ViewModel() {

    val tag = "EDC_CARD_VIEW_MODEL"

    private val _checkCardResult = MutableLiveData<CheckCardResult>()

    val checkCardResult: LiveData<CheckCardResult>
        get() = _checkCardResult

    private val _cardReaderResult = MutableLiveData<CardReaderResult>()

    private val _cardAvailable = MutableLiveData<Boolean>()

    val cardReaderResult: LiveData<CardReaderResult>
        get() = _cardReaderResult

    private val _emvCallbackResult = MutableLiveData<CardReaderResult>()

    val emvCallbackResult: LiveData<CardReaderResult>
        get() = _emvCallbackResult

    private val _pinpadResult = MutableLiveData<PinpadResult>()

    val pinpadResult: LiveData<PinpadResult>
        get() = _pinpadResult

    val cardAvailable: LiveData<Boolean>
        get() = _cardAvailable

    sealed class Event {
        data class OnStartCheckCard(val timeout: Int, val showError: Boolean = false) : Event()
        data class OnStartReadCard(val timeout: Int) : Event()
        data class OnStartEmvTransaction(val emvParams: Bundle, val timeout: Int) : Event()
        data class SimulateInsertCard(val duration: Long) : Event()
        data class OnCheckForCardRemove(val duration: Long) : Event()
        data class OnImportPin(val pinBlock: String) : Event()
        data class OnCardReadConfirm(val isConfirm: Boolean) : Event()
    }

    fun onEventReceived(event: Event) {
        when (event) {
            is Event.OnStartCheckCard -> startCheckCard(event.timeout, event.showError)
            is Event.OnStartReadCard -> startReadCard(event.timeout)
            is Event.SimulateInsertCard -> simulateInsertCard(event.duration)
            is Event.OnCardReadConfirm -> confirmCard(event.isConfirm)
            is Event.OnImportPin -> importPin(event.pinBlock)
            is Event.OnCheckForCardRemove -> checkForCardRemove(event.duration)
            is Event.OnStartEmvTransaction -> startEmvForTransaction(event.timeout, event.emvParams)
        }
    }

    fun stopCheckCard() = viewModelScope.launch {
        deviceUseCase.stopCheckCard()
    }

    fun initPinpad(cardNumber: String?, pinpadButtonCoordinates: List<PinpadButtonCoordinates>) =
        viewModelScope.launch {
//        Log.d(TAG,"wait for card remove")

            deviceUseCase.initPinpad(cardNumber!!, pinpadButtonCoordinates).collect { response ->
//            Log.d(TAG,"initPinpad response $response")
                _pinpadResult.postValue(
                    response
                )
            }
        }

    private fun checkForCardRemove(timeout: Long) = viewModelScope.launch {
        Log.d(tag, "wait for card remove")
        deviceUseCase.checkCardAvailability(timeout).collect { response ->
//            Log.d(TAG,"card availability response $response")
            when (response) {
                is EdcResponse.CardAvailable -> {
                    if (response.isCardAvailable != null) {
                        _cardAvailable.postValue(
                            response.isCardAvailable!!
                        )
                    } else {
                        _cardAvailable.postValue(
                            false
                        )
                    }
                }
            }
        }
    }

    private fun startCheckCard(timeout: Int, showError: Boolean = false) = viewModelScope.launch {
        Log.d(tag, "start check card")
        val nfcAdapter = NfcAdapter.getDefaultAdapter(applicationContext)

        if (Utils.isMfAndroidDevice() && NfcUtils.isDeviceHasNfcIssues(
                Utils.getDeviceBrand(),
                nfcAdapter
            )
        ) {
            _checkCardResult.postValue(CheckCardResult(shouldTurnOffNfc = true))
        } else if (Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_DEVICE)) {
            _checkCardResult.postValue(CheckCardResult(status = Constants.CARD_STATUS_VALID))
        } else if (Build.MANUFACTURER == Constants.DEVICE_MANUFACTURER_PAX ||
            Build.MANUFACTURER == Constants.DEVICE_MANUFACTURER_VERIFONE ||
            Build.MANUFACTURER == Constants.DEVICE_MANUFACTURER_MOREFUN
        ) {
            deviceUseCase.checkCard(timeout).collect { response ->
                Log.d(tag, "check card response $response")
                when (response) {
                    is EdcResponse.Success -> {
                        var checkCardResult = response.data
                        checkCardResult.showErrorDialog = showError
                        _checkCardResult.postValue(
                            response.data
                        )
                        if (checkCardResult.inputMode == Constants.CARD_ENTRY_MODE_MAG) {
                            Log.d(tag, "set MAG card data as cardReaderResult")
                            var cardReaderResult = CardReaderResult(
                                Constants.CARD_ENTRY_MODE_MAG,
                                checkCardResult.data,
                                Constants.CARD_STATUS_VALID
                            )
                            var panNumber = checkCardResult.data?.getString(KEY_PAN_String)
                            if (panNumber.isNullOrEmpty()) {
                                cardReaderResult.cardStatus =
                                    Constants.CARD_CHECK_STATUS_UNEXPECTED_ERROR
                            }
                            _cardReaderResult.postValue(
                                cardReaderResult
                            )
                        }
                    }

                    is EdcResponse.Failure -> {
                        // do nothing
                    }
                }
            }
        }
    }

    private fun startEmvForTransaction(timeout: Int, emvIntent: Bundle) = viewModelScope.launch {
        Log.d(tag, "start emv transaction flow")
        emvIntent.putBoolean("isForceOnline", true)
        // CARD_INSERT(0)- smart IC card
        emvIntent.putInt(ConstIPBOC.startEMV.intent.KEY_cardType_int, Constants.CARD_INSERT)
        emvIntent.putString(ConstIPBOC.startEMV.intent.KEY_merchantId_String, Utils.getMerchantId())
        emvIntent.putString(ConstIPBOC.startEMV.intent.KEY_terminalId_String, Utils.getTerminalId())
        emvIntent.putBoolean(
            ConstIPBOC.startEMV.intent.KEY_isSupportSM_boolean,
            ConstIPBOC.startEMV.intent.VALUE_unsupported
        )
        emvIntent.putString("transCurrCode", "0360")
        deviceUseCase.abortEmv()
        deviceUseCase.startEvm(emvIntent, timeout).collect { response ->
            Log.d(tag, "emv callback response $response")
            when (response) {
                is EdcResponse.Success -> {
                    Log.d("FLOW", response.data.toString())
                    val emvCallbackData = response.data
                    if (emvCallbackData.operation == Constants.EMV_CALLBACK_CONFIRM_CARD_INFO &&
                        emvCallbackData.cardStatus == Constants.CARD_STATUS_VALID &&
                        Utils.isCardExpired(
                            emvCallbackData.data?.getString(
                                KEY_EXPIRED_DATE_String
                            )
                        )
                    ) {
                        Log.d(tag, "card status set to expired")
//                        emvCallbackData.cardStatus = Constants.CARD_STATUS_EXPIRED
                    }
                    _emvCallbackResult.postValue(
                        emvCallbackData
                    )
                }

                is EdcResponse.Failure -> {
                    // do nothing
                }
            }
        }
    }

    private fun startReadCard(timeout: Int) = viewModelScope.launch {
        Log.d(tag, "start read emv card")
        val emvIntent = Bundle()
        emvIntent.putBoolean("isForceOnline", true)
        // CARD_INSERT(0)- smart IC card
        emvIntent.putInt(ConstIPBOC.startEMV.intent.KEY_cardType_int, Constants.CARD_INSERT)
        emvIntent.putString(ConstIPBOC.startEMV.intent.KEY_merchantId_String, Utils.getMerchantId())
        emvIntent.putString(ConstIPBOC.startEMV.intent.KEY_terminalId_String, Utils.getTerminalId())
        emvIntent.putBoolean(
            ConstIPBOC.startEMV.intent.KEY_isSupportSM_boolean,
            ConstIPBOC.startEMV.intent.VALUE_unsupported
        )
        emvIntent.putString("transCurrCode", "0360")
        if (Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_DEVICE)) {
            _cardReaderResult.postValue(
                CardReaderResult(
                    inputMode = Constants.CARD_ENTRY_MODE_IC,
                    cardStatus = Constants.CARD_STATUS_VALID
                )
            )
        }
        deviceUseCase.startEvm(emvIntent, timeout).collect { response ->
            Log.d(tag, "read card response $response")
            when (response) {
                is EdcResponse.Success -> {
                    Log.d("FLOW", response.data.toString())
                    val cardReaderResult = response.data
                    if (cardReaderResult.cardStatus == Constants.CARD_STATUS_VALID &&
                        Utils.isCardExpired(
                            cardReaderResult.data?.getString(
                                KEY_EXPIRED_DATE_String
                            )
                        )
                    ) {
                        Log.d(tag, "card status set to expired")
//                        cardReaderResult.cardStatus = Constants.CARD_STATUS_EXPIRED
                    }
                    if (cardReaderResult.cardStatus == Constants.CARD_STATUS_ERROR) {
                        Log.d(tag, "can't read the card")
                        _checkCardResult.postValue(
                            CheckCardResult(
                                CARD_ENTRY_MODE_NONE,
                                null,
                                Constants.CARD_CHECK_STATUS_ERROR
                            )
                        )
                    }

                    if (cardReaderResult.data != null && cardReaderResult.data?.getString(
                            KEY_PAN_String
                        ).isNotNullOrEmpty()
                    ) {
                        _cardReaderResult.postValue(
                            cardReaderResult
                        )
                    }
                }

                is EdcResponse.Failure -> {
                    // do nothing
                }
            }
        }
    }

    private fun confirmCard(isConfirm: Boolean) = viewModelScope.launch {
        deviceUseCase.importCardConfirmResult.invoke(isConfirm)
    }

    fun generatePinBlock(cardNumber: String?, pin: String): String {
        val response = cardNumber?.let { deviceUseCase.getPinBlock(it, pin) }
        return (response as EdcResponse.Success).data.toString()
    }

    fun getEmvTagDataForTransaction(transactionType: TransactionType): String {
        return when (transactionType) {
            TransactionType.BALANCE_INQUIRY -> {
                if (Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_DEVICE)) {
                    return Utils.getDummyBalanceCheckIccData()
                } else {
                    deviceUseCase.getEmvTagData(Constants.BALANCE_CHECK_TAGS)
                }
            }

            TransactionType.TRANSFER_INQUIRY -> deviceUseCase.getEmvTagData(Constants.TRANSFER_TAGS)
            TransactionType.TRANSFER_POSTING -> deviceUseCase.getEmvTagData(Constants.TRANSFER_TAGS)
            TransactionType.CASH_WITHDRAWAL -> deviceUseCase.getEmvTagData(Constants.TRANSFER_TAGS)
            else -> {
                ""
            }
        }
    }

    private fun importPin(pinBlock: String) {
        deviceUseCase.importPin(pinBlock)
    }

    fun configureEmv() = viewModelScope.launch {
        // configuration should be loaded only once, don't calling this flow on normal android devices to avoid crash
        if (Build.MANUFACTURER == Constants.DEVICE_MANUFACTURER_MOREFUN) {
            try {
                val bundle = Bundle()
                val businessId = "00000000"
                val ret: Int = DeviceHelper.getDeviceService().login(bundle, businessId)
                bwLog("DeviceHelper.getDeviceService().login(bundle, $businessId): $ret")
            } catch (e: Exception) {
                bwLog(tag, "error on login device service")
                bwLog(e = e)
            }
        }
        if (!Utils.hasLoadedConfig()) {
            deviceUseCase.configureAidUseCase()
            deviceUseCase.configureRidUseCase()
        }
    }

    /*
    It should not be needed if duplicate emv flows don't exist
     */
    fun abortEmv() = viewModelScope.launch {
        deviceUseCase.abortEmv()
    }

    private fun simulateInsertCard(duration: Long = 2000) = viewModelScope.launch {
        Log.d(tag, "simulate insert card flow")
        val timer = object : CountDownTimer(duration, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                Log.d(tag, "[Testing with Phone] reading card in $millisUntilFinished")
            }

            override fun onFinish() {
                var checkResultTimeout = CheckCardResult(
                    Constants.CARD_ENTRY_MODE_NONE,
                    null,
                    Constants.CARD_CHECK_STATUS_TIMEOUT
                )
                val dummyCard = Utils.getDummyCardBundle()
                var cardValidResult = CardReaderResult(
                    Constants.CARD_ENTRY_MODE_IC,
                    Utils.getDummyCardBundle(),
                    Constants.CARD_STATUS_VALID
                )
                cardValidResult.iccData = Utils.getDummyIccData()
                var cardUnsupportedResult = CardReaderResult(
                    Constants.CARD_ENTRY_MODE_IC,
                    Utils.getDummyCardBundle(),
                    Constants.CARD_STATUS_UNSUPPORTED
                )
                if (cardValidResult.cardStatus == Constants.CARD_STATUS_VALID &&
                    Utils.isCardExpired(dummyCard?.getString(KEY_EXPIRED_DATE_String))
                ) {
                    Log.d(tag, "card status set to expired")
//                    cardValidResult.cardStatus = Constants.CARD_STATUS_EXPIRED
                }
                var checkResultError = CheckCardResult(
                    Constants.CARD_ENTRY_MODE_NONE,
                    null,
                    Constants.CARD_CHECK_STATUS_ERROR
                )
//                var pinBlockResponse = deviceUseCase.getPinBlock("5859458000031436","123456");
//                Log.d(TAG, "pin block data $pinBlockResponse")
                _cardReaderResult.postValue(cardValidResult)
//                _checkCardResult.postValue(checkResultError)
            }
        }
        timer.start()
    }

    fun stopPinInput() = viewModelScope.launch {
        deviceUseCase.stopPinpadPinInput()
    }

    fun startPinInput() = viewModelScope.launch {
        deviceUseCase.startPinpadPinInput()
    }

    fun doBeeperSound(msec: Int) = viewModelScope.launch {
        deviceUseCase.beeperSound(msec)
    }
}
