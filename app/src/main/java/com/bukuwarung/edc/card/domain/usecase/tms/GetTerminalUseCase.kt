package com.bukuwarung.edc.card.domain.usecase.tms

import com.bukuwarung.edc.card.data.repository.TerminalManagementRepository
import com.bukuwarung.edc.card.domain.model.EdcTransactionResponse
import com.bukuwarung.edc.card.domain.model.FailureType
import javax.inject.Inject
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.callbackFlow

class GetTerminalUseCase @Inject constructor(private val repository: TerminalManagementRepository) {

    operator fun invoke(serialNumber: String) = callbackFlow {
        try {
            // Make the repository call
            val response = repository.getTerminal(serialNumber)

            if (response.isSuccessful) {
                val terminal = response.body()?.data
                if (terminal != null) {
                    trySend(EdcTransactionResponse.Success(terminal))
                } else {
                    trySend(
                        EdcTransactionResponse.Failure(
                            type = FailureType.HTTP_FAILURE,
                            code = "NULL_DATA",
                            message = "Terminal data is null"
                        )
                    )
                }
            } else {
                val errorMessage = "Failed to get terminal: ${response.message()}"
                trySend(
                    EdcTransactionResponse.Failure(
                        type = FailureType.HTTP_FAILURE,
                        code = response.code().toString(),
                        message = errorMessage
                    )
                )
            }
        } catch (e: Exception) {
            trySend(
                EdcTransactionResponse.Failure(
                    type = FailureType.TRANSACTION_FAILURE,
                    code = "EXCEPTION",
                    message = e.message
                )
            )
        }

        // Cleanup when flow is cancelled
        awaitClose {
            // Add any cleanup logic here if needed
        }
    }
}
