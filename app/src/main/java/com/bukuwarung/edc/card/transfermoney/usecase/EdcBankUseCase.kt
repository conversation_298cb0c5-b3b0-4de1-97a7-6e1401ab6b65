package com.bukuwarung.edc.card.transfermoney.usecase

import com.bukuwarung.edc.card.transfermoney.data.repository.TransferMoneyRepository
import javax.inject.Inject

class EdcBankUseCase @Inject constructor(
    private val transferMoneyRepository: TransferMoneyRepository
) {

    suspend fun getEdcBankList(accountId: String, usecase: String, statuses: String) =
        transferMoneyRepository.getEdcBanks(accountId, usecase, statuses)
}
