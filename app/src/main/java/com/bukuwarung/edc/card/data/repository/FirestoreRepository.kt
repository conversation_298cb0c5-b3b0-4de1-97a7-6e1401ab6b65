package com.bukuwarung.edc.card.data.repository

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.bukuwarung.edc.card.data.model.EdcActivation
import com.google.firebase.firestore.DocumentReference
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.ListenerRegistration

class FirestoreRepository {

    private val firestore = FirebaseFirestore.getInstance()
    private lateinit var documentRef: DocumentReference

    private val _data = MutableLiveData<EdcActivation?>()
    val data: LiveData<EdcActivation?> get() = _data

    private var listenerRegistration: ListenerRegistration? = null

    fun startListening(documentId: String) {
        documentRef = firestore.collection("edc_activation_data").document(documentId)
        listenerRegistration = documentRef.addSnapshotListener { snapshot, e ->
            if (e != null) {
                // Handle the error
                _data.value = null
                return@addSnapshotListener
            }

            if (snapshot != null && snapshot.exists()) {
                val data = snapshot.toObject(EdcActivation::class.java)
                _data.value = data
            } else {
                _data.value = null
            }
        }
    }

    fun stopListening() {
        listenerRegistration?.remove()
    }
}
