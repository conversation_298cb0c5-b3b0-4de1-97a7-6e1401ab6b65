package com.bukuwarung.edc.card.ui.transactiondetail.di

import com.bukuwarung.edc.card.ui.transactiondetail.data.api.TransactionDetailsApi
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton
import retrofit2.Retrofit

@Module
@InstallIn(SingletonComponent::class)
class TransactionDetailsApiModule {

    @Provides
    @Singleton
    fun provideTransactionDetailsApi(@Named("normal") retrofit: Retrofit): TransactionDetailsApi =
        retrofit.create(TransactionDetailsApi::class.java)
}
