package com.bukuwarung.edc.card.ui.edcdevices.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.edc.card.ui.edcdevices.model.DeviceItem
import com.bukuwarung.edc.card.ui.edcdevices.usecase.DeviceListUseCase
import com.bukuwarung.edc.util.Utils
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch

@HiltViewModel
class DeviceListViewModel @Inject constructor(private val useCase: DeviceListUseCase) :
    ViewModel() {
    private val _devicesFlow = MutableSharedFlow<MutableList<DeviceItem>>(replay = 1)
    val devicesFlow = _devicesFlow.asSharedFlow()

    fun getDeviceList(devicePlan: String) {
        val devices = Utils.getUserRegisteredDevices().toMutableList()
        viewModelScope.launch {
            if (devices.isNotEmpty()) {
                _devicesFlow.emit(devices)
            } else {
                _devicesFlow.emit(mutableListOf())
            }
        }
    }
}
