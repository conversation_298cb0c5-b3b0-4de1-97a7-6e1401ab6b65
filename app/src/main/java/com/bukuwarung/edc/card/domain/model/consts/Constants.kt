package com.bukuwarung.edc.card.domain.model.consts

object Constants {

    const val DEVICE_TRANSACTION_VALIDATION_ERROR: String = "E01"
    const val KEY_VALIDATION_ERROR_CODE = "VALIDATION_ERROR_CODE"
    const val KEY_INCOMPLETE_TRANSACTION: String = "INCOMPLETE_TRANSACTION"
    const val POS_ENTRY_MODE_IC_CARD: String = "051"
    const val POS_ENTRY_MODE_MAG_CARD: String = "021"

    const val DEVICE_MANUFACTURER_PAX = "PAX"
    const val DEVICE_MANUFACTURER_VERIFONE = "Verifone"
    const val DEVICE_MANUFACTURER_MOREFUN = "Morefun"

    const val CARD_ENTRY_MODE_NONE = 0
    const val CARD_ENTRY_MODE_MAG = 1
    const val CARD_ENTRY_MODE_IC = 2

    const val PINPAD_STATUS_NONE = -1
    const val PINPAD_STATUS_INIT = 0
    const val PINPAD_STATUS_INPUT = 1
    const val PINPAD_STATUS_CONFIRM = 2
    const val PINPAD_STATUS_CANCEL = 3
    const val PINPAD_STATUS_ERROR = 4

    const val TMS_ERROR_NONE = 0
    const val TMS_ERROR_NOT_REGISTERED = 1
    const val TMS_ERROR_NOT_PAIRED = 2
    const val TMS_ERROR_INACTIVE = 3

    const val CARD_CHECK_STATUS_NONE = 0
    const val CARD_CHECK_STATUS_SUCCESS = 1
    const val CARD_CHECK_STATUS_ERROR = 2
    const val CARD_CHECK_STATUS_TIMEOUT = 3
    const val CARD_CHECK_STATUS_UNEXPECTED_ERROR = 5

    const val CARD_STATUS_NONE = 0
    const val CARD_STATUS_VALID = 1
    const val CARD_STATUS_EXPIRED = 2
    const val CARD_STATUS_UNSUPPORTED = 3
    const val CARD_STATUS_UNSUPPORTED_MAG_CARD = 4
    const val CARD_STATUS_ERROR = 6

    const val EMV_NOAPP = 8
    const val EMV_UNSUPPORTED_ECCARD = 16

    const val CARD_INSERT = 0

    const val CARD_TRANSACTION_TIMEOUT = "69"
    const val CARD_TRANSACTION_CANCEL = "E07"
    const val CARD_PIN_LENGTH_INVALID = "E20"

    const val EMV_CALLBACK_NONE = 0
    const val EMV_CALLBACK_REQUEST_AMOUNT = 0
    const val EMV_CALLBACK_SELECT_APPLICATION = 1
    const val EMV_CALLBACK_CONFIRM_CARD_INFO = 2
    const val EMV_CALLBACK_REQUEST_INPUT_PIN = 3
    const val EMV_CALLBACK_REQUEST_ONLINE = 4
    const val EMV_CALLBACK_CONFIRM_CERT_INFO = 5
    const val EMV_CALLBACK_TRANSACTION_RESULT = 6

    val BALANCE_CHECK_TAGS = arrayOf(
        "9F26",
        "9F27",
        "9F10",
        "9F37",
        "9F36",
        "5F34",
        "5F2A",
        "9F1A",
        "9F02",
        "9A",
        "82",
        "9C",
        "84",
        "95"
    )
    val TRANSFER_TAGS = arrayOf(
        "9F26",
        "9F27",
        "9F10",
        "9F37",
        "9F36",
        "5F34",
        "5F2A",
        "9F1A",
        "9F02",
        "9F03",
        "9A",
        "82",
        "9C",
        "84",
        "95"
    )
}
