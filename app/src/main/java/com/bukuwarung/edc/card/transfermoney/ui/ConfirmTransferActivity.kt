package com.bukuwarung.edc.card.transfermoney.ui

import android.os.Build
import android.os.Bundle
import android.widget.Toast
import androidx.activity.viewModels
import com.bukuwarung.edc.R
import com.bukuwarung.edc.ZohoChat
import com.bukuwarung.edc.ZohoChatEntryPoint
import com.bukuwarung.edc.card.CardPinDynamicActivity
import com.bukuwarung.edc.card.ExternalPinpadActivity
import com.bukuwarung.edc.card.constant.CardAnalyticsConstants
import com.bukuwarung.edc.card.constant.PrintConst
import com.bukuwarung.edc.card.data.model.CardReceiptResponse
import com.bukuwarung.edc.card.data.model.EndUserStatusValues
import com.bukuwarung.edc.card.data.model.PinCardErrorResponse
import com.bukuwarung.edc.card.domain.model.consts.Constants
import com.bukuwarung.edc.card.transfermoney.model.TransferMoneyData
import com.bukuwarung.edc.card.transfermoney.model.TransferMoneyRequestResponseBody
import com.bukuwarung.edc.card.transfermoney.viewmodel.TransferMoneyViewModel
import com.bukuwarung.edc.card.ui.EdcCardViewModel
import com.bukuwarung.edc.card.ui.ErrorMapping
import com.bukuwarung.edc.card.ui.ErrorMapping.bcaNotSupported
import com.bukuwarung.edc.card.ui.ErrorMapping.invalidMerchant
import com.bukuwarung.edc.card.ui.ErrorMapping.terminalIdOrDeviceSerialBlockedCodes
import com.bukuwarung.edc.card.ui.receipt.CardReceiptActivity
import com.bukuwarung.edc.databinding.ActivityConfirmTransferBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.global.base.BaseCardActivity
import com.bukuwarung.edc.global.enums.BaseDialogType
import com.bukuwarung.edc.global.enums.TransactionType
import com.bukuwarung.edc.global.network.Status
import com.bukuwarung.edc.homepage.ui.home.HomePageActivity
import com.bukuwarung.edc.payments.data.model.CardErrorType
import com.bukuwarung.edc.payments.ui.addbank.SelectBankActivity
import com.bukuwarung.edc.payments.ui.dialog.CardErrorDialog
import com.bukuwarung.edc.util.CustomBeeper
import com.bukuwarung.edc.util.KycUtil
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.goToDestination
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.orDefault
import com.bukuwarung.edc.util.put
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick
import com.vfi.smartpos.deviceservice.constdefine.ConstIPBOC
import com.vfi.smartpos.deviceservice.constdefine.ConstPBOCHandler
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class ConfirmTransferActivity : BaseCardActivity() {

    private lateinit var binding: ActivityConfirmTransferBinding

    private val viewModel: TransferMoneyViewModel by viewModels()

    private val transferMoneyDetails by lazy {
        intent?.getParcelableExtra(API_RESPONSE) as? TransferMoneyRequestResponseBody
    }
    private val bundle by lazy { intent?.getBundleExtra(DATA) }
    private val cardNumber by lazy { bundle?.getString("PAN") }
    private val isMoneyTransfer by lazy {
        bundle?.getBoolean(SelectBankActivity.IS_MONEY_TRANSFER) ?: true
    }
    private val bankAndAmountDetails by lazy {
        bundle?.getParcelable("BANK_AMOUNT_DETAILS") as? TransferMoneyData
    }
    private val cardExpiry by lazy {
        bundle?.getString(ConstPBOCHandler.onConfirmCardInfo.info.KEY_EXPIRED_DATE_String)
    }
    private val accountType by lazy { bundle?.getString("account_type") }
    private val transactionType by lazy {
        bundle?.getString(Constant.INTENT_KEY_TRANSACTION_TYPE).toString()
    }
    private val pinBlock by lazy { bundle?.getString("PIN_BLOCK") }

    private var isLoading = false
    private var bankDetails: TransferMoneyData? = null
    var errorDialog: CardErrorDialog? = null

    companion object {
        const val DATA = "data"
        const val API_RESPONSE = "api_response"
        const val TRANSACTION_TYPE = "transaction_type"
    }

    override fun setViewBinding() {
        binding = ActivityConfirmTransferBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {
        binding.tbConfirmTransfer.apply {
            tvTitle.text = getString(R.string.transfer_confirmation)
            btnBack.setOnClickListener {
                trackBackPressEvent(CardAnalyticsConstants.TOP_ARROW_BACK_BUTTON)
                transactionFailed()
            }
        }
        val cardExpiry =
            bundle?.getString(ConstPBOCHandler.onConfirmCardInfo.info.KEY_EXPIRED_DATE_String)
        bankDetails = bundle?.getParcelable("BANK_AMOUNT_DETAILS") as? TransferMoneyData
        with(binding) {
            layoutTransferType.tvTitle.text = getString(R.string.transaction_type)
            layoutTransferType.tvValue.text = getString(R.string.transfer)
            layoutDestinationBank.tvTitle.text = getString(R.string.destination_bank)
            layoutDestinationBank.tvValue.text = transferMoneyDetails?.destDetails?.bankName
            layoutDestinationAccount.tvTitle.text = getString(R.string.destination_account)
            layoutDestinationAccount.tvValue.text = transferMoneyDetails?.destDetails?.accountNumber
            layoutAccountOwner.tvTitle.text = getString(R.string.account_ownwer)
            layoutAccountOwner.tvValue.text = transferMoneyDetails?.destDetails?.name
            layoutNominal.tvTitle.text = getString(R.string.nominal)
            layoutNominal.tvValue.text =
                Utils.formatAmount(
                    transferMoneyDetails?.amount?.takeIf { it.isNotBlank() }
                        ?.toDouble()
                )
            layoutNews.tvTitle.text = getString(R.string.news)
            layoutNews.tvValue.text = transferMoneyDetails?.notes
            if (transactionType == TransactionType.CASH_WITHDRAWAL.type) {
                layoutNews.tvTitle.hideView()
                layoutNews.tvValue.hideView()
                layoutNews.tvValue.text = "BW03"
                bundle?.putString("notes", "BW03")
                tbConfirmTransfer.tvTitle.text = "Konfirmasi Tarik Tunai"
                btnAccept.text = getString(R.string.proses)
            }

            btnAccept.singleClick {
                Analytics.trackEvent(CardAnalyticsConstants.EVENT_TRANSFER_CONFIRMATION_REQUEST)
                btnAccept.hideView()
                btnDeny.hideView()
                val emvIntent = Bundle()
                emvIntent.putByte(
                    ConstIPBOC.startEMV.intent.KEY_transProcessCode_byte,
                    0x40.toByte()
                )
                transferMoneyDetails?.amount?.toLong()?.let { it1 ->
                    emvIntent.putLong(
                        ConstIPBOC.startEMV.intent.KEY_authAmount_long,
                        if (Build.MANUFACTURER ==
                            Constants.DEVICE_MANUFACTURER_PAX
                        ) {
                            (it1.toString()).toLong()
                        } else {
                            (
                                it1.toString() +
                                    "00"
                                ).toLong()
                        }
                    )
                }
                val cardEntryMode = bundle?.getInt("CARD_ENTRY_MODE")
                if (Utils.isCardReader() || cardEntryMode == Constants.CARD_ENTRY_MODE_MAG) {
                    doTransferPosting()
                } else {
                    edcCardViewModel.onEventReceived(
                        EdcCardViewModel.Event.OnStartEmvTransaction(
                            emvIntent,
                            60
                        )
                    )
                }
            }
            btnDeny.singleClick {
                Analytics.trackEvent(CardAnalyticsConstants.EVENT_DESTINATION_VALID_CANCEL)
                transactionFailed()
            }
            includePaymentError.ivClose.singleClick {
                checkCardAndShowDialog()
            }
        }
//        setupInactivityDialog()
    }

    private fun transactionFailed() {
        if (errorDialog != null && errorDialog!!.isShowing) {
            errorDialog?.dismiss()
            errorDialog = null
        }
        errorDialog = ErrorMapping.showErrorDialog(
            context = this@ConfirmTransferActivity,
            errorCode = null
        ) { checkCardAndShowDialog() }
        Utils.showDialogIfActivityAlive(this, errorDialog)
    }

    override fun subscribeState() {
        viewModel.transferMoney.observe(this) {
            when (it.status) {
                Status.ERROR -> {
                    isLoading = false
                    binding.includePaymentLoading.root.hideView()
                    val pinCardError = it.data as? PinCardErrorResponse
                    handlePinCardError(pinCardError, PrintConst.TRANSACTION_TYPE_TRANSFER)
                }

                Status.LOADING -> {
                    isLoading = true
                    binding.includePaymentLoading.root.showView()
                    binding.includePaymentLoading.ivClose.hideView()
                    binding.includePaymentLoading.tvClose.hideView()
                }

                Status.SUCCESS -> {
                    isLoading = false
                    stopInactivityTimer(true)
                    val response = it.data as? CardReceiptResponse
                    response?.terminalId = transferMoneyDetails?.terminalId
                    response?.merchantId = transferMoneyDetails?.merchantId
                    bundle?.putInt(TRANSACTION_TYPE, PrintConst.TRANSACTION_TYPE_TRANSFER)
                    Utils.sharedPreferences.put(Utils.FIRST_TIME_INVALID_PIN, true)
                    val map = HashMap<String, String>()
                    map[CardAnalyticsConstants.RC] = response?.responseCode.orEmpty()
                    map[CardAnalyticsConstants.TRANSACTION_STATUS] =
                        response?.status.orDefault("success")
                    map[CardAnalyticsConstants.AMOUNT] = response?.amount.toString()
                    map[CardAnalyticsConstants.SOURCE_BANK] =
                        response?.sourceDetails?.bankName.toString()
                    map[CardAnalyticsConstants.DESTINATION_BANK] =
                        response?.destDetails?.bankName.toString()
                    Analytics.trackEvent(
                        CardAnalyticsConstants.EVENT_RECEIPT_PREVIEW_TRANSFER_VISIT,
                        map
                    )
                    val endUserStatus = response?.endUserStatus?.uppercase() ?: ""
                    if (endUserStatus == EndUserStatusValues.SUCCESS) {
                        if (Utils.sharedPreferences.getBoolean(Utils.TRANSACTION_SOUND, false)) {
                            CustomBeeper(this).playCustomBeep()
                        }
                        openCardReceiptActivity(response)
                    } else {
                        val transactionType = if (response?.transactionType == "CASH_WITHDRAWAL" ||
                            response?.transactionType == "CASH_WITHDRAWAL_POSTING"
                        ) {
                            PrintConst.TRANSACTION_TYPE_CASH_WITHDRAWAL
                        } else {
                            PrintConst.TRANSACTION_TYPE_TRANSFER
                        }
                        val errorType = when (endUserStatus) {
                            EndUserStatusValues.PENDING_SETTLEMENT -> {
                                CardErrorType.PENDING_SETTLEMENT
                            }

                            EndUserStatusValues.PENDING_REFRESH -> {
                                CardErrorType.PENDING_REFRESH
                            }

                            EndUserStatusValues.PENDING -> {
                                CardErrorType.PENDING
                            }

                            else -> {
                                CardErrorType.PENDING
                            }
                        }

                        errorDialog?.dismiss()
                        errorDialog = CardErrorDialog(
                            context = this,
                            errorType = errorType,
                            transactionType = transactionType,
                            errorCode = response?.responseCode,
                            stan = response?.systemTraceAuditNumber,
                            dialogType = BaseDialogType.FULL_SCREEN
                        ) {
                            openCardReceiptActivity(response)
                        }
                        errorDialog?.show()
                    }
                }

                Status.NO_INTERNET -> {
                    isLoading = false
                    Toast.makeText(this, it.message, Toast.LENGTH_SHORT).show()
                }
            }
        }
        edcCardViewModel.checkCardResult.observe(this) {
            when (it?.status) {
                Constants.CARD_STATUS_UNSUPPORTED -> {
                    edcCardViewModel.stopCheckCard()
                    goToDestination(HomePageActivity::class.java)
                }

                Constants.CARD_STATUS_VALID -> {
                    edcCardViewModel.onEventReceived(
                        EdcCardViewModel.Event.OnStartCheckCard(
                            1,
                            false
                        )
                    )
                    if (it.showErrorDialog) {
                        checkCardAndShowDialog()
                    }
                }
            }
        }
        observeEmvTransactionFlow()
    }

    override fun onResume() {
        super.onResume()
//        startInactivityTimer()
    }

    private fun openCardReceiptActivity(response: CardReceiptResponse?) {
        openActivity(CardReceiptActivity::class.java) {
            putParcelable(API_RESPONSE, response)
            putBundle(DATA, bundle)
        }
    }

    private fun observeEmvTransactionFlow() {
        val cardExpiry =
            bundle?.getString(ConstPBOCHandler.onConfirmCardInfo.info.KEY_EXPIRED_DATE_String)
        var accountType = bundle?.getString("account_type")
        val pinBlock = bundle?.getString("PIN_BLOCK").toString()

        edcCardViewModel.emvCallbackResult.observe(this) {
            if (it.cardStatus == Constants.CARD_STATUS_VALID &&
                it.operation == Constants.EMV_CALLBACK_CONFIRM_CARD_INFO
            ) {
                edcCardViewModel.stopCheckCard()
                edcCardViewModel.onEventReceived(EdcCardViewModel.Event.OnCardReadConfirm(true))
            } else if (it.operation == Constants.EMV_CALLBACK_REQUEST_INPUT_PIN) {
                edcCardViewModel.onEventReceived(EdcCardViewModel.Event.OnImportPin(pinBlock))
            } else if (it.operation == Constants.EMV_CALLBACK_REQUEST_ONLINE) {
                doTransferPosting()
            }
        }
    }

    private fun doTransferPosting() {
        val track2Data = bundle?.getString("TRACK2")?.let { Utils.getTrack2(it) }

        val cardEntryMode = bundle?.getInt("CARD_ENTRY_MODE")
        var iccVal =
            if (cardEntryMode ==
                Constants.CARD_ENTRY_MODE_MAG
            ) {
                ""
            } else if (Utils.isCardReader()) {
                bundle?.getString(
                    "icData"
                )
            } else {
                edcCardViewModel.getEmvTagDataForTransaction(TransactionType.TRANSFER_POSTING)
            }
        val posEntryMode =
            if (cardEntryMode ==
                Constants.CARD_ENTRY_MODE_MAG
            ) {
                Constants.POS_ENTRY_MODE_MAG_CARD
            } else {
                Constants.POS_ENTRY_MODE_IC_CARD
            }

        transferMoneyDetails?.let { it1 ->
            // Notes ,accountType, track2Data, posEntryMode, pinBlock,iccData
            val accountId = Utils.getPaymentAccountId()
            // temporary fix for BBW wrong deployment
            // after transfer inquiry BBW add 0000 at end of bank code instead of starting
            val transferMoneyRequestBody = TransferMoneyRequestResponseBody(
                track2Data = track2Data,
                accountType = accountType,
                posEntryMode = posEntryMode,
                cardExpiry = cardExpiry,
                pinBlock = pinBlock,
                cardNumber = it1.cardNumber,
                amount = it1.amount,
                terminalId = it1.terminalId,
                merchantId = it1.merchantId,
                sourceDetails = it1.sourceDetails,
                destDetails = it1.destDetails,
                iccData = iccVal,
                notes = it1.notes,
                invoiceNumber = it1.invoiceNumber

            )
            /*
             * transactions are incomplete unless backend iccData is verified by device
             * keep transaction for reversal in case of device shutdown/damaged iccdata
             */
            Utils.setIncompleteTransferPostingTransaction(
                transferMoneyRequestBody,
                transactionType
            )
            viewModel.transferMoney(
                accountId,
                TransactionType.valueOf(transactionType),
                transferMoneyRequestBody
            )
        }
    }

    private fun handlePinCardError(pinCardError: PinCardErrorResponse?, transactionType: Int) {
        edcCardViewModel.stopCheckCard()
        Utils.clearIncompleteTransaction()
        pinCardError?.code?.let { errorCode ->
            if (errorCode == CardPinDynamicActivity.PIN_INVALID_ERROR_CODE ||
                CardPinDynamicActivity.inValidAttemptErrorCode.contains(
                    errorCode
                )
            ) {
                if (Utils.isCardReader()) {
                    openActivity(ExternalPinpadActivity::class.java) {
                        putBundle("data", bundle)
                        putParcelable("BANK_AMOUNT_DETAILS", bankAndAmountDetails)
                        putBoolean(SelectBankActivity.IS_MONEY_TRANSFER, isMoneyTransfer)
                        putParcelable("PIN_CARD_ERROR", pinCardError)
                    }
                    finish()
                } else {
                    openActivity(Utils.getCardPinActivity(cardNumber)) {
                        putBundle("data", bundle)
                        putParcelable("BANK_AMOUNT_DETAILS", bankAndAmountDetails)
                        putBoolean(SelectBankActivity.IS_MONEY_TRANSFER, isMoneyTransfer)
                        putParcelable("PIN_CARD_ERROR", pinCardError)
                    }
                    finish()
                }
            } else {
                if (errorDialog != null && errorDialog!!.isShowing) {
                    errorDialog?.dismiss()
                    errorDialog = null
                }
                errorDialog = ErrorMapping.showErrorDialog(
                    context = this,
                    errorCode = errorCode,
                    transactionType = transactionType,
                    stan = pinCardError.stan,
                    positiveListener = {
                        checkCardAndShowDialog()
                        if (terminalIdOrDeviceSerialBlockedCodes.contains(errorCode)) {
                            goToDestination(HomePageActivity::class.java)
                            ZohoChat.openZohoChat(
                                ZohoChatEntryPoint.TERMINAL_ID_OR_DEVICE_SERIAL_BLOCKED
                            )
                        }
                    },
                    dismissListener = {
                        checkCardAndShowDialog()
                        if (invalidMerchant.contains(errorCode)) {
                            KycUtil.navigateToKycFlow(this)
                        } else if (bcaNotSupported.contains(errorCode)) {
                            goToDestination(HomePageActivity::class.java)
                        } else if (terminalIdOrDeviceSerialBlockedCodes.contains(errorCode)) {
                            goToDestination(HomePageActivity::class.java)
                        }
                    }
                )
                Utils.showDialogIfActivityAlive(this, errorDialog)
            }
        }
    }

    private fun checkCardAndShowDialog() {
        checkForCardRemove(this)
    }

    override fun onBackPressed() {
        trackBackPressEvent(CardAnalyticsConstants.ANDROID_BACK_BUTTON)
        if (!isLoading) transactionFailed()
    }

    override fun onCardRemove() {
        goToDestination(HomePageActivity::class.java)
    }

    private fun trackBackPressEvent(buttonType: String) {
        val eventProperties = HashMap<String, String>()
        eventProperties[CardAnalyticsConstants.BUTTON] = buttonType
        eventProperties[CardAnalyticsConstants.TRANSACTION_TYPE] = CardAnalyticsConstants.TRANSFER
        eventProperties[CardAnalyticsConstants.PAGE] =
            CardAnalyticsConstants.MONEY_TRANSFER_CONFIRMATION_SCREEN
        Analytics.trackEvent(CardAnalyticsConstants.EVENT_BACK_BUTTON_CLICK, eventProperties)
    }
}
