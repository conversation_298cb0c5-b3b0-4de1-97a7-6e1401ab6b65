package com.bukuwarung.edc.card.constant

object CardAnalyticsConstants {
    const val EVENT_INSERT_CARD_PAGE_VISIT = "insert_card_page_visit"
    const val EVENT_CARD_READING_START = "card_reading_start"
    const val EVENT_CARD_READING_COMPLETED = "card_reading_completed"
    const val EVENT_CHOOSE_ACCOUNT = "choose_account"
    const val EVENT_INFO_CARD_CONFIRM = "info_card_confirm"
    const val EVENT_PIN_VERIFICATION_REQUEST = "pin_verification_request"
    const val EVENT_BACK_BUTTON_CLICK = "back_button_click"
    const val EVENT_PRINT_RECEIPT_START = "print_receipt_start"
    const val EVENT_PRINT_RECEIPT_COMPLETED = "print_receipt_completed"
    const val EVENT_BALANCE_PAGE_PRINT_CLICK = "balance_page_print_click"
    const val EVENT_VISIT_INFORMATION_CARD = "visit_information_card"
    const val EVENT_SELECT_BANK_DESTINATION = "select_bank_destination"
    const val EVENT_TRANSFER_DETAIL_CLICK = "transfer_detail_click"
    const val EVENT_TRANSFER_CONFIRMATION_REQUEST = "transfer_confirmation_request"
    const val EVENT_DESTINATION_VALID_CANCEL = "destination_valid_cancel"
    const val EVENT_TIMEOUT = "timeout"
    const val EVENT_RECEIPT_PREVIEW_TRANSFER_VISIT = "receipt_preview_transfer_visit"
    const val EVENT_RECEIPT_PREVIEW_CHECK_BALANCE_VISIT = "receipt_preview_check_balance_visit"

    const val TIME_TAKEN_TO_READ_CARD = "time_taken_to_read_card"
    const val TRANSACTION_TYPE = "transaction_type"
    const val CARD_TYPE = "type_card"
    const val TRANSACTION_STATUS = "transaction_status"
    const val CARD_READING_STATUS = "card_reading_status"
    const val CARD_EXPIRED = "card_expired"
    const val CARD_UNSUPPORTED = "card_unsupported"
    const val SUCCESS = "success"
    const val FAIL = "fail"
    const val FAIL_REASON = "fail_reason"
    const val SELECTED_ACCOUNT_TYPE = "selected_account_type"
    const val PAGE = "page"
    const val CHECK_BALANCE_DETAIL = "check_balance_detail"
    const val TRANSFER_DETAIL = "transfer_detail"
    const val BUTTON = "button"
    const val TOP_ARROW_BACK_BUTTON = "top_arrow_back_button"
    const val ANDROID_BACK_BUTTON = "android_back_button"
    const val TRIGGER = "trigger"
    const val ENTRY_POINT = "entry_point"
    const val RECEIPT_COPY = "receipt_copy"
    const val RC = "rc"
    const val PRINTING_STATUS = "printing_status"
    const val BANK_DESTINATION = "bank_destination"
    const val ACCOUNT_DESTINATION = "account_destination"
    const val AMOUNT = "amount"
    const val NOTE = "note"
    const val SELECT_BANK_DESTINATION = " select_bank_destination"
    const val INPUT_AMOUNT = "input_amount"
    const val TRANSFER = "transfer"
    const val PAYMENT = "payment"
    const val BALANCE_CHECK = "balance_check"
    const val DESTINATION_BANK = "destination_bank"
    const val SOURCE_BANK = "source_bank"
    const val CARD_PIN_SCREEN = "card_pin_screen"
    const val CARD_READING_SCREEN = "card_reading_screen"
    const val CHOOSE_ACCOUNT_SCREEN = "choose_account_screen"
    const val CARD_INFO_SCREEN = "card_info_screen"
    const val CARD_BALANCE_INFO_SCREEN = "card_balance_info_screen"
    const val MONEY_TRANSFER_DETAILS_CONFIRMATION_SCREEN =
        "money_transfer_details_confirmation_screen"
    const val MONEY_TRANSFER_CONFIRMATION_SCREEN = "moeny_transfer_confirmation_screen"
    const val POP_UP_SET_SETTLEMENT_BANK = "pop_up_set_settlement_bank"
    const val ADD_SETTLEMENT_BANK_ACCOUNT = "add_settlement_bank_account"
    const val INPUT_SETTLEMENT_BANK_ACCOUNT_PAGE = "input_settlement_bank_account_page"
    const val CHECK_SETTLEMENT_BANK_ACCOUNT_ATTEMPT = "check_settlement_bank_account_attempt"
    const val CHECK_SETTLEMENT_BANK_ACCOUNT_RESULT = "check_settlement_bank_account_result"
    const val SETTLEMENT_BANK_ACCOUNT_SAVED_RESULT = "settlement_bank_account_saved_result"
}
