package com.bukuwarung.edc.card.cashwithdrawal.di

import com.bukuwarung.edc.card.cashwithdrawal.api.CashWithdrawalApi
import com.bukuwarung.edc.card.cashwithdrawal.repository.CashWithdrawalRepo
import com.bukuwarung.edc.card.cashwithdrawal.usecase.CashWithdrawalUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton
import retrofit2.Retrofit

@Module
@InstallIn(SingletonComponent::class)
class CashWithdrawalModule {

    @Provides
    @Singleton
    fun provideCashWithdrawalDataSource(@Named("normal") retrofit: Retrofit): CashWithdrawalApi =
        retrofit.create(CashWithdrawalApi::class.java)

    @Provides
    fun provideCashWithdrawalRepository(api: CashWithdrawalApi): CashWithdrawalRepo =
        CashWithdrawalRepo(api)

    @Provides
    fun provideCashWithdrawalUseCase(repo: CashWithdrawalRepo): CashWithdrawalUseCase =
        CashWithdrawalUseCase(repo)
}
