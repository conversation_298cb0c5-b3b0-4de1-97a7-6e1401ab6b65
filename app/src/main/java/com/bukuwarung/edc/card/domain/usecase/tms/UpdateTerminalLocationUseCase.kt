package com.bukuwarung.edc.card.domain.usecase.tms

import com.bukuwarung.edc.card.data.model.TerminalLocation
import com.bukuwarung.edc.card.data.repository.TerminalManagementRepository
import javax.inject.Inject

class UpdateTerminalLocationUseCase @Inject constructor(
    private val repository: TerminalManagementRepository
) {
    suspend operator fun invoke(
        serialNumber: String,
        terminalLocation: TerminalLocation
    ): TerminalLocation {
        val response = repository.updateTerminalLocation(serialNumber, terminalLocation)
        if (response.isSuccessful) {
            return response.body()?.data as TerminalLocation
        } else {
            throw Exception("Failed to update terminal location: ${response.message()}")
        }
    }
}
