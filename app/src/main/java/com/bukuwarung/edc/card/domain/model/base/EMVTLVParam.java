package com.bukuwarung.edc.card.domain.model.base;

/**
 * From project: verifone-x9-demo-emv
 * todo convert to kotlin later
 */

public class EMVTLVParam extends TagLengthValue {
    private static final String TAG = "EMVTLVParam";
    public int flagAppendRemoveClear = 0;

    public static byte[] hexStr2Byte(String hexString) {
//        Log.d(TAG, "hexStr2Byte:" + hexString);
        if (hexString == null || hexString.length() == 0) {
            return new byte[]{0};
        }
        String hexStrTrimed = hexString.replace(" ", "");
//        Log.d(TAG, "hexStr2Byte:" + hexStrTrimed);
        {
            String hexStr = hexStrTrimed;
            int len = hexStrTrimed.length();
            if ((len % 2) == 1) {
                hexStr = hexStrTrimed + "0";
                ++len;
            }
            char highChar, lowChar;
            int high, low;
            byte result[] = new byte[len / 2];
            String s;
            for (int i = 0; i < hexStr.length(); i++) {
                // read 2 chars to convert to byte
//                s = hexStr.substring(i,i+2);
//                int v = Integer.parseInt(s, 16);
//
//                result[i/2] = (byte) v;
//                i++;
                // read high byte and low byte to convert
                highChar = hexStr.charAt(i);
                lowChar = hexStr.charAt(i + 1);
                high = CHAR2INT(highChar);
                low = CHAR2INT(lowChar);
                result[i / 2] = (byte) (high * 16 + low);
                i++;
            }
            return result;

        }
    }

    public static int CHAR2INT(char c) {
        if (
                (c >= '0' && c <= '9')
                        || (c == '=')  // for track2
        ) {
            return c - '0';
        } else if (c >= 'a' && c <= 'f') {
            return c - 'a' + 10;
        } else if (c >= 'A' && c <= 'F') {
            return c - 'A' + 10;
        } else {
            return 0;
        }
    }

    /**
     * @brief get the updateRID, updateAID operation for append, remove, clear
     */
    public int getFlagAppendRemoveClear() {
        return flagAppendRemoveClear;
    }

    /**
     * @brief get the updateRID, updateAID operation for append, remove, clear
     */
    public void setFlagAppendRemoveClear(int flagAppendRemoveClear) {
        this.flagAppendRemoveClear = flagAppendRemoveClear;
    }

    /**
     * @brief get the Length in the AID,RID Length format
     */
    @Override
    public String getLength(int length, int maxBytes) {

        String sLength = null;
        if (length <= 0) {

        } else if (length < 16) {
            sLength = "0" + Integer.toHexString(length).toUpperCase();
        } else if (length < 127) {
            // one byte length
            sLength = Integer.toHexString(length).toUpperCase();
        } else if (length < 256) {
            // 81+len
            sLength = "81" + Integer.toHexString(length).toUpperCase();

        } else if (length < 65535) {
            // 82 + len
            sLength = String.format("82%04X", length);
        }

        return sLength;
    }

    @Override
    public int append(String tlv) {
        int count = 0;
        byte[] bTLV = hexStr2Byte(tlv);
        for (int offset = 0; offset < bTLV.length; ) {
            // read tag
            int tagLen = getTagLen(bTLV[offset]);
            String sTag = tlv.substring(offset * 2, offset * 2 + tagLen * 2);
            int tag = Integer.parseInt(sTag, 16);
            offset += (tagLen);
            // read length
            int lengthLen = getLengthLen(bTLV[offset]);
            if (lengthLen > 1) {
                offset += 1;
            }
            int length = Integer.parseInt(tlv.substring(offset * 2, offset * 2 + 2), 16);
            offset += 1;
            // read value
            String value = tlv.substring(offset * 2, offset * 2 + length * 2);
            offset += length;

            append(tag, value);
            ++count;
        }
        return count;
    }

    protected int getTagLen(byte tag1) {
        if ((tag1 & 31) == 31) {
            return 2;
        } else {
            return 1;
        }
    }

    protected int getLengthLen(byte len1) {
        if ((len1 & 0x81) == 0x81) {
            return 2;
        } else {
            return 1;
        }
    }

    public byte[] getTLV(int tag, byte[] value) {
        byte[] TLV;
        byte[] bLength = new byte[2 + 3];
        String sTag = Integer.toHexString(tag).toUpperCase();
        int offset = 0;
        int lenTag;
        int lenLen;

        int len = value.length;
        if (len <= 0x7F) {
            bLength[offset] = (byte) len;
            ++offset;
        } else if (len <= 0xFF) {
            bLength[offset] = (byte) 0b10000001;
            ++offset;
            bLength[offset] = (byte) (0b10000000 | (len & 0x7F));
            ++offset;
        } else {
            return null;
        }
        lenTag = sTag.length();
        lenLen = offset;
        lenTag /= 2;

        TLV = new byte[lenTag + lenLen + value.length];

        System.arraycopy(hexStr2Byte(sTag), 0, TLV, 0, lenTag);
        offset = lenTag;
        System.arraycopy(bLength, 0, TLV, offset, lenLen);
        offset += lenLen;
        System.arraycopy(value, 0, TLV, offset, value.length);

        return TLV;
    }

}
