package com.bukuwarung.edc.card.di

import com.bukuwarung.edc.card.data.datasource.CheckBalanceDataSource
import com.bukuwarung.edc.card.data.datasource.EdcTransactionApi
import com.bukuwarung.edc.card.data.repository.CheckBalanceRepository
import com.bukuwarung.edc.card.data.repository.EdcTransactionRepository
import com.bukuwarung.edc.card.domain.usecase.CardTransferPostingUseCase
import com.bukuwarung.edc.card.domain.usecase.CheckCardBalanceUseCase
import com.bukuwarung.edc.card.domain.usecase.ConfirmBalanceCheckUseCase
import com.bukuwarung.edc.card.domain.usecase.ConfirmTransferPostingUseCase
import com.bukuwarung.edc.card.domain.usecase.EdcTransactionUseCase
import com.bukuwarung.edc.card.domain.usecase.EvmInputOnlineResultUseCase
import com.bukuwarung.edc.card.domain.usecase.SubmitIncompleteTransactionUseCase
import com.bukuwarung.edc.card.domain.usecase.SubmitReversalUseCase
import com.bukuwarung.edc.card.transfermoney.data.repository.TransferMoneyRepository
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton
import retrofit2.Retrofit

@Module
@InstallIn(SingletonComponent::class)
class EdcApiModule {

    @Singleton
    @Provides
    fun providesCheckBalanceDataSource(
        @Named("normal") retrofit: Retrofit
    ): CheckBalanceDataSource = retrofit.create(CheckBalanceDataSource::class.java)

    @Singleton
    @Provides
    fun providesTransactionDataSource(@Named("normal") retrofit: Retrofit): EdcTransactionApi =
        retrofit.create(EdcTransactionApi::class.java)

    @Provides
    @Singleton
    fun provideEdcTransactionUseCase(edcTransactionRepository: EdcTransactionRepository) =
        EdcTransactionUseCase(
            submitTransactionReversal = SubmitReversalUseCase(edcTransactionRepository),
            submitIncompleteTransaction = SubmitIncompleteTransactionUseCase(
                edcTransactionRepository
            ),
            confirmBalanceCheckUseCase = ConfirmBalanceCheckUseCase(edcTransactionRepository),
            confirmTransferPostingUseCase = ConfirmTransferPostingUseCase(edcTransactionRepository)
        )

    @Provides
    @Singleton
    fun provideEdcCheckBalanceUseCase(
        checkBalanceRepository: CheckBalanceRepository,
        inputOnlineResultUseCase: EvmInputOnlineResultUseCase,
        edcTransactionUseCase: EdcTransactionUseCase
    ) = CheckCardBalanceUseCase(
        checkBalanceRepository,
        inputOnlineResultUseCase,
        edcTransactionUseCase
    )

    @Provides
    @Singleton
    fun provideEdcCardTransferPostingUseCase(
        transferMoneyRepository: TransferMoneyRepository,
        inputOnlineResultUseCase: EvmInputOnlineResultUseCase,
        edcTransactionUseCase: EdcTransactionUseCase
    ) = CardTransferPostingUseCase(
        transferMoneyRepository,
        inputOnlineResultUseCase,
        edcTransactionUseCase
    )
}
