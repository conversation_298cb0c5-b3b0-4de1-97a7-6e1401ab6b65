package com.bukuwarung.edc.card.data.repository

import com.bukuwarung.edc.card.data.datasource.EdcTransactionApi
import com.bukuwarung.edc.card.data.model.IncompleteTransactionRequest
import com.bukuwarung.edc.card.data.model.TransactionConfirmRequest
import com.bukuwarung.edc.card.data.model.TransactionReversalRequest
import javax.inject.Inject

class EdcTransactionRepository @Inject constructor(
    private val edcTransactionApi: EdcTransactionApi
) {

    suspend fun submitTransactionReversal(
        accountId: String,
        transactionReversalRequest: TransactionReversalRequest
    ) = edcTransactionApi.submitReversal(accountId, transactionReversalRequest)

    suspend fun confirmBalanceCheckTransaction(
        accountId: String,
        transactionConfirmRequest: TransactionConfirmRequest
    ) = edcTransactionApi.confirmBalanceCheck(accountId, transactionConfirmRequest)

    suspend fun confirmTransferPostingTransaction(
        accountId: String,
        transactionConfirmRequest: TransactionConfirmRequest
    ) = edcTransactionApi.confirmTransferPosting(accountId, transactionConfirmRequest)

    suspend fun confirmCashWithdrawalPostingTransaction(
        accountId: String,
        transactionConfirmRequest: TransactionConfirmRequest
    ) = edcTransactionApi.confirmCashWithdrawalPosting(accountId, transactionConfirmRequest)

    suspend fun submitIncompleteTransaction(
        accountId: String,
        incompleteTransactionRequest: IncompleteTransactionRequest
    ) = edcTransactionApi.submitIncompleteTransaction(accountId, incompleteTransactionRequest)
}
