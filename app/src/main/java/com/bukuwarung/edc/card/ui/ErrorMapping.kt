package com.bukuwarung.edc.card.ui

import android.content.Context
import com.bukuwarung.edc.payments.data.model.CardErrorType
import com.bukuwarung.edc.payments.ui.dialog.CardErrorDialog

object ErrorMapping {

    val TERMINAL_ID_OR_DEVICE_SERIAL_BLOCKED = "BWR01"
    val unableToReadCardErrorCode = arrayListOf("97")
    val httpErrorCode = arrayListOf("500", "501", "503", "504", "400", "401", "403", "404")
    val errorOccurredInSystemErrorCode = arrayListOf(
        "BW 50",
        "15",
        "20",
        "61",
        "65",
        "70",
        "77",
        "79",
        "80",
        "81",
        "83",
        "88",
        "89",
        "91",
        "92",
        "94",
        "95",
        "N8",
        "P9",
        "Q0",
        "Q1",
        "Q2",
        "R8",
        "T2",
        "T4",
        "T8",
        "U0",
        "U1",
        "U2",
        "U3",
        "U4",
        "U5",
        "U6",
        "U7",
        "U8",
        "V0",
        "V1",
        "V2",
        "V3",
        "V4",
        "V7",
        "V8",
        "V9"
    )
    val bcaNotSupported = arrayListOf("96")
    val systemBusyErrorCode = arrayListOf("99")
    val requestTimeOutErrorCode = arrayListOf("69")
    val transactionTimeOutErrorCode = arrayListOf("68")
    private val inSufficientBalanceErrorCode = arrayListOf("51")
    private val OverLimitPerUserErrorCode = arrayListOf("13")
    val cardNotSupportedErrorCode = arrayListOf("31", "39", "40", "T3")
    val magCardNotSupportedErrorCode = arrayListOf("E05")
    val magCardUnexpectedErrorCode = arrayListOf("E08")
    val terminalNotActivatedErrorCode = arrayListOf("E06")
    val terminalNotActivatedLoadingFailedErrorCode = arrayListOf("E18")
    val terminalNotMappedInTMS = arrayListOf("E20")
    val terminalDisabledInTMS = arrayListOf("E21")
    val terminalConfigNotExistInTMS = arrayListOf("E24")
    val cancelTransaction = arrayListOf("E07")
    private val requestFunctionNotSupportedErrorCode = arrayListOf("53")
    private val noChequingAmountErrorCode = arrayListOf("52")
    private val expiredCardErrorCode = arrayListOf("33", "54")
    val transactionNotPermittedToCardholder = arrayListOf("57")
    private val cardBlockedErrorCode =
        arrayListOf("01", "1", "36", "41", "43", "58", "62", "63", "N0")
    private val closedAccountErrorCode = arrayListOf("78")
    val transactionFailedErrorCode = arrayListOf("100")
    val inputOnlineResultFailed = arrayListOf("E01")
    private val transactionIncompleted = arrayListOf("E02")
    val removeCardErrorCode = arrayListOf("04")
    private val transactionCancelDueToIncorrectPin = arrayListOf("55")
    private val noDestinationAccountNumber = arrayListOf("76")
    private val internalErrorTimeOut = arrayListOf("BW69")
    private val cardPrefixBlocked = arrayListOf("BW36")
    val noInternetErrorCode = arrayListOf("NO503")
    val transactionDeclined = arrayListOf("Z1", "Z2", "EE")
    val transactionPendingE01E02BW69 = arrayListOf("E01", "E02", "BW69")
    private val unIdentifiedErrorCode = arrayListOf("05")
    val invalidMerchant = arrayListOf("03", "E03")
    private val invalidTransaction = arrayListOf("12")
    private val formatError = arrayListOf("30")
    private val invalidCardDamagedUnsupported = arrayListOf("14")
    private val cardBePickedUpAtAtm = arrayListOf("67")
    val terminalIdOrDeviceSerialBlockedCodes = arrayListOf(TERMINAL_ID_OR_DEVICE_SERIAL_BLOCKED)

    fun showErrorDialog(
        context: Context,
        transactionType: Int? = null,
        errorCode: String? = null,
        stan: String? = "",
        source: String? = null,
        exception: Exception? = null,
        positiveListener: ((dialog: CardErrorDialog) -> Unit)? = null,
        dismissListener: ((dialog: CardErrorDialog) -> Unit)? = null
    ): CardErrorDialog {
        val cardErrorType: CardErrorType = returnErrorType(errorCode)
        val errorDialog = CardErrorDialog(
            context = context,
            errorType = cardErrorType,
            transactionType = transactionType,
            errorCode = errorCode,
            stan = stan,
            source = source,
            exception = exception,
            dismissListener = dismissListener,
            positiveListener = positiveListener
        )

        return errorDialog
    }

    fun returnErrorType(errorCode: String?): CardErrorType = when {
        unableToReadCardErrorCode.contains(errorCode) -> CardErrorType.UNABLE_TO_READ
        errorOccurredInSystemErrorCode.contains(errorCode) -> CardErrorType.SYSTEM_ERROR
        bcaNotSupported.contains(errorCode) -> CardErrorType.BCA_NOT_SUPPORTED
        systemBusyErrorCode.contains(errorCode) -> CardErrorType.SYSTEM_BUSY
        httpErrorCode.contains(errorCode) -> CardErrorType.SYSTEM_ERROR
        requestTimeOutErrorCode.contains(errorCode) -> CardErrorType.REQUEST_TIMEOUT
        transactionTimeOutErrorCode.contains(errorCode) -> CardErrorType.TRANSACTION_TIMEOUT
        inSufficientBalanceErrorCode.contains(errorCode) -> CardErrorType.INSUFFICIENT_BALANCE
        OverLimitPerUserErrorCode.contains(errorCode) -> CardErrorType.OVER_LIMIT_AMOUNT
        cardNotSupportedErrorCode.contains(errorCode) -> CardErrorType.CARD_NOT_SUPPORTED
        requestFunctionNotSupportedErrorCode.contains(
            errorCode
        ) -> CardErrorType.REQUEST_NOT_SUPPORTED

        noChequingAmountErrorCode.contains(errorCode) -> CardErrorType.NO_CHEQUING_AMOUNT
        expiredCardErrorCode.contains(errorCode) -> CardErrorType.EXPIRED_CARD
        inputOnlineResultFailed.contains(
            errorCode
        ) -> CardErrorType.INPUT_ONLINE_RESPONSE_FAILED

        transactionIncompleted.contains(errorCode) -> CardErrorType.TRANSACTION_INCOMPLETE
        cardBlockedErrorCode.contains(errorCode) -> CardErrorType.CARD_BLOCKED
        transactionNotPermittedToCardholder.contains(
            errorCode
        ) -> CardErrorType.TRANSACTION_NOT_PERMITTED

        closedAccountErrorCode.contains(errorCode) -> CardErrorType.CLOSED_ACCOUNT
        removeCardErrorCode.contains(errorCode) -> CardErrorType.REMOVE_CARD
        transactionCancelDueToIncorrectPin.contains(errorCode) -> CardErrorType.INCORRECT_PIN
        noDestinationAccountNumber.contains(
            errorCode
        ) -> CardErrorType.NO_DESTINATION_ACCOUNT_NUMBER

        internalErrorTimeOut.contains(errorCode) -> CardErrorType.INTERNAL_ERROR_TIME_OUT
        cardPrefixBlocked.contains(errorCode) -> CardErrorType.CARD_PREFIX_BLOCKED
        noInternetErrorCode.contains(errorCode) -> CardErrorType.NO_INTERNET
        magCardNotSupportedErrorCode.contains(
            errorCode
        ) -> CardErrorType.UNSUPPORTED_MAGNETIC_CARD

        magCardUnexpectedErrorCode.contains(
            errorCode
        ) -> CardErrorType.UNEXPECTED_ERROR_MAGNETIC_CARD

        terminalNotActivatedErrorCode.contains(
            errorCode
        ) -> CardErrorType.TERMINAL_NOT_ACTIVATED

        terminalNotActivatedLoadingFailedErrorCode.contains(
            errorCode
        ) -> CardErrorType.TERMINAL_NOT_ACTIVATED_LOADING_FAILED

        terminalNotMappedInTMS.contains(errorCode) -> CardErrorType.TERMINAL_NOT_EXIST_IN_TMS
        terminalConfigNotExistInTMS.contains(errorCode) -> CardErrorType.TERMINAL_NOT_CONFIGURED
        terminalDisabledInTMS.contains(errorCode) -> CardErrorType.NETWORK_OR_SERVER_ISSUE
        cancelTransaction.contains(errorCode) -> CardErrorType.TRANSACTION_CANCELED
        transactionDeclined.contains(errorCode) -> CardErrorType.TRANSACTION_DECLINED
        unIdentifiedErrorCode.contains(errorCode) -> CardErrorType.UN_IDENTIFIED_ERROR
        invalidMerchant.contains(errorCode) -> CardErrorType.INVALID_MERCHANT
        invalidTransaction.contains(errorCode) -> CardErrorType.INVALID_TRANSACTION
        formatError.contains(errorCode) -> CardErrorType.FORMAT_ERROR
        invalidCardDamagedUnsupported.contains(
            errorCode
        ) -> CardErrorType.INVALID_CARD_DAMAGED_UNSUPPORTED

        cardBePickedUpAtAtm.contains(errorCode) -> CardErrorType.CARD_BE_PICKED_UP_AT_ATM
        terminalIdOrDeviceSerialBlockedCodes.contains(
            errorCode
        ) -> CardErrorType.TERMINAL_ID_OR_DEVICE_SERIAL_BLOCKED

        else -> CardErrorType.TRANSACTION_FAILED
    }
}
