package com.bukuwarung.edc.card.ui.widget

import android.content.Context
import android.util.AttributeSet
import android.util.Log
import android.view.View
import android.widget.Button
import android.widget.LinearLayout
import androidx.appcompat.widget.AppCompatButton
import androidx.appcompat.widget.AppCompatImageView
import com.bukuwarung.edc.R
import com.bukuwarung.edc.card.data.model.PinpadButtonCoordinates

class CardPinpadView : LinearLayout {
    private val keysMap: MutableMap<String, Button?> = HashMap()

    constructor(context: Context?) : super(context) {
        init()
    }

    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs) {
        init()
    }

    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        init()
    }

    private fun init() {
        inflate(context, R.layout.card_pinpad, this)
        initViews()
    }

    var btn = arrayOfNulls<AppCompatButton>(10)
    var btnConf: AppCompatButton? = null
    var btnCancel: AppCompatButton? = null
    var btnDel: AppCompatButton? = null

    fun getLayout(btnBack: AppCompatImageView?): LinkedHashMap<View?, String> {
        val keyboardInputs = LinkedHashMap<View?, String>()
        for (i in 0..9) {
            keyboardInputs[btn[i]] = NUM
        }
        keyboardInputs[btnConf] = ENTER
        keyboardInputs[btnDel] = CLEAR
        keyboardInputs[btnBack] = CANCEL
        return keyboardInputs
    }

    val buttonCoordinates: List<PinpadButtonCoordinates>
        get() = mutableListOf<PinpadButtonCoordinates>().apply {
            addAll((0..9).map { i -> getButtonCoordinates("btn_$i", btn[i], TYPE_NUM) })
            add(getButtonCoordinates("btn_conf", btnConf, TYPE_CONF))
            add(getButtonCoordinates("btn_cancel", btnCancel, TYPE_CANCEL))
            add(getButtonCoordinates("btn_del", btnDel, TYPE_DEL))
        }

    private fun getButtonCoordinates(
        buttonId: String,
        button: Button?,
        type: Int
    ): PinpadButtonCoordinates {
        button ?: return PinpadButtonCoordinates(buttonId, 0, 0, 0, 0, type) // Handle null button
        keysMap[buttonId] = button
        val location = IntArray(2).apply { button.getLocationOnScreen(this) }
        val (x, y) = location
        return PinpadButtonCoordinates(
            buttonName = buttonId,
            topLeftX = x,
            topLeftY = y,
            bottomRightX = x + button.width,
            bottomRightY = y + button.height,
            buttonType = type
        )
    }

    private fun initViews() {
        btn[0] = findViewById(R.id.keyboard_btn0)
        btn[1] = findViewById(R.id.keyboard_btn1)
        btn[2] = findViewById(R.id.keyboard_btn2)
        btn[3] = findViewById(R.id.keyboard_btn3)
        btn[4] = findViewById(R.id.keyboard_btn4)
        btn[5] = findViewById(R.id.keyboard_btn5)
        btn[6] = findViewById(R.id.keyboard_btn6)
        btn[7] = findViewById(R.id.keyboard_btn7)
        btn[8] = findViewById(R.id.keyboard_btn8)
        btn[9] = findViewById(R.id.keyboard_btn9)
        btnDel = findViewById(R.id.keyboard_btn_delete)
        btnCancel = findViewById(R.id.keyboard_btn_cancel)
        btnConf = findViewById(R.id.keyboard_btn_confirm)
    }

    fun initKeyboard(keyMap: Map<String, String>) {
        try {
            Log.d("TAG", "size:" + keyMap.size)
            val entrys = keyMap.entries
            for ((key, value) in entrys) {
                Log.d("TAG", "$key--$value")
                val index = key[4] - '0'
                if (index >= 0 && index < 10) {
                    btn[index]!!.text = value
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    companion object {
        const val TYPE_NUM = 0
        const val TYPE_CONF = 1
        const val TYPE_CANCEL = 2
        const val TYPE_DEL = 3
        const val NUM = "NUM"
        const val ENTER = "ENTER"
        const val CLEAR = "CLEAR"
        const val CANCEL = "CANCEL"
    }
}
