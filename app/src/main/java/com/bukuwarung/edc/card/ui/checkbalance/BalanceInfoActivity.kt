package com.bukuwarung.edc.card.ui.checkbalance

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.bukuwarung.edc.R
import com.bukuwarung.edc.card.constant.CardAnalyticsConstants
import com.bukuwarung.edc.card.data.model.CardReceiptResponse
import com.bukuwarung.edc.card.ui.receipt.CardReceiptActivity
import com.bukuwarung.edc.databinding.LayoutBalanceInformationBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.util.CustomBeeper
import com.bukuwarung.edc.util.DateTimeUtils
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.singleClick
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class BalanceInfoActivity : AppCompatActivity() {

    private lateinit var binding: LayoutBalanceInformationBinding
    private val cardReceiptResponse by lazy {
        intent?.getParcelableExtra(API_RESPONSE) as? CardReceiptResponse?
    }
    private val bundle by lazy { intent?.getBundleExtra(DATA) }

    private val pan: String by lazy { intent?.getStringExtra(CardReceiptActivity.PAN).orEmpty() }

    companion object {
        const val API_RESPONSE = "api_response"
        const val DATA = "data"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        Analytics.trackEvent(CardAnalyticsConstants.EVENT_RECEIPT_PREVIEW_CHECK_BALANCE_VISIT)
        binding = LayoutBalanceInformationBinding.inflate(layoutInflater)
        setContentView(binding.root)
        Utils.clearIncompleteTransaction()
        binding.tbBalanceInfo.apply {
            tvTitle.text = "Cek Saldo Via Kartu"
            btnBack.hideView()
        }

        if (Utils.sharedPreferences.getBoolean(Utils.TRANSACTION_SOUND, false)) {
            CustomBeeper(this).playCustomBeep()
        }

        with(binding) {
            layoutDate.tvTitle.text = getString(R.string.date)
            var date = cardReceiptResponse?.transactionDate
            if (date != null && date.contains("/")) {
                date = date.replace("/", "-")
            }
            layoutDate.tvValue.text = DateTimeUtils.getFormattedLocalDateTime(
                date.orEmpty(),
                DateTimeUtils.DD_MMM_YYYY_HH_MM
            )
            saldoCard.tvAmount.text =
                Utils.formatAmount(cardReceiptResponse?.balanceInformation?.balance?.toDouble())

            btnSubmit.singleClick {
                Analytics.trackEvent(CardAnalyticsConstants.EVENT_BALANCE_PAGE_PRINT_CLICK)
                openActivity(CardReceiptActivity::class.java) {
                    putParcelable(API_RESPONSE, cardReceiptResponse)
                    putBundle(DATA, bundle)
                }
            }
        }
    }

    override fun onBackPressed() {
        trackBackPressEvent(CardAnalyticsConstants.ANDROID_BACK_BUTTON)
    }

    private fun trackBackPressEvent(buttonType: String) {
        val eventProperties = HashMap<String, String>()
        eventProperties[CardAnalyticsConstants.BUTTON] = buttonType
        eventProperties[CardAnalyticsConstants.TRANSACTION_TYPE] =
            CardAnalyticsConstants.BALANCE_CHECK
        eventProperties[CardAnalyticsConstants.PAGE] =
            CardAnalyticsConstants.CARD_BALANCE_INFO_SCREEN
        Analytics.trackEvent(CardAnalyticsConstants.EVENT_BACK_BUTTON_CLICK, eventProperties)
    }
}
