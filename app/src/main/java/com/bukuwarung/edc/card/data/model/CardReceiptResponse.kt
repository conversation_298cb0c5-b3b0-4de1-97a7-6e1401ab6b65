package com.bukuwarung.edc.card.data.model

import android.os.Parcelable
import androidx.annotation.Keep
import com.bukuwarung.edc.card.transfermoney.model.BankDetails
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Keep
@Parcelize
data class CardReceiptResponse(
    @SerializedName("id")
    var id: String? = null,
    @SerializedName("balance_information")
    var balanceInformation: BalanceInformation? = null,
    @SerializedName("invoice_number")
    var invoiceNumber: String? = null,
    @SerializedName("merchant_id")
    var merchantId: String? = null,
    @SerializedName("rrn")
    var rrn: String? = null,
    @SerializedName("system_trace_audit_number")
    var systemTraceAuditNumber: String? = null,
    @SerializedName("terminal_id")
    var terminalId: String? = null,
    @SerializedName("transaction_date")
    var transactionDate: String? = null,
    @SerializedName("icc_data")
    var iccData: String? = null,
    @SerializedName("response_code")
    var responseCode: String? = null,
    @SerializedName("dest_details")
    var destDetails: BankDetails? = null,
    @SerializedName("amount")
    var amount: Double? = null,
    @SerializedName("card_number")
    var cardNumber: String? = null,
    @SerializedName("source_details")
    var sourceDetails: BankDetails? = null,
    @SerializedName("status")
    var status: String? = null,
    @SerializedName("end_user_status")
    var endUserStatus: String? = null,
    @SerializedName("error")
    val errorBody: ErrorBody? = null,
    @SerializedName("transaction_type")
    var transactionType: String? = null
) : Parcelable

@Keep
@Parcelize
data class ErrorBody(
    @SerializedName("code")
    val code: String? = null,
    @SerializedName("message")
    val message: String? = null
) : Parcelable

object EndUserStatusValues {
    const val SUCCESS = "SUCCESS" // 200
    const val PENDING_SETTLEMENT = "PENDING_SETTLEMENT" // 68
    const val PENDING_REFRESH = "PENDING_REFRESH" // BW69, E01, E02
    const val PENDING = "PENDING" // TO
    const val FAILED = "FAILED"
}
