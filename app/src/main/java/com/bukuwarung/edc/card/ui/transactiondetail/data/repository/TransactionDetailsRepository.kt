package com.bukuwarung.edc.card.ui.transactiondetail.data.repository

import com.bukuwarung.edc.card.ui.transactiondetail.data.api.TransactionDetailsApi
import javax.inject.Inject

class TransactionDetailsRepository @Inject constructor(
    private val transactionDetailsApi: TransactionDetailsApi
) {

    suspend fun getTransactionDetails(accountId: String, transactionId: String, type: String) =
        transactionDetailsApi.getTransactionDetails(accountId, transactionId, type)
}
