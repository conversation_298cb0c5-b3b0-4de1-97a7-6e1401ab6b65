package com.bukuwarung.edc.card.cardhistory.data.repository

import com.bukuwarung.edc.card.cardhistory.data.api.CardTransactionHistoryApi
import javax.inject.Inject

class CardHistoryRepository @Inject constructor(
    private val cardTransactionHistoryApi: CardTransactionHistoryApi
) {

    suspend fun getTransactionHistory(
        accountId: String,
        pageNumber: Int,
        pageSize: Int,
        order: String?,
        startDate: String?,
        endDate: String?,
        type: String?,
        terminalId: String?,
        status: String?
    ) = cardTransactionHistoryApi.getTransactionHistory(
        accountId,
        type,
        pageNumber,
        pageSize,
        order,
        startDate,
        endDate,
        terminalId,
        status
    )
}
