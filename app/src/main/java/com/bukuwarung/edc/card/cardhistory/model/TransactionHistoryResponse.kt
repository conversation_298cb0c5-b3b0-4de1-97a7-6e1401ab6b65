package com.bukuwarung.edc.card.cardhistory.model

import com.google.errorprone.annotations.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class TransactionHistoryResponse(

    @field:SerializedName("pagination_details")
    val paginationDetails: PaginationDetails? = null,

    @field:SerializedName("history")
    val history: ArrayList<HistoryItem> = arrayListOf()
)

@Keep
data class HistoryItem(

    @field:SerializedName("date")
    val date: String? = null,

    @field:SerializedName("id")
    val id: String? = null,

    @field:SerializedName("type")
    val type: String? = null,

    @field:SerializedName("reference_number")
    val referenceNumber: String? = null,

    @field:SerializedName("status")
    val status: String? = null,

    @field:SerializedName("end_user_status")
    val endUserStatus: String? = null,

    @field:SerializedName("amount")
    val amount: Double? = null,

    @field:SerializedName("response_code")
    var responseCode: String? = null,

    @field:SerializedName("plan_type")
    val planType: String? = null,

    @field:SerializedName("order_amount")
    val orderAmount: String? = null,

    @field:SerializedName("created_at")
    val createdAt: String? = null,

    @field:SerializedName("payment_status")
    val paymentStatus: String? = null,

    @field:SerializedName("payment_link_expired")
    val paymentLinkExpired: Boolean? = null,

    @field:SerializedName("payment_date")
    val paymentDate: String? = null,

    @SerializedName("serial_number")
    val serialNumber: String? = null,

    @SerializedName("vendor")
    val vendor: String? = null

)

@Keep
data class PaginationDetails(

    @field:SerializedName("page_number")
    val pageNumber: Int? = null,

    @field:SerializedName("total_count")
    val totalCount: Int? = null,

    @field:SerializedName("page_size")
    val pageSize: Int? = null
)
