package com.bukuwarung.edc.card.activation.ui.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.analytic.BtAnalytics.bwLog
import com.bukuwarung.bluetooth_printer.activities.print.setup.GetMappedPhoneNumberUseCase
import com.bukuwarung.network.model.ActivateDeviceRequest
import com.bukuwarung.network.model.ActivateDeviceResponse
import com.bukuwarung.network.model.Resource
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.json.JSONObject

@HiltViewModel
class EdcActivationViewModel @Inject constructor(
    private val getMappedPhoneNumberUseCase: GetMappedPhoneNumberUseCase
) : ViewModel() {

    private val _activateDeviceState = MutableLiveData<Resource<ActivateDeviceResponse>>()
    val activateDeviceState: LiveData<Resource<ActivateDeviceResponse>>
        get() = _activateDeviceState

    fun activateDevice(request: ActivateDeviceRequest) = viewModelScope.launch(Dispatchers.IO) {
        try {
            val response = getMappedPhoneNumberUseCase.activateDevice(request)
            if (response.isSuccessful) {
                _activateDeviceState.postValue(Resource.success(response.body()?.data))
            } else {
                val errorBody = JSONObject(response.errorBody()?.string().orEmpty())
                var message = if (errorBody.has("message")) {
                    errorBody.getString("message")
                } else {
                    "Some Unknown Error occurred"
                }
                _activateDeviceState.postValue(Resource.error(message, null))
            }
        } catch (e: Exception) {
            bwLog(e = e)
        }
    }
}
