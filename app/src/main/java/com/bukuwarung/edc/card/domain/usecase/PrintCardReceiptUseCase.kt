package com.bukuwarung.edc.card.domain.usecase

import android.graphics.Bitmap
import com.bukuwarung.edc.card.domain.model.ReceiptPrintCommand
import com.bukuwarung.edc.card.domain.service.IEdcDeviceService
import javax.inject.Inject

class PrintCardReceiptUseCase @Inject constructor(private val deviceService: IEdcDeviceService) {
    operator fun invoke(
        printCommand: ReceiptPrintCommand,
        headerLogo: ByteArray?,
        bukuAgen: ByteArray?,
        headerBitmap: Bitmap?,
        bukuAgenBitmap: Bitmap?,
        showFooterLogo: Boolean
    ) = deviceService.printReceipt(
        printCommand,
        headerLogo,
        bukuAgen,
        headerBitmap,
        bukuAgenBitmap,
        showFooterLogo
    )
}
