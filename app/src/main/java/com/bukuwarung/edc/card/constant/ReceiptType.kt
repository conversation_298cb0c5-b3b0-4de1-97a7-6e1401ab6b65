package com.bukuwarung.edc.card.constant

import com.bukuwarung.edc.card.constant.PrintConst.COPY_TYPE_BANK
import com.bukuwarung.edc.card.constant.PrintConst.COPY_TYPE_CUSTOMER
import com.bukuwarung.edc.card.constant.PrintConst.COPY_TYPE_MERCHANT
import com.bukuwarung.edc.card.constant.PrintConst.COPY_TYPE_NONE

enum class ReceiptType(val receiptId: Int, val message: String) {
    RECEIPT_TYPE_CUSTOMER(COPY_TYPE_CUSTOMER, "Customer Copy"),
    RECEIPT_TYPE_MERCHANT(COPY_TYPE_MERCHANT, "Merchant Copy"),
    RECEIPT_TYPE_BANK(COPY_TYPE_BANK, "Bank Copy"),
    RECEIPT_TYPE_NONE(COPY_TYPE_NONE, "")
}
