package com.bukuwarung.edc.card.ui.transactiondetail.data.api

import com.bukuwarung.edc.card.ui.transactiondetail.model.TransactionDetailResponse
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Path
import retrofit2.http.Query

interface TransactionDetailsApi {
    @GET("edc-adapter/transaction/history/{account_id}/detail/{transaction_id}")
    suspend fun getTransactionDetails(
        @Path("account_id") accountId: String,
        @Path("transaction_id") transactionId: String,
        @Query("type") type: String
    ): Response<TransactionDetailResponse>
}
