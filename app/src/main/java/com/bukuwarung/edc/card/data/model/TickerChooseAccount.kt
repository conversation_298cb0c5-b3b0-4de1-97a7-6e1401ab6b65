package com.bukuwarung.edc.card.data.model

import androidx.annotation.Keep
import com.bukuwarung.edc.global.enums.TransactionType
import com.bukuwarung.edc.util.Utils
import com.google.gson.annotations.SerializedName

@Keep
data class TickerChooseAccount(
    @SerializedName("end_time")
    val endTime: String? = null,

    @SerializedName("ticker_message")
    val tickerMessage: String? = null,

    @SerializedName("show_on_cash_withdraw")
    val showOnCashWithdraw: Boolean = false,

    @SerializedName("show_on_transfer")
    val showOnTransfer: Boolean = false,

    @SerializedName("show_on_check_balance")
    val showOnCheckBalance: Boolean = false
) {
    fun shouldShow(transactionType: String): Boolean {
        val currentTime = System.currentTimeMillis()
        val parsedEndTime = Utils.getTime(endTime)

        if (currentTime >= parsedEndTime) {
            return false
        }

        return when (transactionType) {
            TransactionType.CASH_WITHDRAWAL.type -> showOnCashWithdraw
            TransactionType.TRANSFER_INQUIRY.type,
            TransactionType.TRANSFER_POSTING.type -> showOnTransfer

            else -> showOnCheckBalance
        }
    }
}
