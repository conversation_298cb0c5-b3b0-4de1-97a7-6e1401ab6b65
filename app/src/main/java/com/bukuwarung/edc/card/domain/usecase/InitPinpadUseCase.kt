package com.bukuwarung.edc.card.domain.usecase

import com.bukuwarung.edc.card.data.model.PinpadButtonCoordinates
import com.bukuwarung.edc.card.domain.service.IEdcDeviceService
import javax.inject.Inject

class InitPinpadUseCase @Inject constructor(private val deviceService: IEdcDeviceService) {
    operator fun invoke(
        cardNumber: String,
        pinpadButtonCoordinates: List<PinpadButtonCoordinates>
    ) = deviceService.initPinpad(cardNumber, pinpadButtonCoordinates)
}
