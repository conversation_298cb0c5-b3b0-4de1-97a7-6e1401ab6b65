package com.bukuwarung.edc.card.domain.model

import com.google.errorprone.annotations.Keep

sealed class EdcTransactionResponse<out T> {

    @Keep
    data class Success<out T>(val data: T) : EdcTransactionResponse<T>()

    @Keep
    data class Failure(
        val type: FailureType,
        val code: String,
        val message: String?,
        val data: String? = "",
        val stan: String? = ""
    )
}

enum class FailureType {
    DEVICE_CONFIRMATION_FAILURE,
    TRANSACTION_FAILURE,
    HTTP_FAILURE
}
