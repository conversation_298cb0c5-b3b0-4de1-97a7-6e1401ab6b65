package com.bukuwarung.edc.card.data.repository

import com.bukuwarung.bluetooth_printer.BluetoothDevices
import com.bukuwarung.edc.card.data.datasource.TerminalApi
import com.bukuwarung.edc.card.data.model.ActivationRequest
import com.bukuwarung.edc.card.data.model.ActivationResponse
import com.bukuwarung.edc.card.data.model.Terminal
import com.bukuwarung.edc.card.data.model.TerminalLocation
import com.bukuwarung.edc.card.data.model.TerminalPairedDevice
import com.bukuwarung.edc.card.data.model.TmsResponse
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.app.config.AppConfig
import javax.inject.Inject
import retrofit2.Response

class TerminalManagementRepository @Inject constructor(private val api: TerminalApi) {

    suspend fun activateTerminal(
        serialNumber: String,
        appDeviceId: String,
        fcmToken: String
    ): Response<TmsResponse<ActivationResponse>> =
        api.activateTerminal(serialNumber, appDeviceId, ActivationRequest(fcmToken))

    suspend fun updateTerminalStatus(
        serialNumber: String,
        status: String,
        appDeviceId: String
    ): Response<TmsResponse<Terminal>> =
        api.updateTerminalStatus(serialNumber, mapOf("status" to status), appDeviceId)

    suspend fun getTerminal(serialNumber: String): Response<TmsResponse<Terminal>> =
        api.getTerminal(serialNumber)

    suspend fun updateTerminal(
        serialNumber: String,
        terminal: Terminal
    ): Response<TmsResponse<Terminal>> {
        if (Utils.getMasterKey().isNullOrEmpty() || Utils.getConnectedDeviceMasterKey(serialNumber)
                .isNullOrEmpty()
        ) {
            return api.updateTerminal(serialNumber, terminal, true)
        } else {
            try {
                Utils.setTerminalMasterKey(Utils.getConnectedDeviceMasterKey(serialNumber))
            } catch (e: Exception) {
                e.printStackTrace()
            }
            return api.updateTerminal(
                serialNumber,
                terminal,
                AppConfig.current.variantConfig.shouldReactivateTerminal
            )
        }
    }

    suspend fun updateTerminalLocation(
        serialNumber: String,
        terminalLocation: TerminalLocation
    ): Response<TmsResponse<TerminalLocation>> =
        api.updateTerminalLocation(serialNumber, terminalLocation)

    suspend fun pairBluetoothDevice(
        serialNumber: String,
        appDeviceId: String,
        androidId: String,
        btName: String,
        btAddress: String
    ): Response<TmsResponse<TerminalPairedDevice>> =
        api.pairBluetoothDevice(serialNumber, appDeviceId, androidId, btName, btAddress)

    fun getPairedDeviceMacAddress(): String = BluetoothDevices.getPairedCardReader() ?: ""
}
