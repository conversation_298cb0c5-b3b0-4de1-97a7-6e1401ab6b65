package com.bukuwarung.edc.card.data.service.pax

import android.os.Bundle
import android.os.RemoteException
import android.util.Log
import com.bukuwarung.edc.card.data.model.PinpadButtonCoordinates
import com.bukuwarung.edc.card.domain.model.PinpadResult
import com.bukuwarung.edc.card.domain.model.consts.Constants
import com.bukuwarung.edc.util.Utils
import com.pax.p_service.aidl.PinInputListener
import com.pax.p_service.aidl.PinKeyCoorInfo
import javax.inject.Inject
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.callbackFlow

class PaxPinpadFlow @Inject constructor(private val edcDeviceService: PaxDeviceServiceImpl) {

    fun initPinpad(cardNumber: String, pinpadButtonCoordinates: List<PinpadButtonCoordinates>) =
        callbackFlow {
            Log.d(TAG, "start check card")

            try {
                val pinInputListener: PinInputListener = object : PinInputListener.Stub() {
                    @Throws(RemoteException::class)
                    override fun onInput(len: Int, key: Int) {
                        // the key is * always.
                        Log.d("TAG", "onInput, length:$len,key:$key")
                        var data = Bundle()
                        data.putInt("len", len)
                        data.putInt("key", key)
                        val pinpadResult = PinpadResult(
                            status = Constants.PINPAD_STATUS_INPUT,
                            data = data
                        )
                        trySend(pinpadResult)
                    }

                    @Throws(RemoteException::class)
                    override fun onConfirm(pinblockByte: ByteArray, isNonePin: Boolean) {
                        Log.d("TAG", "onConfirm")
                        var data = Bundle()
                        data.putString("pin_block", Utils.byte2HexStr(pinblockByte))
                        val pinpadResult = PinpadResult(
                            status = Constants.PINPAD_STATUS_CONFIRM,
                            data = data
                        )
                        trySend(pinpadResult)
                    }

                    @Throws(RemoteException::class)
                    override fun onCancel() {
                        Log.d("TAG", "onCancel")
                        val pinpadResult = PinpadResult(
                            status = Constants.PINPAD_STATUS_CANCEL,
                            data = Bundle()
                        )
                        trySend(pinpadResult)
                    }

                    @Throws(RemoteException::class)
                    override fun onError(errorCode: Int) {
                        Log.d("TAG", "onError")
                        var errorData = Bundle()
                        errorData.putInt("error_code", errorCode)
                        val pinpadResult = PinpadResult(
                            status = Constants.PINPAD_STATUS_ERROR,
                            data = errorData
                        )
                        trySend(pinpadResult)
                    }
                }
                val param = Bundle()
                val pinLimit = byteArrayOf(0, 6)
                param.putByteArray("pinLimit", pinLimit)
                param.putInt("timeout", 60)
                param.putBoolean("isOnline", true)
                param.putBoolean("isUnmute", true)
                param.putString("pan", cardNumber)
                param.putString("promptString", "please input pin:")
                param.putInt("desType", 1)
                val num = byteArrayOf(0, 1, 2, 3, 4, 5, 6, 7, 8, 9)
                param.putByteArray("displayKeyValue", num)
                var pinKeyCoorInfo: List<PinKeyCoorInfo> = pinpadButtonCoordinates.map {
                    PinKeyCoorInfo(
                        it.buttonName,
                        it.topLeftX,
                        it.topLeftY,
                        it.bottomRightX,
                        it.bottomRightY,
                        it.buttonType
                    )
                }
                val keyMap: Map<String, String> = edcDeviceService?.pinpad?.initPinInputCustomView(
                    1,
                    param,
                    pinKeyCoorInfo,
                    pinInputListener
                ) as Map<String, String>
                val pinpadResult = PinpadResult(
                    status = Constants.PINPAD_STATUS_INIT,
                    data = Bundle(),
                    keyMap = keyMap
                )
                trySend(pinpadResult)
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                awaitClose {
                    Log.d(TAG, "pinpad flow close")
                }
            }
        }

    companion object {
        const val TAG = "EDC_SEARCH_CARD"
    }
}
