package com.bukuwarung.edc.card.data.service.bluetooth

import PinBlockEncryptionUtils
import android.content.Context
import android.graphics.Bitmap
import android.os.Bundle
import android.util.Log
import com.bukuwarung.cardreader.CardReaderHelper
import com.bukuwarung.edc.card.data.model.PinpadButtonCoordinates
import com.bukuwarung.edc.card.domain.model.EdcResponse
import com.bukuwarung.edc.card.domain.model.PinpadResult
import com.bukuwarung.edc.card.domain.model.ReceiptPrintCommand
import com.bukuwarung.edc.card.domain.service.CardReaderResponse
import com.bukuwarung.edc.card.domain.service.EmvResponse
import com.bukuwarung.edc.card.domain.service.IEdcDeviceService
import com.bukuwarung.edc.card.domain.service.LoadKeyResponse
import com.bukuwarung.edc.card.domain.service.PinBlockResponse
import com.bukuwarung.edc.card.domain.service.PrinterResponse
import com.bukuwarung.edc.card.domain.service.TransactionValidationResult
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.util.CustomBeeper
import com.bukuwarung.edc.util.Utils
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.flow.Flow

@Singleton
class ExternalCardReaderServiceImpl @Inject constructor(val context: Context) : IEdcDeviceService {

    var isInitiated: Boolean = false
    var isConnected: Boolean = false

    override fun connectService(): Boolean = true

    override fun startCheckCard(timeOut: Int): Flow<CardReaderResponse> {
        TODO("Not yet implemented")
    }

    override fun stopCheckCard() = CheckCardFlow(this).stopCheckCard()

    override fun printReceipt(
        printCommand: ReceiptPrintCommand,
        header: ByteArray?,
        bukuAgen: ByteArray?,
        headerBitmap: Bitmap?,
        bukuAgenBitmap: Bitmap?,
        showFooterLogo: Boolean
    ): Flow<PrinterResponse> = TianyuReceiptPrinterFlow(
        this,
        printCommand,
        header,
        bukuAgen,
        headerBitmap,
        bukuAgenBitmap,
        showFooterLogo
    ).printReceipt() as Flow<PrinterResponse>

    override fun printPaymentReceipt(paymentReceipt: String): Flow<PrinterResponse> {
        TODO("Not yet implemented")
    }

    override fun abortEmv() {
        // not required
    }

    override fun updateAID(operation: Int, aidType: Int, aid: String?): Boolean {
        if (Utils.hasLoadedConfig()) return true
        val result = CardReaderHelper.getInstance().updateEmvConfig() == true
        Utils.setLoadedConfig(result)
        return result
    }

    override fun updateRID(operation: Int, rid: String?): Boolean {
        if (Utils.hasLoadedConfig()) return true
        val result = (CardReaderHelper.getInstance().updateRid() == true)
        Utils.setLoadedConfig(result)
        return result
    }

    override fun startInsertCardRead(emvIntent: Bundle, timeout: Int): Flow<EmvResponse> {
        TODO("Not yet implemented")
    }

    override fun startPrint(): Flow<PrinterResponse> {
        TODO("Not yet implemented")
    }

    override fun printTestSlip(): Flow<PrinterResponse> {
        TODO("Not yet implemented")
    }

    override fun getEmvTagData(tags: Array<String>): String = ""

    override fun checkCardAvailability(timeOut: Long): Flow<EdcResponse.CardAvailable> =
        CheckCardFlow(this).checkCardAvailability(timeOut)

    override fun stopPinpadPinInput() {
        TODO("Not yet implemented")
    }

    override fun startPinpadPinInput() {
        TODO("Not yet implemented")
    }

    override fun initPinpad(
        cardNumber: String,
        pinKeyCoordinate: List<PinpadButtonCoordinates>
    ): Flow<PinpadResult> {
        TODO("Not yet implemented")
    }

    override fun doBeep(durationMills: Int) {
        if (Utils.sharedPreferences.getBoolean(Utils.TRANSACTION_SOUND, false)) {
            CustomBeeper(context).playCustomBeep()
        }
    }

    /*
    masterKey -> TMK
    workingKey -> TWK/TPK
     */

    override fun loadKeys(masterKey: String, workKey: String): LoadKeyResponse {
        var isTMKSuccess = false
        var isWorkkeySuccess = false
        val props = HashMap<String, String>()
        try {
            if (Utils.getMasterKey().isNullOrEmpty()) {
                props["flow"] = "empty"
                isTMKSuccess = true
            } else {
                props["flow"] = "reload"
                isTMKSuccess = CardReaderHelper.getInstance()?.updateMainKey(masterKey) == true
            }
            Log.d("loadMainKey", isTMKSuccess.toString())
        } catch (e: Exception) {
            e.printStackTrace()
        }
        try {
            isWorkkeySuccess = CardReaderHelper?.getInstance()?.updateWorkingKey(workKey) == true
            Log.d("loadWorkKeys", "updateWorkingKey： $isWorkkeySuccess")
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
        try {
            props["work_key"] = isWorkkeySuccess.toString()
            props["main_key"] = isTMKSuccess.toString()
            props["main_key_len"] = masterKey.length.toString()
            props["work_key_len"] = workKey.length.toString()
            Analytics.trackEventMobile("update_work_key_service", props)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        if (isWorkkeySuccess && isTMKSuccess) {
            return EdcResponse.Success(true)
        } else {
            return EdcResponse.Success(false)
        }
    }

    override fun getPinBlock(panNumber: String, pin: String): PinBlockResponse {
        val result: String? = PinBlockEncryptionUtils.encryptPinFormat0(
            pin,
            panNumber,
            Utils.getMasterKey(),
            Utils.getWorkKey()
        )
        return EdcResponse.Success(result)
    }

    override fun importCardConfirmResult(isConfirm: Boolean) {
        // not required for bluetooth card reader
    }

    override fun importPin(pinBlock: String) {
        // not required for bluetooth card reader
    }

    override fun inputOnlineResult(
        responseCode: String,
        field55IccData: String
    ): Flow<TransactionValidationResult> =
        DeviceEmvFlow(this).inputOnlineResult(responseCode, field55IccData)

    override fun loadMasterKey(masterKey: String): Boolean {
        var isMasterKeyloaded = false
        try {
            isMasterKeyloaded = CardReaderHelper.getInstance().updateMainKey(masterKey) == true
            Log.d("loadMainKey", isMasterKeyloaded.toString())
        } catch (e: Exception) {
            e.printStackTrace()
        }
        val map = HashMap<String, String>()
        map["type"] = "master-key"
        map["status"] = isMasterKeyloaded.toString()
        Analytics.trackEvent("device_activation", map)
        return isMasterKeyloaded
    }

    companion object {
        const val TAG = "TNDeviceServiceImpl"
    }
}
