package com.bukuwarung.edc.card.data.util

import android.os.Bundle
import android.os.RemoteException
import android.text.TextUtils
import android.util.Log
import com.bukuwarung.analytic.BtAnalytics.bwLog
import com.bukuwarung.edc.card.domain.model.EdcResponse
import com.bukuwarung.edc.card.domain.model.OnlineTransactionResult
import com.bukuwarung.edc.card.domain.model.consts.Constants
import com.bukuwarung.edc.device.DeviceHelper
import com.bukuwarung.edc.settings.ui.setting.MiniatmDebugSettingActivity
import com.bukuwarung.edc.util.HexUtil
import com.bukuwarung.edc.util.Utils
import com.morefun.yapi.device.pinpad.DukptCalcObj
import com.morefun.yapi.device.pinpad.DukptCalcObj.DukptAlgEnum
import com.morefun.yapi.device.pinpad.DukptCalcObj.DukptKeyIndexEnum
import com.morefun.yapi.device.pinpad.DukptCalcObj.DukptOperEnum
import com.morefun.yapi.device.pinpad.DukptCalcObj.DukptTypeEnum
import com.morefun.yapi.device.pinpad.PinPadType
import com.morefun.yapi.emv.EmvDataSource
import com.morefun.yapi.emv.EmvTermCfgConstrants
import com.morefun.yapi.emv.EmvTransDataConstrants
import com.vfi.smartpos.deviceservice.constdefine.ConstIPBOC
import com.vfi.smartpos.deviceservice.constdefine.ConstOnlineResultHandler
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import kotlin.math.min

object EmvUtil {

    val TAG: String = "EMV-${EmvUtil::class.java.name}"

    fun getOnlineDataValidationResult(
        result: Int,
        data: Bundle?
    ): EdcResponse.Success<OnlineTransactionResult>? {
        Log.d("EMV_TRANS", "inputOnlineResult -> onProccessResult: $result $data")
        val str = """
                            RESULT:$result
                            TC_DATA:${
            data!!.getString(
                ConstOnlineResultHandler.onProccessResult.data.KEY_TC_DATA_String,
                "not defined"
            )
        }
                            SCRIPT_DATA:${
            data.getString(
                ConstOnlineResultHandler.onProccessResult.data.KEY_SCRIPT_DATA_String,
                "not defined"
            )
        }
                            REVERSAL_DATA:${
            data.getString(
                ConstOnlineResultHandler.onProccessResult.data.KEY_REVERSAL_DATA_String,
                "not defined"
            )
        }
        """.trimIndent()
        Log.d("EMV_TRANS", "inputOnlineResult -> onProccessResult: $result $str")
        var resultCode = result
        if (Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_MOCK_API)) {
            // mock api icc data is always invalid, to continue transaction process device verification has to be hardcodeed to success
            resultCode = ConstOnlineResultHandler.onProccessResult.result.TC
            if (Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_FORCE_INCOMPLETE)) {
                // intentionally break the flow
                return null
            } else if (Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_CORRUPT_ICC)) {
                // intentionally break the flow
                resultCode = ConstOnlineResultHandler.onProccessResult.result.Online_AAC
            }
        } else if (Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_REVERSAL)) {
            resultCode = ConstOnlineResultHandler.onProccessResult.result.Online_AAC
        } else if (Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_FORCE_INCOMPLETE)) {
            // intentionally break the flow
            return null
        }
        // since device has validated backend response, transaction can be removed from incomplete state
        Utils.clearIncompleteTransaction()
        if (resultCode != ConstOnlineResultHandler.onProccessResult.result.TC) {
            data.putString(
                Constants.KEY_VALIDATION_ERROR_CODE,
                Constants.DEVICE_TRANSACTION_VALIDATION_ERROR
            )
        }
        return EdcResponse.Success(
            OnlineTransactionResult(
                resultCode,
                data,
                if (result ==
                    ConstOnlineResultHandler.onProccessResult.result.TC
                ) {
                    "online result TC(success)"
                } else {
                    "online result refused"
                }
            )
        )
    }

    fun getInitialTermConfig(): Bundle {
        val bundle = Bundle()
        bundle.putByteArray(
            EmvTermCfgConstrants.TERMCAP,
            byteArrayOf(0x20.toByte(), 0x40.toByte(), 0x00.toByte())
        )
        bundle.putByteArray(
            EmvTermCfgConstrants.ADDTERMCAP,
            byteArrayOf(0xE0.toByte(), 0x00.toByte(), 0xF0.toByte(), 0xA0.toByte(), 0x01.toByte())
        )
        bundle.putByteArray(
            EmvTermCfgConstrants.ADD_TERMCAP_EX,
            byteArrayOf(0xE0.toByte(), 0x00.toByte(), 0xF0.toByte(), 0xA0.toByte(), 0x01.toByte())
        )
        bundle.putByte(EmvTermCfgConstrants.TERMTYPE, 0x22.toByte())
        bundle.putByteArray(
            EmvTermCfgConstrants.COUNTRYCODE,
            byteArrayOf(0x03.toByte(), 0x60.toByte())
        )
        bundle.putByteArray(
            EmvTermCfgConstrants.CURRENCYCODE,
            byteArrayOf(0x03.toByte(), 0x60.toByte())
        )
        bundle.putByteArray(
            EmvTermCfgConstrants.TRANS_PROP_9F66,
            byteArrayOf(0x34.toByte(), 0x00.toByte(), 0x00.toByte(), 0x80.toByte())
        )
        bundle.putByteArray(
            EmvTermCfgConstrants.REF_CURRCODE_N_9F3C,
            byteArrayOf(0x03.toByte(), 0x60.toByte())
        )
        bundle.putByte(
            EmvTermCfgConstrants.REF_CURREXP_N_9F3D,
            0x02.toByte()
        )
        return bundle
    }

    fun getTransBundle(amount: String?, bundle: Bundle): Bundle {
        val date: String = getCurrentTime("yyMMddHHmmss")

        bundle.putBoolean(EmvTransDataConstrants.FORCE_ONLINE_CALL_PIN, true)
        bundle.putBoolean(EmvTransDataConstrants.EMV_TRANS_ENABLE_CONTACTLESS, true)
        bundle.putBoolean(EmvTransDataConstrants.EMV_TRANS_ENABLE_CONTACT, true)
        bundle.putBoolean(EmvTransDataConstrants.CONTACT_SERVICE_SWITCH, false)
        bundle.putBoolean(EmvTransDataConstrants.SELECT_APP_RETURN_AID, false)
        bundle.putBoolean(EmvTransDataConstrants.SELECT_APP_RETURN_PRIORITY, true)
        // bundle.putString(EmvTransDataConstrants.PROCTYPE, "30") //40 / 30 is balance

        bundle.putInt(EmvTransDataConstrants.CHECK_CARD_TIME_OUT, 30)
        bundle.putInt(EmvTransDataConstrants.ISQPBOCFORCEONLINE, 1)

        // trans trype 30 is balance, and 40 is TF
        try {
            val type = bundle.getByte(ConstIPBOC.startEMV.intent.KEY_transProcessCode_byte)
            if (type == 0x39.toByte() || type == 0x40.toByte()) {
                bundle.putByte(EmvTransDataConstrants.B9C, 0x40.toByte())
            } else {
                bundle.putByte(EmvTransDataConstrants.B9C, 0x30.toByte())
            }
        } catch (e: Exception) {
            Log.e(TAG, "bundle don't have trx type!")
            bundle.putByte(EmvTransDataConstrants.B9C, 0x30.toByte())
        }

        bundle.putString(EmvTransDataConstrants.TRANSDATE, date.substring(0, 6))
        bundle.putString(EmvTransDataConstrants.TRANSTIME, date.substring(6, 12))
        bundle.putString(EmvTransDataConstrants.TRANSAMT, amount)
        bundle.putString(EmvTransDataConstrants.MERID, Utils.getMerchantId())
        bundle.putString(EmvTransDataConstrants.TERMID, Utils.getTerminalId())

        return bundle
    }

    fun getCurrentTime(format: String?): String {
        val df = SimpleDateFormat(format)
        val curDate = Date(System.currentTimeMillis())
        return df.format(curDate)
    }

    fun readPan(): String? {
        var pan: String? = getPbocData("5A", true)
        if (TextUtils.isEmpty(pan)) {
            pan = getPanFromTrack2()
        }
        if (pan?.endsWith("F") == true) {
            return pan.substring(0, pan.length - 1)
        }
        return pan
    }

    fun getPbocData(tagName: String, isHex: Boolean): String? {
        try {
            val data = ByteArray(512)
            Log.d(TAG, "getPbocData Tag:$tagName")
            val bundle = Bundle()
            bundle.putInt(DukptCalcObj.Param.DUKPT_KEY_INDEX, 0)

            val len: Int = DeviceHelper.getEmvHandler().readEmvData(
                arrayOf(
                    tagName.uppercase(
                        Locale.getDefault()
                    )
                ),
                data,
                null
            )

            if (len > 0) {
                val tlvData: TlvData = TlvData.fromRawData(HexUtil.subByte(data, 0, len), 0)
                return if (isHex) {
                    tlvData.getValue()
                } else {
                    tlvData.getGBKValue()
                }
            }
            return null
        } catch (e: RemoteException) {
            e.printStackTrace()
        }
        return null
    }

    private fun getPanFromTrack2(): String? {
        val track2: String? = readTrack2()
        if (track2 != null) {
            for (i in track2.indices) {
                if (track2[i] == '=' || track2[i] == 'D') {
                    val endIndex = min(i.toDouble(), 19.0).toInt()
                    return track2.substring(0, endIndex)
                }
            }
        }
        return null
    }

    fun readTrack2(): String? {
        var track2 = getPbocData(EmvDataSource.GET_TRACK2_TAG_6B, true)
            ?: getPbocData(EmvDataSource.GET_TRACK2_TAG_C2, true)
        getValidTrack2DataOrNull(track2 ?: "")?.let {
            return it
        }
        // track2data need to decrypt
        try {
            val alg = DukptAlgEnum.DUKPT_ALG_CBC
            val oper = DukptOperEnum.DUKPT_DECRYPT
            val type = DukptTypeEnum.DUKPT_DES_KEY_DATA1

            bwLog(
                msg = buildString {
                    append("PlainTrack2:")
                    append(track2)
                    append(", alg:")
                    append(alg)
                    append(", oper:")
                    append(oper)
                    append(", type:")
                    append(type)
                    append(", DukptKeyIndexEnum.KEY_INDEX_0:")
                    append(DukptKeyIndexEnum.KEY_INDEX_0)
                }
            )
            val dukptCalcObj = DukptCalcObj(
                DukptKeyIndexEnum.KEY_INDEX_0,
                type,
                oper,
                alg,
                track2
            )

            val bundle = DeviceHelper.getPinpad().dukptCalcDes(dukptCalcObj)
            bwLog(msg = "DUKPT Calculation Finished: bundle = $bundle")
            track2 = bundle.getString(DukptCalcObj.DUKPT_DATA)
        } catch (e: Exception) {
            bwLog(e)
        }

        if (!TextUtils.isEmpty(track2) && track2!!.endsWith("F")) {
            return removeTrailingF(track2)
        }
        return track2
    }

    private fun removeTrailingF(data: String?): String? {
        if (data.isNullOrEmpty()) {
            return data
        }
        return data.replace("F+$".toRegex(), "")
    }

    fun getServiceCodeFromTrack2(track2: String?): String {
        var serviceCode24 = ""
        if (track2.isNullOrEmpty()) {
            return ""
        }
        for (i in 0 until track2.length) {
            if (track2[i] == '=' || track2[i] == 'd' || track2[i] == 'D') {
                serviceCode24 = track2.substring(i + 5, i + 5 + 3)
                break
            }
        }
        return serviceCode24
    }

    fun expDate(): String? {
        val value = getPbocData("5F24", true)
        return value?.substring(0, 4)
    }

    fun cardSn(): String? = getPbocData("5F34", true)

    fun getPinBlock() {
        try {
            val type = PinPadType.SEC_DUKPT_FIELD
            val pinFormat = PinPadType.SEC_PIN_FORMAT0
            val keyId = 0 // getKeyIndex()
            val pan = "1234567890123456".toByteArray()
            val pin = "123456".toByteArray()
            val pinBlock = ByteArray(8)

            val ret =
                DeviceHelper.getPinpad().getPinBlock(type, pinFormat, keyId, pan, pin, pinBlock)
            Log.i(TAG, "getPinBlock: $ret")
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun getTLVDatas(tags: Array<String>): String? {
        for (i in tags.indices) {
            tags[i] = tags[i].uppercase(Locale.getDefault())
        }
        try {
            val buffer = ByteArray(3096)

            val bundle = Bundle()
            bundle.putInt(DukptCalcObj.Param.DUKPT_KEY_INDEX, 0)

            val byteNum = DeviceHelper.getEmvHandler().readEmvData(tags, buffer, bundle)
            return if (byteNum > 0) {
                HexUtil.bytesToHexString(HexUtil.subByte(buffer, 0, byteNum))
            } else {
                ""
            }
        } catch (e: RemoteException) {
            e.printStackTrace()
        }
        return null
    }

    private fun getValidTrack2DataOrNull(data: String): String? {
        var track2 = data
        if (!TextUtils.isEmpty(track2) && track2.endsWith("F")) {
            track2 = removeTrailingF(data) ?: ""
        }
        // find valid separator: 'D' or '='
        val separator = when {
            track2.contains('D') -> 'D'
            track2.contains('=') -> '='
            else -> return null
        }

        val parts = track2.split(separator)
        if (parts.size != 2) return null

        val pan = parts[0]
        val secondPart = parts[1]

        // Validate PAN: 13-19 digit
        if (pan.length !in 13..19 || !pan.all { it.isDigit() }) return null

        // Validate expiration date and service code
        if (secondPart.length < 7) return null

        val expDate = secondPart.substring(0, 4)
        val serviceCode = secondPart.substring(4, 7)

        if (!expDate.all { it.isDigit() } || !serviceCode.all { it.isDigit() }) return null

        return track2
    }
}
