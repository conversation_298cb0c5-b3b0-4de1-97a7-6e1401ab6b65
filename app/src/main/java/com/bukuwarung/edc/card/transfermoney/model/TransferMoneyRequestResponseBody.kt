package com.bukuwarung.edc.card.transfermoney.model

import android.os.Parcelable
import com.bukuwarung.edc.card.data.model.ErrorBody
import com.google.errorprone.annotations.Keep
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
@Keep
data class TransferMoneyRequestResponseBody(
    @field:SerializedName("track_2_data")
    val track2Data: String? = null,
    @field:SerializedName("account_type")
    val accountType: String? = null,
    @field:SerializedName("amount")
    val amount: String? = null,
    @field:SerializedName("notes")
    var notes: String? = null,
    @field:SerializedName("pos_entry_mode")
    val posEntryMode: String? = null,
    @field:SerializedName("card_expiry")
    val cardExpiry: String? = null,
    @field:SerializedName("pin_block")
    val pinBlock: String? = null,
    @field:SerializedName("processing_code")
    val processingCode: String? = null,
    @field:SerializedName("system_trace_audit_number")
    val stan: String? = null,
    @field:SerializedName("account_id")
    val accountId: String? = null,
    @field:SerializedName("transaction_time")
    val transactionTime: String? = null,
    @field:SerializedName("transaction_date")
    val transactionDate: String? = null,
    @field:SerializedName("rrn")
    val rrn: String? = null,
    @field:SerializedName("response_code")
    val responseCode: String? = null,
    @field:SerializedName("card_number")
    val cardNumber: String? = null,
    @field:SerializedName("terminal_id")
    val terminalId: String? = null,
    @field:SerializedName("merchant_id")
    val merchantId: String? = null,
    @field:SerializedName("source_details")
    val sourceDetails: BankDetails? = null,
    @field:SerializedName("dest_details")
    val destDetails: BankDetails? = null,
    @field:SerializedName("icc_data")
    val iccData: String? = null,
    @field:SerializedName("invoice_number")
    val invoiceNumber: String? = null,
    @SerializedName("error")
    val errorBody: ErrorBody? = null
) : Parcelable

@Parcelize
@Keep
data class BankDetails(

    @SerializedName("bank_code")
    val bankCode: String? = null,

    @SerializedName("name")
    val name: String? = null,

    @SerializedName("bank_name")
    val bankName: String? = null,

    @SerializedName("account_number")
    val accountNumber: String? = null
) : Parcelable
