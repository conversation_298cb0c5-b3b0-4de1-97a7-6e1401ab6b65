package com.bukuwarung.edc.card.cashwithdrawal.ui.activity

import android.widget.PopupMenu
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import com.bukuwarung.bluetooth_printer.base.BaseActivity
import com.bukuwarung.edc.R
import com.bukuwarung.edc.app.config.VariantConfig
import com.bukuwarung.edc.card.cashwithdrawal.ui.activity.AddSettlementBankAccountActivity.Companion.ENTRY_POINT
import com.bukuwarung.edc.card.cashwithdrawal.viewmodel.CashWithdrawAddBankAccountViewModel
import com.bukuwarung.edc.card.constant.PrintConst
import com.bukuwarung.edc.databinding.ActivitySettlementAccountBinding
import com.bukuwarung.edc.ppob.common.view.PaymentDownBottomSheet
import com.bukuwarung.edc.util.ToastUtil
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.getDrawableCompat
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.openActivityForResult
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick
import com.bumptech.glide.Glide
import com.google.android.material.snackbar.Snackbar
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import kotlinx.coroutines.flow.collectLatest

@AndroidEntryPoint
class SettlementAccountActivity : BaseActivity() {

    @Inject
    lateinit var variantConfig: VariantConfig

    private lateinit var binding: ActivitySettlementAccountBinding
    private val entryPoint by lazy { intent.getStringExtra(ENTRY_POINT) }

    private val viewModel: CashWithdrawAddBankAccountViewModel by viewModels()
    override fun setViewBinding() {
        binding = ActivitySettlementAccountBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {
        setUpToolbar()
        binding.ivMore.singleClick {
            showPopupMenu()
        }

        if (!variantConfig.shouldShowSettlementMenu) {
            viewModel.processIntent(
                CashWithdrawAddBankAccountViewModel
                    .AddSettlementBankAccountIntent
                    .GetSettlementBankList
            )
        } else {
            getBankAccount()
        }
    }

    private fun getBankAccount() {
        val getSettlementBankList = CashWithdrawAddBankAccountViewModel
            .AddSettlementBankAccountIntent
            .GetSettlementBankList
        val getRnLWithdrawalBankList = CashWithdrawAddBankAccountViewModel
            .AddSettlementBankAccountIntent
            .GetRnLWithdrawalBankList
        when (entryPoint) {
            WithdrawAddBankAccountEntryPoint.CASH_WITHDRAWAL_ACCOUNT.value -> {
                viewModel.processIntent(getSettlementBankList)
            }

            WithdrawAddBankAccountEntryPoint.RNL_WITHDRAWAL_ACCOUNT.value -> {
                viewModel.processIntent(getRnLWithdrawalBankList)
            }
        }
    }

    private fun showPopupMenu() {
        val popupMenu = PopupMenu(this, binding.ivMore)
        popupMenu.menuInflater.inflate(R.menu.change_bank_account_menu, popupMenu.menu)
        popupMenu.setOnMenuItemClickListener { item ->
            when (item.itemId) {
                R.id.change_bank_account -> {
                    openActivityForResult(
                        AddSettlementBankAccountActivity::class.java,
                        bankAddedActivityForResult
                    ) {
                        putString(ENTRY_POINT, entryPoint)
                    }
                    true
                }

                else -> false
            }
        }
        popupMenu.show()
    }

    private val bankAddedActivityForResult =
        registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result: ActivityResult ->
            if (result.resultCode == RESULT_OK) {
                getBankAccount()
                ToastUtil.setToast(
                    this,
                    PrintConst.ALERT_INFO,
                    getString(R.string.successfully_edited_bank_account),
                    this.findViewById(R.id.tvTitle),
                    Snackbar.LENGTH_SHORT
                )
            }
        }

    private fun setUpToolbar() {
        with(binding.tbProfile) {
            navigationIcon =
                <EMAIL>(R.drawable.ic_back)
            setNavigationOnClickListener {
                Utils.hideKeyboard(this@SettlementAccountActivity)
                finish()
            }
        }
    }

    override fun subscribeState() {
        lifecycleScope.launchWhenStarted {
            viewModel.viewEvent.collectLatest { event ->
                when (event) {
                    is CashWithdrawAddBankAccountViewModel.Event.BankListLoaded -> {
                        event.bankList.firstOrNull()?.let {
                            binding.gpSettlementBank.showView()
                            binding.emptyLayout.root.hideView()
                            binding.progressBar.hideView()
                            binding.tvBankHolderName.showView()
                            binding.tvBankHolderName.text =
                                it.bankName.plus("-").plus(it.accountHolderName)
                            binding.tvBankAccountNumber.text = it.accountNumber
                            Glide.with(this@SettlementAccountActivity)
                                .load(it.bankLogo.orEmpty())
                                .placeholder(R.drawable.ic_bank)
                                .error(R.drawable.ic_bank)
                                .into(binding.ivBankLogo)
                        } ?: run {
                            showEmptyScreen(getString(R.string.no_settlement_account_pls_add))
                        }
                    }

                    is CashWithdrawAddBankAccountViewModel.Event.ShowErrorMessage -> {
                        binding.gpSettlementBank.hideView()
                        binding.progressBar.hideView()
                        showEmptyScreen(getString(R.string.some_error_occurred_try_after_sometime))
                        ToastUtil.setToast(
                            this@SettlementAccountActivity,
                            PrintConst.ALERT_ERROR,
                            event.message.orEmpty(),
                            binding.tbProfile
                        )
                    }

                    is CashWithdrawAddBankAccountViewModel.Event.ShowBottomSheet -> {
                        showPaymentDownBottomSheet(false)
                        showEmptyScreen("some error occurred,pls try after some time.")
                    }

                    else -> {
                    }
                }
            }
        }
        lifecycleScope.launchWhenStarted {
            viewModel.bankViewStateFlow.collectLatest { bankViewState ->
                binding.progressBar.visibility = bankViewState.bankListLoader.asVisibility()
            }
        }
    }

    private fun showEmptyScreen(message: String?) {
        binding.gpSettlementBank.hideView()
        binding.progressBar.hideView()
        binding.emptyLayout.root.showView()
        binding.emptyLayout.tvEmptyTitle.text = getString(R.string.no_settlement_bank_account)
        binding.emptyLayout.tvEmptyMessage.text = message
        binding.emptyLayout.btnEmptyCta.hideView()
    }

    private fun showPaymentDownBottomSheet(isServiceDown: Boolean, message: String? = null) {
        val paymentDownBottomSheet = PaymentDownBottomSheet.createInstance(isServiceDown, message)
        paymentDownBottomSheet.show(supportFragmentManager, PaymentDownBottomSheet.TAG)
    }

    enum class WithdrawAddBankAccountEntryPoint(val value: String) {
        CASH_WITHDRAWAL_ACCOUNT("CASH_WITHDRAWAL_ACCOUNT"),
        RNL_WITHDRAWAL_ACCOUNT("EDC_REVENUE_SHARING")
    }
}
