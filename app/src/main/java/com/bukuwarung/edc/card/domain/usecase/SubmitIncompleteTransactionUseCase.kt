package com.bukuwarung.edc.card.domain.usecase

import androidx.annotation.Keep
import com.bukuwarung.edc.card.data.model.IncompleteTransactionRequest
import com.bukuwarung.edc.card.data.repository.EdcTransactionRepository
import com.bukuwarung.edc.card.domain.model.EdcTransactionResponse
import com.bukuwarung.edc.card.domain.model.FailureType
import com.bukuwarung.edc.card.ui.ErrorMapping
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.util.errorMessage
import com.google.gson.Gson
import javax.inject.Inject
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.callbackFlow

class SubmitIncompleteTransactionUseCase @Inject constructor(
    private val edcTransactionRepository: EdcTransactionRepository
) {
    suspend operator fun invoke(
        accountId: String,
        incompleteTransactionRequest: IncompleteTransactionRequest
    ) = callbackFlow {
        try {
            val response = edcTransactionRepository.submitIncompleteTransaction(
                accountId,
                incompleteTransactionRequest
            )
            if (response.isSuccessful) {
                trySend(EdcTransactionResponse.Success(response.body()))
            } else {
                val httpErrorCode = response.code()
                if (httpErrorCode == Constant.UNPROCESSABLE_ENTITY) {
                    val errorBody = response.errorBody()?.string()
                    val errorResponse = Gson().fromJson(errorBody, ErrorResponse::class.java)
                    val errorDetail = errorResponse.error

                    trySend(
                        EdcTransactionResponse.Failure(
                            type = FailureType.HTTP_FAILURE,
                            code = errorDetail.code,
                            message = errorDetail.message,
                            data = response.errorMessage()
                        )
                    )
                } else {
                    trySend(
                        EdcTransactionResponse.Failure(
                            type = FailureType.HTTP_FAILURE,
                            code = httpErrorCode.toString(),
                            message = response.message(),
                            data = response.errorMessage()
                        )
                    )
                }
            }
        } catch (e: Exception) {
            trySend(
                EdcTransactionResponse.Failure(
                    FailureType.HTTP_FAILURE,
                    ErrorMapping.noInternetErrorCode[0],
                    e.message
                )
            )
        } finally {
            awaitClose {
                // flow closed
            }
        }
    }
}

@Keep
data class ErrorResponse(val error: ErrorDetail)

@Keep
data class ErrorDetail(val code: String, val message: String)
