package com.bukuwarung.edc.card.domain.model

import com.bukuwarung.edc.card.constant.PrintConst
import com.bukuwarung.edc.card.constant.ReceiptType
import com.bukuwarung.edc.card.data.model.CardReceiptResponse
import com.bukuwarung.edc.card.domain.model.consts.Constants.CARD_ENTRY_MODE_IC
import com.bukuwarung.edc.card.ui.receipt.CardReceiptViewModel
import com.bukuwarung.edc.payments.data.model.OrderResponse
import com.bukuwarung.edc.util.Utils
import com.google.errorprone.annotations.Keep

@Keep
data class ReceiptPrintCommand(
    var receipt: CardReceiptResponse? = null,
    var maskedPan: String? = "",
    var notes: String? = "",
    var copy: ReceiptType? = ReceiptType.RECEIPT_TYPE_NONE,
    var printType: Int = PrintConst.TRANSACTION_TYPE_CHECK_BALANCE,
    var accountType: String = "",
    var destination: String = CardReceiptViewModel.Destination.TRANSACTION_FLOW.name,
    var cardEntryMode: Int = CARD_ENTRY_MODE_IC,
    var orderResponse: OrderResponse? = null,
    var terminalId: String? = Utils.getTerminalId()
)
