package com.bukuwarung.edc.card.domain.usecase

import android.util.Log
import com.bukuwarung.edc.card.domain.model.EmvApplicationParam
import com.bukuwarung.edc.card.domain.service.IEdcDeviceService
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.Utils.bwLog
import com.vfi.smartpos.deviceservice.constdefine.ConstIPBOC
import javax.inject.Inject

class ConfigureAidUseCase @Inject constructor(private val deviceService: IEdcDeviceService) {

    val tag = "ConfigureAidUseCase"

    operator fun invoke() {
        // clear AID
        Utils.setLoadedConfig(false)
        var status = deviceService.updateAID(3, 1, null)
        bwLog(tag, "Clear AID (smart AID):$status")

        status = deviceService.updateAID(3, 2, null)
        bwLog(tag, "Clear AID (CTLS):$status")

        if (Utils.getApplicationId().isNullOrEmpty()) {
            val appParamList: MutableList<EmvApplicationParam> = getApplicationParamList()
            for (emvAppParam in appParamList) {
                val tlvString = emvAppParam.tlvString
                status = deviceService.updateAID(
                    emvAppParam.flagAppendRemoveClear,
                    emvAppParam.aidType,
                    tlvString
                )
                bwLog(tag, "Set AID [$status] aid=$tlvString")
            }
        } else {
            val tmsAid = Utils.getApplicationId()
            status = deviceService.updateAID(
                ConstIPBOC.updateRID.operation.append,
                ConstIPBOC.updateAID.aidType.smart_card,
                tmsAid
            )
            bwLog(tag, "Set AID [$status] aid=[$tmsAid]")
        }

        Utils.setLoadedConfig(true)
    }

    private fun getApplicationParamList(): MutableList<EmvApplicationParam> {
        val appParamList: MutableList<EmvApplicationParam> = ArrayList<EmvApplicationParam>()
        // add config for atm
        appParamList.add(getAtmApplicationConfig())
        return appParamList
    }

    private fun getAtmApplicationConfig(): EmvApplicationParam {
        val emvApplicationParam = EmvApplicationParam()
        emvApplicationParam.flagAppendRemoveClear = ConstIPBOC.updateRID.operation.append
        emvApplicationParam.aidType = ConstIPBOC.updateAID.aidType.smart_card
        emvApplicationParam.comment = "NSICCS ATM/Debit"
        emvApplicationParam.append(EmvApplicationParam.TAG_AID_9F06, "A0000006021010")
        emvApplicationParam.append(EmvApplicationParam.TAG_APP_TERM_CAP_9F33, "E0F8C8")
        emvApplicationParam.append(EmvApplicationParam.TAG_APP_TERM_TYPE_9F35, "22")
        emvApplicationParam.append(EmvApplicationParam.TAG_ASI_DF01, "00")

        emvApplicationParam.append(EmvApplicationParam.TAG_DEFAULT_DDOL_DF14, "039F3704")
        emvApplicationParam.append(EmvApplicationParam.TAG_THRESHOLD_DF15, "00000000")
        emvApplicationParam.append(EmvApplicationParam.TAG_MAX_TARGET_PERCENTAGE_DF16, "70")
        emvApplicationParam.append(EmvApplicationParam.TAG_TARGET_PERCENTAGE_DF17, "70")
        // need to update based on merchant category
        emvApplicationParam.append(EmvApplicationParam.TAG_APP_TERM_ADD_CAP_9F40, "EC00F0A000")
//        emvApplicationParam.append(EmvApplicationParam.TAG_ECTransLimit_9F7B, "000000000000")
        emvApplicationParam.append(
            EmvApplicationParam.TAG_DEFAULT_TDOL_97_OPTIONAL,
            "0F9F02065F2A029A039C0195059F3704"
        )
//        emvApplicationParam.append(EmvApplicationParam.TAG__9F15_Optional, "202020202020202020202020202020")
        emvApplicationParam.append(EmvApplicationParam.TAG_FLOOR_LIMIT_9F1B, "00000000")
        emvApplicationParam.append(EmvApplicationParam.TAG_THRESHOLD_DF15, "00000000")
        emvApplicationParam.append(EmvApplicationParam.TAG_VER_NUM_9F09, "0100")
        emvApplicationParam.append(EmvApplicationParam.TAG_CURRENCY_CODE_TERM_5F2A, "0360")
        emvApplicationParam.append(EmvApplicationParam.TAG_COUNTRY_CODE_TERM_9F1A, "0360")
        emvApplicationParam.append(EmvApplicationParam.TAG_TAC_DEFAULT_DF11, "FC6024A800")
        emvApplicationParam.append(EmvApplicationParam.TAG_TAC_ONLINE_DF12, "FC60ACF800")
        emvApplicationParam.append(EmvApplicationParam.TAG_TAC_DENIAL_DF13, "0010000000")

        emvApplicationParam.append(EmvApplicationParam.TAG__DF18, "01")

        // don't remove below commented tags. These will be required for prod release
//        emvApplicationParam.append(EmvApplicationParam.TAG__DF18, "01");
//        emvApplicationParam.append(EmvApplicationParam.TAG_CTLSFloorLimit_DF19, "000000100000");
//        emvApplicationParam.append(EmvApplicationParam.TAG_CTLSTransLimit_DF20, "000999999999");
//        emvApplicationParam.append(EmvApplicationParam.TAG_CTLSCVMLimit_DF21, "000000100000");
        Log.d(
            tag,
            "emv app param for: $emvApplicationParam.comment aid: ${emvApplicationParam.tlvString}"
        )
        return emvApplicationParam
    }

    companion object {
        const val EMV_SHARED_PREF = "EMV_CONFIG"
    }
}
