package com.bukuwarung.edc.card.ui.edcdevices.model

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class DeviceListResponse(

    @field:SerializedName("result")
    val result: Boolean? = null,

    @field:SerializedName("data")
    val data: List<DeviceItem>? = null
)

@Keep
data class DeviceItem(

    @field:SerializedName("devicePlan")
    val devicePlan: String? = null,

    @field:SerializedName("bwUserId")
    val bwUserId: String? = null,

    @field:SerializedName("serialNumber")
    val serialNumber: String? = null,

    @field:SerializedName("merchantId")
    val merchantId: String? = null,

    @field:SerializedName("endDate")
    val endDate: String? = null,

    @field:SerializedName("terminalId")
    val terminalId: String? = null,

    @field:SerializedName("startDate")
    val startDate: String? = null,

    @field:SerializedName("paymentAccountId")
    val paymentAccountId: String? = null,

    @field:SerializedName("vendor")
    val vendor: String? = null,

    @field:SerializedName("isPartnerShipUser")
    val isPartnerShipUser: Boolean? = false,
)

data class DeviceInfo(
    val serialNumber: String,
    val paymentAccountId: String,
    val terminalId: String
)
