package com.bukuwarung.edc.card.activation.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import com.bukuwarung.bluetooth_printer.activities.print.setup.BluetoothDeviceScanActivity.AppType
import com.bukuwarung.edc.BuildConfig
import com.bukuwarung.edc.R
import com.bukuwarung.edc.app.config.VariantConfig
import com.bukuwarung.edc.card.activation.ui.viewmodel.EdcActivationViewModel
import com.bukuwarung.edc.card.constant.PrintConst
import com.bukuwarung.edc.databinding.FragmentEdcAndroidActivationBinding
import com.bukuwarung.edc.util.ToastUtil
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.addQuery
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick
import com.bukuwarung.edc.webview.WebviewActivity
import com.bukuwarung.edc.webview.constant.ClassConstants
import com.bukuwarung.edc.webview.constant.ClassConstants.WEBVIEW_URL
import com.bukuwarung.network.model.ActivateDeviceRequest
import com.bukuwarung.network.model.Status
import com.google.android.material.snackbar.Snackbar
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class EdcActivationFragment : Fragment() {

    @Inject
    lateinit var variantConfig: VariantConfig

    companion object {
        fun newInstance(type: EDCActivationFragmentType) = EdcActivationFragment().apply {
            arguments = Bundle().apply {
                putSerializable("type", type)
            }
        }
    }

    private var _binding: FragmentEdcAndroidActivationBinding? = null
    private val binding get() = _binding!!

    private val viewModel: EdcActivationViewModel by viewModels()

    private val type by lazy { arguments?.getSerializable("type") as EDCActivationFragmentType }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentEdcAndroidActivationBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.apply {
            toolbar.btnBack.singleClick {
                requireActivity().supportFragmentManager.popBackStack()
            }
            btnActivateEdc.singleClick {
                // redirect to webView
                val entryPoint = variantConfig.variantIdentifier
                requireActivity().openActivity(WebviewActivity::class.java) {
                    putString(
                        WEBVIEW_URL,
                        BuildConfig.MINI_ATMPRO_BUY_EDC_EXTERNAL_URL.addQuery(
                            "entryPoint=$entryPoint"
                        )
                    )
                    putBoolean(ClassConstants.HIDE_TOOLBAR, true)
                }
            }
            when (type) {
                EDCActivationFragmentType.Android -> {
                    if (!Utils.isCardReader()) {
                        viewModel.activateDevice(
                            ActivateDeviceRequest(
                                serialNumber = if (Utils.isMfAndroidDevice()) {
                                    Utils.getHardwareSerialNumber()
                                } else {
                                    Utils.getDeviceSerialNumber()
                                },
                                type = if (variantConfig.cardActivationType == "PARTNERSHIP") {
                                    AppType.PARTNERSHIP.name
                                } else {
                                    AppType.NON_PARTNERSHIP.name
                                }
                            )
                        )
                    }
                    toolbar.apply {
                        tvTitle.text = getString(R.string.activate_now)
                    }
                    grpAndroid.showView()
                    grpAtmPro.hideView()
                    ivEdcImage.setImageResource(R.drawable.ic_edc_android)
                }

                EDCActivationFragmentType.MiniAtmPro -> {
                    toolbar.apply {
                        tvTitle.text = getString(R.string.active_device_list)
                    }
                    tvTitle.text = getString(R.string.activate_edc_first)
                    grpAtmPro.showView()
                    grpAndroid.hideView()
                    ivEdcImage.setImageResource(R.drawable.ic_card_reader_sleep)
                }
            }
        }
        subscribe()
    }

    private fun subscribe() {
        viewModel.activateDeviceState.observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> {
                    binding.pbLoader.hideView()
                    ToastUtil.setToast(
                        requireActivity(),
                        PrintConst.ALERT_ERROR,
                        "Device activation failed",
                        binding.toolbar.tvTitle,
                        Snackbar.LENGTH_SHORT
                    )
                }

                Status.SUCCESS -> {
                    binding.pbLoader.hideView()
                    ToastUtil.setToast(
                        requireActivity(),
                        PrintConst.ALERT_INFO,
                        "Device activated successfully",
                        binding.toolbar.tvTitle,
                        Snackbar.LENGTH_SHORT
                    )
                }

                Status.LOADING -> {
                    binding.pbLoader.showView()
                }

                Status.NO_INTERNET -> {
                    binding.pbLoader.hideView()
                }
            }
        }
    }

    enum class EDCActivationFragmentType {
        Android,
        MiniAtmPro
    }
}
