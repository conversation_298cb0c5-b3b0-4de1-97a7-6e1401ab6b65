package com.bukuwarung.edc.card.di

import android.os.Build
import com.bukuwarung.edc.card.data.service.bluetooth.ExternalCardReaderServiceImpl
import com.bukuwarung.edc.card.data.service.morefun.MFDeviceServiceImpl
import com.bukuwarung.edc.card.data.service.pax.PaxDeviceServiceImpl
import com.bukuwarung.edc.card.data.service.verifone.VFDeviceServiceImpl
import com.bukuwarung.edc.card.domain.model.consts.Constants
import com.bukuwarung.edc.card.domain.service.IEdcDeviceService
import com.bukuwarung.edc.card.domain.usecase.AbortEmvUseCase
import com.bukuwarung.edc.card.domain.usecase.BeeperSoundUseCase
import com.bukuwarung.edc.card.domain.usecase.CheckCardAvailabilityUseCase
import com.bukuwarung.edc.card.domain.usecase.CheckCardUseCase
import com.bukuwarung.edc.card.domain.usecase.ConfigureAidUseCase
import com.bukuwarung.edc.card.domain.usecase.ConfigureRidUseCase
import com.bukuwarung.edc.card.domain.usecase.EdcDeviceUseCase
import com.bukuwarung.edc.card.domain.usecase.EmvTagDataUseCase
import com.bukuwarung.edc.card.domain.usecase.EvmInputOnlineResultUseCase
import com.bukuwarung.edc.card.domain.usecase.GeneratePinBlockUseCase
import com.bukuwarung.edc.card.domain.usecase.ImportCardConfirmResultUseCase
import com.bukuwarung.edc.card.domain.usecase.ImportPinUseCase
import com.bukuwarung.edc.card.domain.usecase.InitPinpadUseCase
import com.bukuwarung.edc.card.domain.usecase.LoadDeviceKeyUseCase
import com.bukuwarung.edc.card.domain.usecase.PrintCardReceiptUseCase
import com.bukuwarung.edc.card.domain.usecase.PrintPaymentReceiptUseCase
import com.bukuwarung.edc.card.domain.usecase.PrinterTestUseCase
import com.bukuwarung.edc.card.domain.usecase.StartEvmCardReadUseCase
import com.bukuwarung.edc.card.domain.usecase.StartPinpadPinInputUseCase
import com.bukuwarung.edc.card.domain.usecase.StopCheckCardUseCase
import com.bukuwarung.edc.card.domain.usecase.StopPinpadPinInputUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class EdcDeviceModule {

    @Provides
    @Singleton
    fun provideEdcService(
        vfDeviceService: VFDeviceServiceImpl,
        paxDeviceService: PaxDeviceServiceImpl,
        mfDeviceService: MFDeviceServiceImpl,
        externalCardReaderService: ExternalCardReaderServiceImpl
    ): IEdcDeviceService {
        if (Build.MANUFACTURER == Constants.DEVICE_MANUFACTURER_VERIFONE) {
            return vfDeviceService
        } else if (Build.MANUFACTURER == Constants.DEVICE_MANUFACTURER_PAX) {
            return paxDeviceService
        } else if (Build.MANUFACTURER == Constants.DEVICE_MANUFACTURER_MOREFUN) {
            return mfDeviceService
        } else {
            return externalCardReaderService
        }
    }

    @Provides
    fun provideEdcDeviceUseCases(service: IEdcDeviceService) = EdcDeviceUseCase(
        checkCard = CheckCardUseCase(service),
        stopCheckCard = StopCheckCardUseCase(service),
        startEvm = StartEvmCardReadUseCase(service),
        printReceipt = PrintCardReceiptUseCase(service),
        printTest = PrinterTestUseCase(service),
        printPaymentReceipt = PrintPaymentReceiptUseCase(service),
        loadKeys = LoadDeviceKeyUseCase(service),
        getPinBlock = GeneratePinBlockUseCase(service),
        importCardConfirmResult = ImportCardConfirmResultUseCase(service),
        importPin = ImportPinUseCase(service),
        configureAidUseCase = ConfigureAidUseCase(service),
        configureRidUseCase = ConfigureRidUseCase(service),
        inputOnlineResult = EvmInputOnlineResultUseCase(service),
        abortEmv = AbortEmvUseCase(service),
        getEmvTagData = EmvTagDataUseCase(service),
        checkCardAvailability = CheckCardAvailabilityUseCase(service),
        initPinpad = InitPinpadUseCase(service),
        startPinpadPinInput = StartPinpadPinInputUseCase(service),
        stopPinpadPinInput = StopPinpadPinInputUseCase(service),
        beeperSound = BeeperSoundUseCase(service)
    )
}
