package com.bukuwarung.edc.card.cashwithdrawal.model

import androidx.annotation.Keep
import com.bukuwarung.edc.payments.data.model.BankAccount
import com.google.gson.annotations.SerializedName

@Keep
data class SettlementBankAccountResponse(

    @SerializedName("user_id")
    val userId: String = "",
    @SerializedName("terminal_id")
    val terminalId: String = "",
    @SerializedName("bank_accounts")
    val bankAccounts: ArrayList<BankAccount> = arrayListOf()
)
