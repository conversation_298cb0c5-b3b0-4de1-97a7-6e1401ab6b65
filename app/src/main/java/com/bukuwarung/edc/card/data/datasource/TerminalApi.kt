package com.bukuwarung.edc.card.data.datasource

import com.bukuwarung.edc.card.data.model.ActivationRequest
import com.bukuwarung.edc.card.data.model.ActivationResponse
import com.bukuwarung.edc.card.data.model.Terminal
import com.bukuwarung.edc.card.data.model.TerminalLocation
import com.bukuwarung.edc.card.data.model.TerminalPairedDevice
import com.bukuwarung.edc.card.data.model.TmsResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.PATCH
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Path
import retrofit2.http.Query

interface TerminalApi {

    @POST("ac/api/v2/edc/terminals/{serialNumber}/pair")
    suspend fun pairBluetoothDevice(
        @Path("serialNumber") serialNumber: String,
        @Query("appDeviceId") appDeviceId: String,
        @Query("androidId") androidId: String,
        @Query("btName") btName: String,
        @Query("btAddress") btAddress: String
    ): Response<TmsResponse<TerminalPairedDevice>>

    @GET("ac/api/v2/edc/terminals/{serialNumber}")
    suspend fun getTerminal(
        @Path("serialNumber") serialNumber: String
    ): Response<TmsResponse<Terminal>>

    @POST("ac/api/v2/edc/terminals/{serialNumber}/activate")
    suspend fun activateTerminal(
        @Path("serialNumber") serialNumber: String,
        @Header("app-device-id") appDeviceId: String,
        @Body request: ActivationRequest
    ): Response<TmsResponse<ActivationResponse>>

    @PATCH("ac/api/v2/edc/terminals/{serialNumber}/status")
    suspend fun updateTerminalStatus(
        @Path("serialNumber") serialNumber: String,
        @Body request: Map<String, String>,
        @Header("app-device-id") appDeviceId: String
    ): Response<TmsResponse<Terminal>>

    @PUT("ac/api/v2/edc/terminals/{serialNumber}")
    suspend fun updateTerminal(
        @Path("serialNumber") serialNumber: String,
        @Body terminal: Terminal,
        @Query("reactivation") reactivation: Boolean
    ): Response<TmsResponse<Terminal>>

    @PATCH("ac/api/v2/edc/terminals/{serialNumber}/location")
    suspend fun updateTerminalLocation(
        @Path("serialNumber") serialNumber: String,
        @Body terminalLocation: TerminalLocation
    ): Response<TmsResponse<TerminalLocation>>
}
