package com.bukuwarung.edc.di

import android.content.SharedPreferences
import com.bukuwarung.edc.app.config.AppConfig
import com.bukuwarung.edc.app.config.LoginConfig
import com.bukuwarung.edc.app.config.NetworkConfig
import com.bukuwarung.edc.app.config.VariantConfig
import com.bukuwarung.edc.app.config.types.AppVariant
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.network.utils.AppProvider
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
object AppModule {

    @Provides
    fun providesSharedPreferences(): SharedPreferences = Utils.sharedPreferences

    @Provides
    fun provideConnectivity(): AppProvider = AppProviderImpl()

    @Provides
    fun provideAppConfig(): AppConfig = AppConfig.current

    @Provides
    fun provideNetworkConfig(appConfig: AppConfig): NetworkConfig = appConfig.networkConfig

    @Provides
    fun provideAppVariant(appConfig: AppConfig): AppVariant = appConfig.appVariant

    @Provides
    fun provideVariantConfig(appConfig: AppConfig): VariantConfig = appConfig.variantConfig

    @Provides
    fun provideLoginConfig(appConfig: AppConfig): LoginConfig = appConfig.loginConfig
}
