package com.bukuwarung.edc.di

import android.os.Build
import com.bukuwarung.edc.BuildConfig
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.settings.ui.setting.MiniatmDebugSettingActivity
import com.bukuwarung.edc.util.EncryptedPreferencesHelper
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.orDefault
import com.bukuwarung.network.NetworkConst.BUKUWARUNG_TOKEN
import com.bukuwarung.network.utils.AppProvider

class AppProviderImpl : AppProvider {
    override fun isConnected(): Boolean = Utils.isInternetAvailable()

    override fun testingMock(): Boolean =
        Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_MOCK_API)

    override fun getDeviceBrand(): String = Utils.getDeviceBrand().orDefault(Build.BRAND)

    override fun getDeviceModel(): String = Utils.getDeviceModel().orDefault(Build.MODEL)

    override fun getAppVersionName(): String = BuildConfig.VERSION_NAME

    override fun getAppVersionCode(): String = BuildConfig.VERSION_CODE.toString()

    override fun getAuthToken(): String = EncryptedPreferencesHelper.get(BUKUWARUNG_TOKEN, "")

    override fun getUserId(): String = Utils.getUserId()

    override fun getClientId(): String = Constant.CLIENT_ID

    override fun getClientSecret(): String = Constant.CLIENT_SECRET

    override fun getApiBaseUrl(): String = BuildConfig.API_BASE_URL

    override fun getAccountingApiBaseUrl(): String = BuildConfig.ACCOUNTING_API_BASE_URL

    override fun getBukuOrigin(): String = BuildConfig.BUKU_ORIGIN

    override fun getBureauEventId() = EncryptedPreferencesHelper.get("bureau_event_id", "")

    override fun forceLogout() {
        Utils.clearDataAndLogout(true)
    }
}
