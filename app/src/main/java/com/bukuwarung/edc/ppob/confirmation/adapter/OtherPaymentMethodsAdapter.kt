package com.bukuwarung.edc.ppob.confirmation.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.edc.databinding.LayoutOtherPaymentMethodsBinding
import com.bukuwarung.edc.ppob.confirmation.model.FinproGetPaymentMethodsResponse
import com.bukuwarung.edc.ppob.confirmation.model.FinproPaymentMethod
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.isNotNullOrBlank

class OtherPaymentMethodsAdapter(
    private val list: List<FinproGetPaymentMethodsResponse>,
    private val clickAction: (FinproPaymentMethod) -> Unit,
    private val clickSaldoRecharge: () -> Unit,
    private val clickDeeplink: (String) -> Unit,
    private val useSaldoBonus: Boolean,
    private val changeUseSaldoReward: (Boolean) -> Unit,
    private val otherPaymentMethods: Boolean,
    private val clickSaldoFreezeErrorMessage: () -> Unit
) : RecyclerView.Adapter<OtherPaymentMethodsAdapter.OtherPaymentMethodsItemViewHolder>() {
    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): OtherPaymentMethodsItemViewHolder {
        val itemBinding =
            LayoutOtherPaymentMethodsBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        return OtherPaymentMethodsItemViewHolder(
            itemBinding,
            clickAction,
            clickSaldoRecharge,
            clickDeeplink,
            useSaldoBonus,
            changeUseSaldoReward,
            otherPaymentMethods,
            clickSaldoFreezeErrorMessage
        )
    }

    override fun onBindViewHolder(holder: OtherPaymentMethodsItemViewHolder, position: Int) {
        holder.bind(list[position])
    }

    override fun getItemCount(): Int = list.size

    class OtherPaymentMethodsItemViewHolder(
        private val binding: LayoutOtherPaymentMethodsBinding,
        private val clickAction: (FinproPaymentMethod) -> Unit,
        private val clickSaldoRecharge: () -> Unit,
        private val clickDeeplink: (String) -> Unit,
        private val useSaldoBonus: Boolean,
        private val changeUseSaldoReward: (Boolean) -> Unit,
        private val otherPaymentMethods: Boolean,
        private val clickSaldoFreezeErrorMessage: () -> Unit
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: FinproGetPaymentMethodsResponse) = with(binding) {
            tvTitle.text = item.title
            tvTitle.visibility = item.title.isNotNullOrBlank().asVisibility()
            tvSubTitle.text = item.subtext
            tvSubTitle.visibility = item.subtext.isNotNullOrBlank().asVisibility()
            rvPaymentMethods.adapter =
                PaymentMethodsAdapter(
                    item.channels,
                    clickAction,
                    clickSaldoRecharge,
                    clickDeeplink,
                    useSaldoBonus,
                    changeUseSaldoReward,
                    otherPaymentMethods,
                    clickSaldoFreezeErrorMessage
                )
        }
    }
}
