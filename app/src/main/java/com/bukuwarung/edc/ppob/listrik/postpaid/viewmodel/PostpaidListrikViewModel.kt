package com.bukuwarung.edc.ppob.listrik.postpaid.viewmodel

import android.content.Context
import com.bukuwarung.edc.R
import com.bukuwarung.edc.payments.usecase.FinproUseCase
import com.bukuwarung.edc.ppob.baseactivity.viewmodel.PpobViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class PostpaidListrikViewModel @Inject constructor(finproUseCase: FinproUseCase) :
    PpobViewModel(finproUseCase) {

    fun checkCustomerNumberValidation(customerNumber: String = "", context: Context): Boolean {
        val numberLengthInvalid = customerNumber.length > 16 || customerNumber.length < 8
        viewState.value = currentViewState()?.copy(
            numberLengthInvalid = numberLengthInvalid,
            errorMessage = context.getString(R.string.postpaid_customer_id_invalid)
        )
        return numberLengthInvalid
    }
}
