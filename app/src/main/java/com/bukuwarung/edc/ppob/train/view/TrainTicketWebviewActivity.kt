package com.bukuwarung.edc.ppob.train.view

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.CountDownTimer
import android.view.View
import android.webkit.WebChromeClient
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.ActivityTrainTicketWebviewBinding
import com.bukuwarung.edc.global.Constant.ONE_SECOND
import com.bukuwarung.edc.homepage.ui.home.HomePageActivity
import com.bukuwarung.edc.ppob.baseactivity.viewmodel.PpobViewModel
import com.bukuwarung.edc.ppob.common.model.PpobEvent
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.edc.ppob.constants.PpobRemoteConfig
import com.bukuwarung.edc.ppob.train.dialog.TrainWebviewExitDialog
import com.bukuwarung.edc.ppob.train.model.EnrollmentStatus
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.getDrawableCompat
import com.bukuwarung.edc.util.goToDestination
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.orDefault
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick
import com.bukuwarung.edc.webview.WebviewActivity
import com.bukuwarung.edc.webview.constant.ClassConstants
import com.bukuwarung.ui_component.base.BaseErrorView
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class TrainTicketWebviewActivity : AppCompatActivity() {

    lateinit var binding: ActivityTrainTicketWebviewBinding
    private val viewModel: PpobViewModel by viewModels()

    private var isInternetError = false
    private var trainConfirmationPolling: CountDownTimer? = null
    private var enrollmentId: String? = null
    private var returnToHomeOnBackPress: Boolean? = null

    companion object {
        fun createIntent(context: Context) = Intent(context, TrainTicketWebviewActivity::class.java)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityTrainTicketWebviewBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setupView()
        subscribeState()
    }

    private fun setupView() {
        setToolBarView()
        viewModel.getTrainTicketUrl()
        binding.bukuErrorView.addCallback(errorViewCallBack)
        binding.webView.apply {
            webViewClient = TrainWebViewClient()
            webChromeClient = WebChromeClient()
            isVerticalScrollBarEnabled = true
            settings.cacheMode = WebSettings.LOAD_NO_CACHE
            settings.javaScriptEnabled = true
            scrollBarStyle = View.SCROLLBARS_OUTSIDE_OVERLAY
            settings.loadWithOverviewMode = true
            settings.useWideViewPort = true
            settings.domStorageEnabled = true
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        trainConfirmationPolling?.cancel()
    }

    inner class TrainWebViewClient : WebViewClient() {
        override fun doUpdateVisitedHistory(view: WebView?, url: String?, isReload: Boolean) {
            val isRedirectionUrl = url?.contains(
                PpobRemoteConfig.getPpobConfigs().trainPollingConfig?.redirectionUrlContains
                    .orDefault(PpobConst.REDIRECTION_URL_CONTAINS)
            )
            if (isRedirectionUrl == true && trainConfirmationPolling == null) {
                val pollingConfig = PpobRemoteConfig.getPpobConfigs().trainPollingConfig
                trainConfirmationPolling?.cancel()
                trainConfirmationPolling = object : CountDownTimer(
                    pollingConfig?.pollingTimeInSeconds.orDefault(PpobConst.DEFAULT_POLLING_TIME) *
                        ONE_SECOND,
                    pollingConfig?.pollingInternalSeconds.orDefault(
                        PpobConst.DEFAULT_POLLING_INTERVAL
                    ) *
                        ONE_SECOND
                ) {
                    override fun onTick(millisUntilFinished: Long) {
                        enrollmentId?.let { viewModel.getTrainEnrollmentDetail(it) }
                    }

                    override fun onFinish() {
                        // show error
                        trainConfirmationPolling?.cancel()
                        handleEnrollmentFailure()
                    }
                }
                trainConfirmationPolling?.start()
                binding.webView.hideView()
                binding.clTrainLoading.showView()
            }
            super.doUpdateVisitedHistory(view, url, isReload)
        }
    }

    private var errorViewCallBack = object : BaseErrorView.Callback {
        override fun ctaClicked() {
            handleErrorViewClick()
        }

        override fun messageClicked() {
            handleErrorViewClick()
        }
    }

    private fun handleErrorViewClick() {
        if (isInternetError) {
            viewModel.getTrainTicketUrl()
        } else {
            goToDestination(HomePageActivity::class.java)
        }
    }

    private fun subscribeState() {
        viewModel.observeEvent.observe(this) {
            with(binding) {
                when (it) {
                    is PpobEvent.ShowTrainWebView -> {
                        webView.apply {
                            it.enrollmentResponse?.url?.let { url ->
                                loadUrl(url)
                            }
                            enrollmentId = it.enrollmentResponse?.enrollmentId
                        }
                    }

                    is PpobEvent.ShowTrainTicketDetail -> {
                        when (it.enrollmentDetailResponse.status) {
                            EnrollmentStatus.SUCCESS -> {
                                trainConfirmationPolling?.cancel()
                                startActivity(
                                    TrainTicketDetailsActivity.createIntent(
                                        this@TrainTicketWebviewActivity,
                                        it.enrollmentDetailResponse.inquiryNumber.orEmpty()
                                    )
                                )
                            }

                            EnrollmentStatus.FAILURE -> {
                                handleEnrollmentFailure()
                            }

                            else -> {
                                // Currently we can ignore other cases, re-poll is handled by the timer
                            }
                        }
                    }

                    is PpobEvent.InternetError -> {
                        isInternetError = true
                        webView.hideView()
                        bukuErrorView.showView()
                        bukuErrorView.setErrorType(
                            BaseErrorView.Companion.ErrorType.CUSTOM,
                            getString(R.string.inet_lost),
                            it.message,
                            getString(R.string.retry),
                            R.mipmap.ic_connection_error
                        )
                    }

                    is PpobEvent.ServerError -> {
                        isInternetError = false
                        webView.hideView()
                        bukuErrorView.showView()
                        bukuErrorView.setErrorType(
                            BaseErrorView.Companion.ErrorType.CUSTOM,
                            getString(R.string.server_unreachable_default_title),
                            it.message,
                            getString(R.string.go_back),
                            R.mipmap.ic_server_unreachable
                        )
                    }

                    else -> {}
                }
            }
        }
    }

    private fun handleEnrollmentFailure() {
        returnToHomeOnBackPress = true
        with(binding) {
            clTrainLoading.hideView()
            bukuErrorView.apply {
                showView()
                setErrorType(
                    BaseErrorView.Companion.ErrorType.CUSTOM,
                    getString(R.string.failed_to_load_page),
                    getString(R.string.please_reorder),
                    getString(R.string.return_to_home),
                    R.mipmap.ic_server_unreachable
                )
                addCallback(object : BaseErrorView.Callback {
                    override fun ctaClicked() {
                        finish()
                    }

                    override fun messageClicked() {
                    }
                })
            }
        }
    }

    private fun showDialog() {
        val dialog = TrainWebviewExitDialog(this, {
            if (!it) {
                finish()
            }
        }, {})
        dialog.show()
    }

    override fun onBackPressed() {
        when {
            returnToHomeOnBackPress.isTrue -> super.onBackPressed()
            binding.webView.canGoBack() -> binding.webView.goBack()
            else -> super.onBackPressed()
        }
    }

    private fun setToolBarView() {
        with(binding.includeToolBar) {
            toolBarLabel.text = getString(R.string.train_ticket_title)
            tbPpob.navigationIcon = getDrawableCompat(R.drawable.ic_arrow_back)
            tbPpob.setNavigationOnClickListener {
                Utils.hideKeyboard(this@TrainTicketWebviewActivity)
                showDialog()
            }
            ivHelp.singleClick {
                Utils.hideKeyboard(this@TrainTicketWebviewActivity)
                openActivity(WebviewActivity::class.java) {
                    putString(
                        ClassConstants.WEBVIEW_URL,
                        PpobRemoteConfig.getPpobConfigs().supportUrls.trainTickets
                    )
                }
            }
        }
    }
}
