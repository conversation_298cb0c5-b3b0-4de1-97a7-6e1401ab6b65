package com.bukuwarung.edc.ppob.internetandtvcable.viewmodel

import com.bukuwarung.edc.payments.usecase.FinproUseCase
import com.bukuwarung.edc.ppob.baseactivity.viewmodel.PpobViewModel
import com.bukuwarung.edc.ppob.common.model.Biller
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class InternetAndTvCableViewModel @Inject constructor(finproUseCase: FinproUseCase) :
    PpobViewModel(finproUseCase) {

    fun checkCustomerNumberValidation(
        customerNumber: String = "",
        selectedBiller: Biller?
    ): Bo<PERSON>an {
        val required = selectedBiller?.billerMetaData?.requriedParametersList?.firstOrNull()
        val validationRegex = required?.validationRegex.orEmpty()
        val errorMessage = required?.errorMessage.orEmpty()
        val numberLengthInvalid: Boolean =
            validationRegex.isNotEmpty() && !Regex(validationRegex).matches(customerNumber)
        viewState.value = currentViewState()?.copy(
            numberLengthInvalid = numberLengthInvalid,
            errorMessage = errorMessage
        )
        return numberLengthInvalid
    }
}
