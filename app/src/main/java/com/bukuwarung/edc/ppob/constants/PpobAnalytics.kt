package com.bukuwarung.edc.ppob.constants

object PpobAnalytics {

    const val EVENT_PPOB_FETCH_BILL = "ppob_bill_check_clicked"

    const val PHONE_FILLED = "phone_filled"
    const val ID = "id"
    const val MONTH = "month"
    const val TYPE = "type"
    const val STATUS = "status"
    const val REASON = "reason"
    const val NOMINAL = "nominal"
    const val PROVIDER = "provider"
    const val ENTRY_POINT = "entry_point"
    const val PDAM_DOMICILE = "pdam_domicile"
    const val PARAM_PLN = "pln"
    const val MACHINE_NUMBER = "machine_number"
    const val VIN_NUMBER = "nik_vin"
    const val POLICY_NUMBER = "policy_number"
    const val SAMSAT_DOMICILE = "samsat_domicile"
    const val PARAM_PHONE_NUMBER = "phone_number"
    const val PPOB_SELECT_PAYMENT_METHOD = "ppob_select_payment_method"
    const val BUTTON = "button"
    const val ANJUKAN = "anjukan"
    const val INSUFFICIENT_BALANCE = "insufficient_balance"
    const val SALDO = "saldo"
    const val WALLET = "wallet"
    const val CURRENT_WALLET_BALANCE = "current_wallet_balance"
    const val AVAILABLE_PAYMENT_METHOD = "available_payment_method"
    const val NON_AVAILABLE_PAYMENT_METHOD = "non_available_payment_method"
    const val BANK = "bank"
    const val PAYMENT_METHOD = "payment_method"
    const val CURRENT_SALDO_BONUS_BALANCE = "current_saldo_bonus_balance"
    const val PPOB_FORM = "ppob_form"
    const val ORDER_ID = "order_id"
    const val PACKAGE_PRICE = "package_price"
    const val BUYING_PRICE = "buying_price"
    const val SELLING_PRICE = "selling_price"
    const val PAYMENT_STATUS = "payment_status"
    const val INCOMPLETE = "incomplete"
    const val PPOB_STATUS = "ppob_status"
    const val FAVOURITE_STATUS = "favourite_status"
    const val FAVOURITE_CONTACT_NAME = "favourite_contact_name"
    const val HOMEPAGE = "homepage"
    const val CUSTOMER_ID = "customer_id"
    const val TRANSACTION_TYPE = "transaction_type"
    const val ENABLE_SALDO_BONUS = "enable_saldo_bonus"
    const val NOMINAL_SALDO_BONUS_USED = "nominal_saldo_bonus_used"
    const val PPOB_TYPE = "ppob_type"
    const val ACTION_TYPE = "action_type"
    const val CASHBACK = "cashback"
    const val CHANGE_PAYMENT_METHOD = "change_payment_method"
    const val PHONEBOOK_FAV_CUSTOMER_SECTION = "phonebook_fav_customer_section"
    const val PHONEBOOK_EXISTING_RECORD_USER_SECTION = "phonebook_existing_record_user_section"
    const val PHONEBOOK_NO_RCRDS__USER_SECTION = "phonebook_no_rcrds_user_section"
    const val PHONEBOOK_RECOMMENDATION_CUSTOMER_SECTION =
        "phonebook_recommendation_customer_section"
    const val LOAD_MORE_RECOMMENDATION_CUSTOMER_SECTION =
        "load_more_recommendation_customer_section"
    const val CUSTOMER_CREATE_MANUAL = "create_manual"
    const val ORDER_FORM = "order_form"

    const val PPOB_BUY_PAGE = "ppob_buy_page"

    const val STATUS_SUCCESS = "success"
    const val STATUS_FAILED = "failed"

    const val PPOB_PULSA = "ppob_pulsa"
    const val PPOB_PULSA_POSTPAID = "pulsa_postpaid"
    const val PPOB_LISTRIK = "ppob_listrik"
    const val PPOB_EWALLET = "ppob_ewallet"
    const val PPOB_PACKET_DATA = "ppob_packet_data"
    const val PPOB_GAMING_VOUCHERS = "ppob_gaming_vouchers"
    const val PPOB_BPJS = "ppob_bpjs"
    const val PPOB_PDAM = "ppob_pdam"
    const val PPOB_MULTIFINANCE = "ppob_multifinance"
    const val PPOB_CABLE = "ppob_cable"
    const val PPOB_VEHICLE_TAX = "ppob_e_samsat"
    const val LISTRIK_POSTPAID = "listrik_postpaid"
    const val SET_PPOB_SELLING_PRICE = "set_ppob_selling_price"
    const val TOOLS_PROMOTION = "tools_promotion"
    const val EVENT_TRAIN_TICKETS = "ppob_train_tickets"

    const val EVENT_PPOB_PULSA_BUY_BUTTON = "EVENT_PPOB_PULSA_BUY_BUTTON"
    const val EVENT_PPOB_POSTPAID_PULSA_BUY_BUTTON = "EVENT_PPOB_POSTPAID_PULSA_BUY_BUTTON"
    const val EVENT_PPOB_LISTRIK_BUY_BUTTON = "EVENT_PPOB_LISTRIK_BUY_BUTTON"
    const val EVENT_PPOB_POSTPAID_LISTRIK_BUY_BUTTON = "EVENT_PPOB_POSTPAID_LISTRIK_BUY_BUTTON"
    const val EVENT_PPOB_EWALLET_BUY_BUTTON = "EVENT_PPOB_EWALLET_BUY_BUTTON"
    const val EVENT_PPOB_PAKET_DATA_BUY_BUTTON = "EVENT_PPOB_PAKET_DATA_BUY_BUTTON"
    const val EVENT_PPOB_GAMING_VOUCHER_BUY_BUTTON = "EVENT_PPOB_GAMING_VOUCHER_BUY_BUTTON"
    const val EVENT_PPOB_BPJS_BUY_BUTTON = "EVENT_PPOB_BPJS_BUY_BUTTON"
    const val EVENT_PPOB_PDAM_BUY_BUTTON = "EVENT_PPOB_PDAM_BUY_BUTTON"
    const val EVENT_PPOB_MULTIFINANCE_BUY_BUTTON = "EVENT_PPOB_MULTIFINANCE_BUY_BUTTON"
    const val EVENT_PPOB_VEHICLE_TAX_BUY_BUTTON = "EVENT_PPOB_VEHICLE_TAX_BUY_BUTTON"
    const val EVENT_PPOB_INTERNET_DAN_TV_CABLE_BUY_BUTTON =
        "EVENT_PPOB_INTERNET_DAN_TV_CABLE_BUY_BUTTON"
    const val PPOB_BUY_BUTTON = "ppob_buy_button"
    const val EVENT_PPOB_SELECT_PACK = "ppob_pack_selected"
    const val EVENT_CLICK_REORDER = "click_reorder"
    const val EVENT_PPOB_BUY_BUTTON_CLICKED = "ppob_buy_button_clicked"
    const val EVENT_BUTTON_CLICK = "button_click"
    const val EVENT_WALLET_TOP_UP_CLICK = "wallet_topup_click"
    const val EVENT_PPOB_SELECT_PAYMENT_METHOD = "ppob_payment_method_selected"
    const val EVENT_PPOB_CREATED = "ppob_created"
    const val EVENT_PAYMENT_METHOD_NUDGE = "Payment_method_nudge"
}
