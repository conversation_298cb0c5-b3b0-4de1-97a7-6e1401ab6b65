package com.bukuwarung.edc.ppob.confirmation.view

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.CountDownTimer
import android.os.Handler
import android.os.Looper
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import com.airbnb.lottie.LottieDrawable
import com.bukuwarung.edc.R
import com.bukuwarung.edc.card.ui.receipt.CardReceiptViewModel
import com.bukuwarung.edc.databinding.ActivityPpobStatusBinding
import com.bukuwarung.edc.global.Constant.TAG_PRINT
import com.bukuwarung.edc.homepage.ui.home.HomePageActivity
import com.bukuwarung.edc.payments.data.model.OrderResponse
import com.bukuwarung.edc.payments.ui.core.OrderDetailActivity
import com.bukuwarung.edc.payments.ui.core.PaymentViewModel
import com.bukuwarung.edc.ppob.confirmation.viewmodel.PpobStatusViewModel
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.edc.ppob.constants.PpobRemoteConfig
import com.bukuwarung.edc.util.ToastUtil.setToast
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.Utils.bwLog
import com.bukuwarung.edc.util.goToDestination
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.milliSecondsToSeconds
import com.bukuwarung.edc.util.orDash
import com.bukuwarung.edc.util.orNil
import com.bukuwarung.edc.util.secondsToMilliSeconds
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick
import com.bukuwarung.ui_component.utils.setSingleClickListener
import com.bumptech.glide.Glide
import dagger.hilt.android.AndroidEntryPoint
import java.util.Calendar
import kotlin.math.roundToInt

@AndroidEntryPoint
class PpobStatusActivity : AppCompatActivity() {

    private lateinit var binding: ActivityPpobStatusBinding

    private val viewModel: PpobStatusViewModel by viewModels()
    private val paymentViewModel: PaymentViewModel by viewModels()
    private val orderId: String by lazy { intent.getStringExtra(ORDER_ID).orEmpty() }
    private val category: String by lazy { intent.getStringExtra(CATEGORY).orEmpty() }
    private val imageUrl: String by lazy { intent.getStringExtra(IMAGE_URL).orEmpty() }
    private val pdtNumber: String by lazy { intent.getStringExtra(PRODUCT_NUMBER).orEmpty() }
    private val pdtName: String by lazy { intent.getStringExtra(PRODUCT_NAME).orEmpty() }
    private var timer: CountDownTimer? = null
    private var apiCallTimeInMillis: Long? = null
    private var orderDetail: OrderResponse? = null
    private val scrollViewHandler = Handler(Looper.getMainLooper())
    private val toastHandler = Handler(Looper.getMainLooper())

    companion object {
        private const val ORDER_ID = "ORDER_ID"
        private const val CATEGORY = "CATEGORY"
        private const val IMAGE_URL = "IMAGE_URL"
        private const val PRODUCT_NUMBER = "PRODUCT_NUMBER"
        private const val PRODUCT_NAME = "PRODUCT_NAME"
        fun createIntent(
            context: Context,
            orderId: String,
            category: String,
            imageUrl: String,
            pdtNumber: String,
            pdtName: String
        ): Intent {
            val i = Intent(context, PpobStatusActivity::class.java)
            i.putExtra(ORDER_ID, orderId)
            i.putExtra(CATEGORY, category)
            i.putExtra(IMAGE_URL, imageUrl)
            i.putExtra(PRODUCT_NUMBER, pdtNumber)
            i.putExtra(PRODUCT_NAME, pdtName)
            return i
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPpobStatusBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setupView()
        subscribeState()
    }

    private fun setupView() {
        with(binding) {
            ivClose.setSingleClickListener { goToDestination(HomePageActivity::class.java) }
            viewModel.init(orderId)
            val config = PpobRemoteConfig.getPpobConfigs().ppobPollingConfig
            val ppobStatusPollingTotalTime: Long =
                config?.ppobStatusPollingTotalTimeSeconds.secondsToMilliSeconds()
            val ppobStatusPollingInterval: Long =
                config?.ppobStatusPollingIntervalSeconds.secondsToMilliSeconds()
            timer = object :
                CountDownTimer(ppobStatusPollingTotalTime, ppobStatusPollingInterval) {
                override fun onTick(millisUntilFinished: Long) {
                    if (apiCallTimeInMillis == null) {
                        apiCallTimeInMillis =
                            Calendar.getInstance().timeInMillis
                    }
                    viewModel.getPpobStatus()
                }

                override fun onFinish() {
                    finishAndRedirectToPaymentHistoryScreen()
                }
            }
            timer?.start()
            btnViewPpobDetails.text = getString(
                R.string.view_payment_details_with_time,
                ppobStatusPollingTotalTime.milliSecondsToSeconds().toString()
            )
            btnViewPpobDetails.setSingleClickListener { finishAndRedirectToPaymentHistoryScreen() }
            tvCategory.text = getString(PpobConst.CATEGORY_NAME_MAP[category] ?: R.string.ppob)

            includeBillerInfo.tvPdtNumber.text = pdtNumber.orDash
            includeBillerInfo.tvPdtName.text = pdtName.orDash
            Glide.with(this@PpobStatusActivity)
                .load(imageUrl)
                .error(PpobConst.CATEGORY_DEFAULT_ICON[category] ?: R.mipmap.ic_biller_default)
                .into(includeBillerInfo.ivPpobUsecase)
        }
    }

    private fun finishAndRedirectToPaymentHistoryScreen() {
        finish()
        timer?.cancel()
        startActivities(
            arrayOf(
                Intent(this, HomePageActivity::class.java).apply {
                    flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
                },
                Intent(this, OrderDetailActivity::class.java).apply {
                    putExtra(OrderDetailActivity.ORDER_ID, orderId)
                    putExtra(OrderDetailActivity.PAYMENT_TYPE, category)
                }
            )
        )
    }

    private fun subscribeState() {
        viewModel.eventStatus.observe(this) {
            when (it) {
                is PpobStatusViewModel.Event.ShowPpobPendingUi -> setPendingStatusView()
                is PpobStatusViewModel.Event.ShowPpobCompletedUi -> setSuccessStatusView()
                is PpobStatusViewModel.Event.ShowPpobFailedUi -> setFailedStatusView()

                is PpobStatusViewModel.Event.HandleApiError ->
                    finishAndRedirectToPaymentHistoryScreen()

                is PpobStatusViewModel.Event.ShowPpobReceipt -> setReceipt(it.orderResponse)
                else -> {}
            }
        }
        paymentViewModel.printState.observe(this) {
            when (it) {
                is CardReceiptViewModel.State.SetPrintError -> {
                    bwLog(
                        TAG_PRINT,
                        "Print error [${it.errorStatus.errorCode}]: ${it.errorStatus.msg}"
                    )
                    setToast(
                        this,
                        it.errorStatus.errorLevel,
                        it.errorStatus.msg,
                        binding.root
                    )
                }

                else -> {
                }
            }
        }
    }

    private fun setPendingStatusView() = with(binding) {
        lavAnimation.setAnimation(R.raw.hourglass)
        lavAnimation.repeatCount = LottieDrawable.INFINITE
        tvPpobStatus.text = getString(R.string.payment_out_status_pending_title)
        tvPpobStatusSubtitle.hideView()
        tvTotalPaymentAmount.text = Utils.formatAmount(viewModel.getPaymentAmount())
    }

    private fun setSuccessStatusView() = with(binding) {
        tvPpobStatusSubtitle.hideView()
        lavAnimation.setAnimation(R.raw.checkmark_success)
        lavAnimation.repeatCount = LottieDrawable.INFINITE
        tvPpobStatus.text = getString(
            R.string.ppob_status_success_title,
            getString(PpobConst.CATEGORY_NAME_MAP[category] ?: R.string.ppob)
        )
        tvTotalPaymentAmount.text = Utils.formatAmount(viewModel.getPaymentAmount())
        btnViewPpobDetails.hideView()
        tvFastPpob.showView()
        tvPpobCompletionTime.showView()
        val timeToCompleteTrx =
            (Calendar.getInstance().timeInMillis - apiCallTimeInMillis.orNil).toDouble()
                .milliSecondsToSeconds().roundToInt()
        tvPpobCompletionTime.text = getString(
            R.string.payment_completed_time,
            timeToCompleteTrx.toString()
        )
        vwReference.showView()
        timer?.cancel()
    }

    private fun setFailedStatusView() = with(binding) {
        lavAnimation.setAnimation(R.raw.trx_failed)
        lavAnimation.repeatCount = LottieDrawable.INFINITE
        tvPpobStatus.text = getString(R.string.payment_out_status_failed_title)
        tvPpobStatusSubtitle.showView()
        tvTotalPaymentAmount.text = Utils.formatAmount(viewModel.getPaymentAmount())
        timer?.cancel()
        btnViewPpobDetails.text = getString(R.string.view_payment_details)
    }

    private fun setReceipt(orderResponse: OrderResponse?) {
        orderDetail = orderResponse
        with(binding.includePaymentReceipt) {
            binding.vwDottedLine2.showView()
            root.showView()
            orderInvoice.apply {
                makeInvoice(
                    orderResponse = orderResponse,
                    paymentType = category
                )
                showView()
            }
            btnPrint.singleClick {
                bwLog("PpobStatusActivity", "btnPrint.singleClick")
                paymentViewModel.printPpobReceipt(orderResponse)
            }
        }
    }

    override fun onDestroy() {
        bwLog(e = Exception("PpobStatusActivity-onDestroy"))
        super.onDestroy()
        timer?.cancel()
        scrollViewHandler.removeCallbacksAndMessages(null)
        toastHandler.removeCallbacksAndMessages(null)
    }

    @SuppressLint("MissingSuperCall")
    override fun onBackPressed() {
        goToDestination(HomePageActivity::class.java)
    }
}
