package com.bukuwarung.edc.ppob.recentsandfavourites.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.LayoutRecentFavItemNewBinding
import com.bukuwarung.edc.databinding.LayoutRecentLoadMoreBinding
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.edc.ppob.recentsandfavourites.model.ProfilesItem
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.isNotNullOrEmpty
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.orNil
import com.bukuwarung.edc.util.singleClick
import com.bumptech.glide.Glide

class RecentsItemAdapter(
    private val clickAction: (ProfilesItem) -> Unit,
    private val seeAllClickAction: () -> Unit = {},
    private val clickFavourite: (String) -> Unit,
    private val clickDeleteFavourite: (ProfilesItem) -> Unit
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val VIEW_TYPE_ITEM = 0
        const val VIEW_TYPE_SEE_ALL = 1
    }

    private var list = emptyList<ProfilesItem>()
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder =
        if (viewType == VIEW_TYPE_ITEM) {
            PaymentHistoryViewHolder(
                LayoutRecentFavItemNewBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                ),
                clickAction,
                clickFavourite,
                clickDeleteFavourite
            )
        } else {
            LoadMorePaymentHistoryViewHolder(
                LayoutRecentLoadMoreBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                ),
                seeAllClickAction
            )
        }

    override fun getItemViewType(position: Int): Int = list[position].viewType

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder is PaymentHistoryViewHolder) {
            holder.bind(list[position])
        } else if (holder is LoadMorePaymentHistoryViewHolder) {
            holder.bind()
        }
    }

    override fun getItemCount() = list.size

    fun setData(list: List<ProfilesItem>) {
        this.list = list
        notifyDataSetChanged()
    }

    class PaymentHistoryViewHolder(
        private val binding: LayoutRecentFavItemNewBinding,
        private val clickAction: (ProfilesItem) -> Unit,
        private val clickAddFavourite: (String) -> Unit,
        private val clickDeleteFavourite: (ProfilesItem) -> Unit
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: ProfilesItem) {
            with(binding) {
                tvDate.text =
                    tvDate.context.getString(R.string.wib, item.recentTransaction?.formattedDate())
                tvAmount.text = Utils.formatAmount(item.recentTransaction?.amount)
                tvTitle.text =
                    if (item.favorite.isTrue &&
                        item.alias.isNotNullOrEmpty()
                    ) {
                        item.alias
                    } else {
                        item.details?.accountNumber
                    }
                if (item.favorite.isTrue) {
                    ivFavourite.setImageResource(R.drawable.ic_favourite_fill)
                    ivFavourite.singleClick {
                        clickDeleteFavourite(item)
                    }
                } else {
                    ivFavourite.setImageResource(R.drawable.ic_favourite_grey_border)
                    ivFavourite.singleClick {
                        clickAddFavourite(item.recentTransaction?.orderId.orEmpty())
                    }
                }
                btCheck.singleClick {
                    clickAction(item)
                }
                Glide.with(ivCategory.context)
                    .load(item.biller?.icon)
                    .placeholder(PpobConst.CATEGORY_ICON[item.category].orNil)
                    .error(PpobConst.CATEGORY_ICON[item.category].orNil)
                    .into(ivCategory)
            }
        }
    }

    class LoadMorePaymentHistoryViewHolder(
        private val binding: LayoutRecentLoadMoreBinding,
        private val seeAllClickAction: () -> Unit
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind() {
            binding.tvSeeAll.setOnClickListener {
                seeAllClickAction()
            }
        }
    }
}
