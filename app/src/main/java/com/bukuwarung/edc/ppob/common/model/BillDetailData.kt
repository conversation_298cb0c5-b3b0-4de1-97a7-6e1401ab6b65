package com.bukuwarung.edc.ppob.common.model

import android.os.Parcelable
import androidx.annotation.Keep
import kotlinx.parcelize.Parcelize

@Keep
@Parcelize
data class BillDetailData(
    var key: String?,
    var value: String?,
    var textColor: Int? = null,
    var isExpanded: Boolean = true,
    var rightDrawable: Int = 0,
    var isFavourite: Boolean = false,
    var setHeadingStyle: Boolean = false
) : Parcelable
