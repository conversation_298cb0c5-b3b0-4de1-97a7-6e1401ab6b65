package com.bukuwarung.edc.ppob.common.model

import com.bukuwarung.edc.payments.data.model.OrderResponse
import com.bukuwarung.edc.ppob.train.model.TrainEnrollmentDetailResponse
import com.bukuwarung.edc.ppob.train.model.TrainEnrollmentResponse
import com.bukuwarung.payments.data.model.PpobProduct

sealed class PpobEvent {
    data class ShowProductsList(
        val list: List<PpobProduct>,
        val isFilteredCall: Boolean = false,
        val searchTerm: String = "",
        val billerDetails: Map<String, Biller>? = null
    ) : PpobEvent()

    data class ServerError(val message: String?) : PpobEvent()
    data class InternetError(val message: String?) : PpobEvent()
    data class SearchServerError(val message: String?) : PpobEvent()
    data class SearchInternetError(val message: String?) : PpobEvent()
    data class OtherError(val message: String?) : PpobEvent()
    data class ToDetail(val orderDetail: OrderResponse?) : PpobEvent()
    object ShowProfileDialog : PpobEvent()
    data class RefreshFavourite(val message: String, val refreshFavourite: Boolean) : PpobEvent()
    data class ShowBillerList(val list: List<Biller>) : PpobEvent()
    data class ShowTrainWebView(val enrollmentResponse: TrainEnrollmentResponse?) : PpobEvent()
    data class ShowTrainTicketDetail(val enrollmentDetailResponse: TrainEnrollmentDetailResponse) :
        PpobEvent()
}
