package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class PpobProduct(
    @SerializedName("name")
    val name: String? = null,
    @SerializedName("description")
    val description: String? = null,
    @SerializedName("sku")
    val sku: String? = null,
    @SerializedName("amount")
    val amount: Double? = null,
    @SerializedName("product_info")
    val productInfo: PpobProductInfo? = null,
    @SerializedName("validation")
    val validationInfo: ValidationInfo? = null,
    @SerializedName("active")
    val active: Boolean? = null,
    @SerializedName("discount")
    val discount: Discount? = null,
    var isSelected: Boolean = false
) : Parcelable

@Parcelize
data class PpobProductInfo(
    @SerializedName("biller_name")
    val billerName: String?,
    @SerializedName("biller")
    val biller: String,
    @SerializedName("is_popular")
    val isPopular: String,
    @SerializedName("type")
    val type: String,
    @SerializedName("logo")
    val logo: String?,
    @SerializedName("validation_regex")
    val validationRegex: String?,
    @SerializedName("error_message")
    val errorMessage: String?,
    @SerializedName("is_freeform")
    val isFreeForm: Boolean?
) : Parcelable

@Parcelize
data class ValidationInfo(
    @SerializedName("validation_regex")
    val validationRegex: String?,
    @SerializedName("validation_error_message")
    val validationErrorMessage: String?,
    @SerializedName("value_type")
    val valueType: String?
) : Parcelable

@Parcelize
data class Discount(
    @SerializedName("value")
    val value: Double? = null,
    @SerializedName("original")
    val original: Double? = null
) : Parcelable
