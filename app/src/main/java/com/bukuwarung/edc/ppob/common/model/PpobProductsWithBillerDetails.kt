package com.bukuwarung.edc.ppob.common.model

import android.os.Parcelable
import com.bukuwarung.payments.data.model.PpobProduct
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class PpobProductsWithBillerDetails(
    @SerializedName("products")
    val productsList: List<PpobProduct>? = null,
    @SerializedName("biller_detail")
    val billerDetails: Map<String, Biller>? = null
) : Parcelable
