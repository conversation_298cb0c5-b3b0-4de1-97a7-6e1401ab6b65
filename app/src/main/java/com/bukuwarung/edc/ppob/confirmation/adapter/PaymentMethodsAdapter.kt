package com.bukuwarung.edc.ppob.confirmation.adapter

import android.content.Context
import android.text.SpannableStringBuilder
import android.view.Gravity
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.LayoutPpobPaymentMethodBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.payments.data.model.FeeDetails
import com.bukuwarung.edc.ppob.confirmation.model.FinproPaymentMethod
import com.bukuwarung.edc.ppob.constants.PpobAnalytics
import com.bukuwarung.edc.util.TextUtils
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.boldText
import com.bukuwarung.edc.util.getColorCompat
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.isNotNullOrBlank
import com.bukuwarung.edc.util.isNotZero
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.orNil
import com.bukuwarung.edc.util.showView
import com.bukuwarung.ui_component.utils.setSingleClickListener
import com.bumptech.glide.Glide

class PaymentMethodsAdapter(
    private val list: List<FinproPaymentMethod>,
    private val clickPaymentMethod: (FinproPaymentMethod) -> Unit,
    private val clickSaldoRecharge: () -> Unit,
    private val clickDeeplink: (String) -> Unit,
    private val useSaldoBonus: Boolean,
    private val changeUseSaldoReward: (Boolean) -> Unit,
    private val otherPaymentMethods: Boolean,
    private val clickSaldoFreezeErrorMessage: () -> Unit
) : RecyclerView.Adapter<PaymentMethodsAdapter.PaymentMethodsItemViewHolder>() {
    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): PaymentMethodsItemViewHolder {
        val itemBinding =
            LayoutPpobPaymentMethodBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        return PaymentMethodsItemViewHolder(
            itemBinding,
            clickPaymentMethod,
            clickSaldoRecharge,
            clickDeeplink,
            useSaldoBonus,
            changeUseSaldoReward,
            otherPaymentMethods,
            clickSaldoFreezeErrorMessage
        )
    }

    override fun onBindViewHolder(holder: PaymentMethodsItemViewHolder, position: Int) {
        holder.bind(list[position])
    }

    override fun getItemCount(): Int = list.size

    companion object {
        private const val BNPL_NOT_REGISTERED = "BNPL_NOT_REGISTERED"
        private const val BNPL_IN_PROCESS = "BNPL_IN_PROCESS"
        private const val BNPL_DUE = "BNPL_DUE"
        const val DISABLE = "DISABLE"
        private const val SALDO_RECHARGE = "SALDO_RECHARGE"
        private const val CAMPAIGN = "CAMPAIGN"
        private const val ADVERTISEMENT = "ADVERTISEMENT"
    }

    class PaymentMethodsItemViewHolder(
        private val binding: LayoutPpobPaymentMethodBinding,
        private val clickPaymentMethod: (FinproPaymentMethod) -> Unit,
        private val clickSaldoRecharge: () -> Unit,
        private val clickDeeplink: (String) -> Unit,
        private val useSaldoBonus: Boolean,
        private val changeUseSaldoReward: (Boolean) -> Unit,
        private val otherPaymentMethods: Boolean,
        private val clickSaldoFreezeErrorMessage: () -> Unit
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: FinproPaymentMethod) = with(binding) {
            val context = ivLogo.context
            Glide.with(context)
                .load(item.logo)
                .error(R.drawable.ic_bank)
                .into(ivLogo)
            tvBankName.text = item.name
            rbSelect.isChecked = item.details?.isSelected.isTrue
            saldoLimitView.hideView()
            tvSaldoInfo.setSingleClickListener { }
            with(item.tag?.tagType) {
                // setting the text under bank name
                when {
                    this.equals(DISABLE, true) -> {
                        if (item.code.equals(PaymentConst.BNPL)) {
                            tvNewLabel.hideView()
                            tvInfo.showView()
                            tvInfo.text = context.getString(
                                R.string.bnpl_remaining_limit,
                                Utils.formatAmount(item.details?.limit)
                            )
                            tvInfo.setTextColor(context.getColorCompat(R.color.black_60))
                            tvErrorInfo.text = item.tag?.displayText.orEmpty()
                            tvErrorInfo.visibility =
                                item.tag?.displayText.isNotNullOrBlank().asVisibility()
                            tvErrorInfo.setTextColor(context.getColorCompat(R.color.red_60))
                        } else {
                            tvErrorInfo.hideView()
                            tvNewLabel.hideView()
                            tvInfo.text = item.tag?.displayText.orEmpty()
                            tvInfo.visibility =
                                item.tag?.displayText.isNotNullOrBlank().asVisibility()
                            tvInfo.setTextColor(context.getColorCompat(R.color.red_60))
                        }
                    }

                    (item.code.equals(PaymentConst.SALDO)) -> {
                        tvErrorInfo.hideView()
                        tvNewLabel.hideView()
                        tvInfo.showView()
                        tvInfo.text = Utils.formatAmount(item.details?.saldoBalance)
                        when {
                            this.equals(PaymentConst.SALDO_FREEZE) -> {
                                tvSaldoInfo.showView()
                                tvSaldoInfo.text =
                                    SpannableStringBuilder(
                                        context.getString(R.string.saldo_freeze_message)
                                    ).boldText(
                                        context.getString(
                                            R.string.saldo_freeze_message_highlight_text
                                        ),
                                        ignoreCase = true
                                    )
                                tvSaldoInfo.setSingleClickListener {
                                    clickSaldoFreezeErrorMessage()
                                }
                            }

                            this.equals(PaymentConst.MONTHLY_LIMIT_REACHED, true) || this.equals(
                                PaymentConst.DAILY_LIMIT_REACHED
                            ) -> {
                                tvSaldoInfo.showView()
                                tvSaldoInfo.text = item.tag?.displayText.orEmpty()
                            }

                            else -> {
                                tvSaldoInfo.hideView()
                            }
                        }
                        saldoLimitView.setViewWithHiddenInfoIcon(
                            item.details?.remainingDailyLimit,
                            item.details?.remainingMonthlyLimit
                        )
                        // Only in case of recharge we don't show remaining limits
                        saldoLimitView.visibility =
                            (!this.equals(PaymentConst.SALDO_RECHARGE)).asVisibility()
                        tvInfo.setTextColor(context.getColorCompat(R.color.black_60))
                    }

                    (this.equals(BNPL_NOT_REGISTERED)) -> {
                        tvErrorInfo.hideView()
                        tvNewLabel.showView()
                        tvInfo.showView()
                        tvInfo.text = item.tag?.displayText.orEmpty()
                        tvInfo.setTextColor(context.getColorCompat(R.color.black_60))
                    }

                    (this.equals(BNPL_IN_PROCESS)) -> {
                        tvErrorInfo.hideView()
                        tvNewLabel.hideView()
                        tvInfo.showView()
                        tvInfo.text = item.tag?.displayText.orEmpty()
                        tvInfo.setTextColor(context.getColorCompat(R.color.black_60))
                    }

                    (
                        this.equals(
                            BNPL_DUE
                        ) || (item.code.equals(PaymentConst.BNPL) && this == null)
                        ) -> {
                        tvErrorInfo.hideView()
                        tvNewLabel.hideView()
                        tvInfo.showView()
                        tvInfo.text = context.getString(
                            R.string.bnpl_remaining_limit,
                            Utils.formatAmount(item.details?.limit)
                        )
                        tvInfo.setTextColor(context.getColorCompat(R.color.black_40))
                    }

                    else -> {
                        tvErrorInfo.hideView()
                        tvNewLabel.hideView()
                        setFeeInfo(item.feeDetails, context)
                    }
                }
                // setting background color, click listener and showing radio button or bnpl buttons
                when {
                    this.equals(DISABLE, true) ||
                        this.equals(SALDO_RECHARGE, true) ||
                        this.equals(PaymentConst.MONTHLY_LIMIT_REACHED, true) ||
                        this.equals(PaymentConst.DAILY_LIMIT_REACHED, true) ||
                        this.equals(BNPL_DUE, true) ||
                        this.equals(PaymentConst.SALDO_FREEZE, true) -> {
                        root.setSingleClickListener { }
                        rbSelect.isEnabled = false
                        rbSelect.showView()
                        btnBnplNotRegistered.hideView()
                        tvBnplInProgress.hideView()
                        rbSelect.setSingleClickListener { }
                        clPaymentMethod.setBackgroundColor(
                            context.getColorCompat(R.color.neutral_50)
                        )
                    }

                    this.equals(BNPL_NOT_REGISTERED) -> {
                        root.setSingleClickListener {
                            val map = HashMap<String, String>()
                            map[PpobAnalytics.ENTRY_POINT] =
                                PpobAnalytics.PPOB_SELECT_PAYMENT_METHOD
                            map[PpobAnalytics.BUTTON] = PpobAnalytics.ANJUKAN
                            Analytics.trackEvent(PpobAnalytics.EVENT_BUTTON_CLICK, map)
                            clickDeeplink(item.tag?.deeplink.orEmpty())
                        }
                        rbSelect.hideView()
                        tvBnplInProgress.hideView()
                        btnBnplNotRegistered.showView()
                        btnBnplNotRegistered.text = item.tag?.ctaText.orEmpty()
                        btnBnplNotRegistered.setSingleClickListener {
                            val map = HashMap<String, String>()
                            map[PpobAnalytics.ENTRY_POINT] =
                                PpobAnalytics.PPOB_SELECT_PAYMENT_METHOD
                            map[PpobAnalytics.BUTTON] = PpobAnalytics.ANJUKAN
                            Analytics.trackEvent(PpobAnalytics.EVENT_BUTTON_CLICK, map)
                            clickDeeplink(item.tag?.deeplink.orEmpty())
                        }
                        clPaymentMethod.setBackgroundColor(context.getColorCompat(R.color.white))
                    }

                    this.equals(BNPL_IN_PROCESS) -> {
                        root.setSingleClickListener { clickDeeplink(item.tag?.deeplink.orEmpty()) }
                        rbSelect.hideView()
                        tvBnplInProgress.showView()
                        btnBnplNotRegistered.hideView()
                        tvBnplInProgress.text = item.tag?.ctaText.orEmpty()
                        tvBnplInProgress.setSingleClickListener {
                            clickDeeplink(item.tag?.deeplink.orEmpty())
                        }
                        clPaymentMethod.setBackgroundColor(context.getColorCompat(R.color.white))
                    }

                    else -> {
                        rbSelect.showView()
                        btnBnplNotRegistered.hideView()
                        tvBnplInProgress.hideView()
                        rbSelect.isEnabled = true
                        rbSelect.setSingleClickListener { clickPaymentMethod(item) }
                        root.setSingleClickListener { clickPaymentMethod(item) }
                        clPaymentMethod.setBackgroundColor(context.getColorCompat(R.color.white))
                    }
                }

                // setting tag info
                when {
                    this.equals(SALDO_RECHARGE, true) -> {
                        with(baBankInfo) {
                            showView()
                            addText(context.getString(R.string.add_saldo_balance))
                            addActionText(context.getString(R.string.topup_saldo))
                            backGroundColor(R.drawable.background_error)
                            actionTextColor(context.getColorCompat(R.color.red_80))
                            textColor(context.getColorCompat(R.color.black_60))
                            setClickAction { clickSaldoRecharge() }
                        }
                    }

                    this.equals(BNPL_DUE, true) -> {
                        with(baBankInfo) {
                            showView()
                            addText(item.tag?.displayText.orEmpty())
                            addActionText(item.tag?.ctaText.orEmpty())
                            backGroundColor(R.drawable.background_error)
                            actionTextColor(context.getColorCompat(R.color.red_80))
                            textColor(context.getColorCompat(R.color.black_60))
                            setClickAction { clickDeeplink(item.tag?.deeplink.orEmpty()) }
                        }
                    }

                    this.equals(ADVERTISEMENT, true) -> {
                        with(baBankInfo) {
                            showView()
                            addText(item.tag?.displayText.orEmpty())
                            hideActionText()
                            setTextGravity(Gravity.CENTER)
                            if (item.details?.isSelected.isTrue) {
                                backGroundColor(R.drawable.background_success)
                                textColor(context.getColorCompat(R.color.green_80))
                            } else {
                                backGroundColor(R.drawable.background_info)
                                textColor(context.getColorCompat(R.color.blue_80))
                            }
                        }
                    }

                    this.equals(CAMPAIGN, true) -> {
                        with(baBankInfo) {
                            showView()
                            addText(item.tag?.displayText.orEmpty())
                            hideActionText()
                            setTextGravity(Gravity.CENTER)
                            if (item.details?.isSelected.isTrue) {
                                backGroundColor(R.drawable.background_warning)
                                textColor(context.getColorCompat(R.color.black_80))
                            } else {
                                backGroundColor(R.drawable.background_info)
                                textColor(context.getColorCompat(R.color.blue_80))
                            }
                        }
                    }

                    else -> {
                        baBankInfo.hideView()
                    }
                }
            }
            val showSaldoReward =
                item.details?.saldoBonus != null &&
                    item.details.saldoBonus.available?.isNotZero().isTrue
            with(binding.includePpobReward) {
                root.visibility = (showSaldoReward && !otherPaymentMethods).asVisibility()
                tvTitle.text = context.getString(
                    R.string.use_saldo_reward,
                    Utils.formatAmount(item.details?.saldoBonus?.available)
                )
                tvSubtitle.text = context.getString(
                    R.string.total_saldo_bonus,
                    Utils.formatAmount(item.details?.saldoBonus?.total)
                )
                swReward.isChecked = useSaldoBonus
                swReward.setOnCheckedChangeListener { _, isChecked ->
                    changeUseSaldoReward(isChecked)
                }
            }
        }

        private fun setFeeInfo(feeDetails: FeeDetails?, context: Context) {
            with(binding) {
                feeDetails?.let {
                    tvInfo.showView()
                    if (it.fee.orNil == 0.0) {
                        tvInfo.setTextColor(context.getColorCompat(R.color.green_60))
                        tvInfo.text = context.getString(R.string.free_upper_case)
                    } else if (it.fee.orNil - it.discount.orNil > 0.0) {
                        tvInfo.setTextColor(context.getColorCompat(R.color.black40))
                        if (it.discount.orNil == 0.0) {
                            tvInfo.text =
                                context.getString(R.string.biaya_admin) + " " + Utils.formatAmount(
                                    it.fee
                                )
                        } else {
                            val info =
                                context.getString(R.string.biaya_admin) + " " + Utils.formatAmount(
                                    it.fee
                                ) + " " + Utils.formatAmount(it.fee.orNil - it.discount.orNil)
                            tvInfo.text = TextUtils.decorateTextString(
                                info,
                                hashMapOf(
                                    Utils.formatAmount(
                                        it.fee
                                    ) to TextUtils.TextDecorations(
                                        strikethrough = true
                                    ),
                                    Utils.formatAmount(it.fee.orNil - it.discount.orNil) to
                                        TextUtils.TextDecorations(
                                            bold = true,
                                            textColor = context.getColorCompat(
                                                R.color.colorPrimary
                                            )
                                        )
                                )
                            )
                        }
                    }
                } ?: run {
                    tvInfo.hideView()
                }
            }
        }
    }
}
