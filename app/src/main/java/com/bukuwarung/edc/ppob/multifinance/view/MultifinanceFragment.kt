package com.bukuwarung.edc.ppob.multifinance.view

import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.FragmentMultifinanceBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.payments.data.model.OrderResponse
import com.bukuwarung.edc.ppob.common.model.Biller
import com.bukuwarung.edc.ppob.common.model.FinproAddCartRequest
import com.bukuwarung.edc.ppob.common.model.FinproBeneficiary
import com.bukuwarung.edc.ppob.common.model.PpobEvent
import com.bukuwarung.edc.ppob.common.view.PaymentDownBottomSheet
import com.bukuwarung.edc.ppob.common.view.PpobBillDetailsBottomSheet
import com.bukuwarung.edc.ppob.common.view.PpobBillersDialog
import com.bukuwarung.edc.ppob.constants.PpobAnalytics
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.edc.ppob.multifinance.viewmodel.MultifinanceViewModel
import com.bukuwarung.edc.ppob.recentsandfavourites.model.ProfilesItem
import com.bukuwarung.edc.ppob.recentsandfavourites.view.RecentAndFavouriteFragment
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.showView
import com.bukuwarung.payments.data.model.PpobProduct
import com.bukuwarung.ui_component.utils.isNotNullOrEmpty
import com.bukuwarung.ui_component.utils.setSingleClickListener
import com.github.razir.progressbutton.hideProgress
import com.github.razir.progressbutton.showProgress
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class MultifinanceFragment :
    Fragment(),
    PpobBillersDialog.ICommunicator,
    RecentAndFavouriteFragment.IRecentAndFavCommunicator,
    PpobBillDetailsBottomSheet.PpobBillDetailsBsListener {

    private var _binding: FragmentMultifinanceBinding? = null
    private val binding get() = _binding!!
    private var selectedProduct: PpobProduct? = null
    private var selectedBiller: Biller? = null
    private var addToCartResponse: OrderResponse? = null
    private val category = PpobConst.CATEGORY_MULTIFINANCE
    private var recentBiller = ""
    private var recentAndFavouriteFragment: RecentAndFavouriteFragment? = null

    private val viewModel: MultifinanceViewModel by viewModels()

    private val from by lazy { arguments?.getString(FROM).orEmpty() }
    private val accountNumber by lazy { arguments?.getString(ACCOUNT_NUMBER).orEmpty() }
    private val phoneNumber by lazy { arguments?.getString(PHONE_NUMBER).orEmpty() }
    private val code by lazy { arguments?.getString(CODE).orEmpty() }

    companion object {
        private const val FROM = "FROM"
        private const val ACCOUNT_NUMBER = "ACCOUNT_NUMBER"
        private const val PHONE_NUMBER = "PHONE_NUMBER"
        private const val CODE = "CODE"
        fun createIntent(
            from: String,
            accountNumber: String,
            code: String,
            phoneNumber: String
        ): MultifinanceFragment {
            val bundle = Bundle().apply {
                putString(FROM, from)
                putString(ACCOUNT_NUMBER, accountNumber)
                putString(CODE, code)
                putString(PHONE_NUMBER, phoneNumber)
            }
            return MultifinanceFragment().apply {
                arguments = bundle
            }
        }
    }

    override fun onCreateView(
        layoutInflater: LayoutInflater,
        viewGroup: ViewGroup?,
        bundle: Bundle?
    ): View {
        _binding = FragmentMultifinanceBinding.inflate(layoutInflater, viewGroup, false)
        setupView()
        subscribeState()
        return binding.root
    }

    override fun onResume() {
        super.onResume()
        recentAndFavouriteFragment = RecentAndFavouriteFragment.createIntent(category).also {
            childFragmentManager.beginTransaction().add(
                binding.flRecentAndFav.id,
                it
            ).commit()
        }
    }

    private fun setupView() {
        with(binding) {
            bivBiller.setDropDownDrawable {
                showMultifinanceProductsDialog()
            }
            bivCustomerNumber.onTextChanged {
                bivCustomerNumber.setClearDrawable()
                viewModel.checkCustomerNumberValidation(bivCustomerNumber.getText(), selectedBiller)
            }
            bivNumber.onTextChanged { bivNumber.setClearDrawable() }
            btnCek.setSingleClickListener {
                Utils.hideKeyboard(requireActivity())
                addToCart()
            }
            if (accountNumber.isNotNullOrEmpty()) {
                showViewAndSetData(code, accountNumber, phoneNumber)
            }
        }
    }

    private fun showViewAndSetData(biller: String, accountNumber: String, phoneNumber: String) =
        with(binding) {
            bivCustomerNumber.showView()
            bivNumber.showView()
            btnCek.showView()
            recentBiller = biller
            bivCustomerNumber.setText(accountNumber)
            bivBiller.setText(biller)
            bivNumber.setText(phoneNumber)
            btnCek.callOnClick()
        }

    private fun addToCart() {
        viewModel.addToCart(
            FinproAddCartRequest(
                sku = selectedProduct?.sku.orEmpty(),
                beneficiary = FinproBeneficiary(
                    category = category,
                    accountNumber = binding.bivCustomerNumber.getText(),
                    code = recentBiller,
                    phoneNumber = binding.bivNumber.getText()
                ),
                userType = PpobConst.USERTYPE_TREATMENT
            )
        )
    }

    private fun showMultifinanceProductsDialog() {
        Utils.hideKeyboard(requireActivity())
        PpobBillersDialog.getInstance(selectedProduct, category).show(
            childFragmentManager,
            PpobBillersDialog.TAG
        )
    }

    private fun subscribeState() {
        viewModel.observeEvent.observe(viewLifecycleOwner) {
            when (it) {
                is PpobEvent.ToDetail -> {
                    addToCartResponse = it.orderDetail
                    trackFetchBillEvent()
                    binding.bivCustomerNumber.setSuccessState("")
                    showBillDetailsBottomSheet()
                }

                is PpobEvent.ServerError -> {
                    trackFetchBillEvent(it.message.orEmpty())
                    binding.bivCustomerNumber.setSuccessState("")
                    showPaymentDownBottomSheet(true, it.message)
                }

                is PpobEvent.InternetError -> {
                    trackFetchBillEvent(it.message.orEmpty())
                    binding.bivCustomerNumber.setSuccessState("")
                    showPaymentDownBottomSheet(false, it.message)
                }

                is PpobEvent.OtherError -> {
                    trackFetchBillEvent(it.message.orEmpty())
                    binding.bivCustomerNumber.setErrorMessage(it.message.orEmpty())
                }

                else -> {
                    binding.bivCustomerNumber.setSuccessState("")
                }
            }
        }
        viewModel.viewState.observe(viewLifecycleOwner) {
            if (it.numberLengthInvalid) {
                binding.bivCustomerNumber.setErrorMessage(it.errorMessage)
                binding.btnCek.isEnabled = false
            } else if (binding.bivCustomerNumber.getText().isNotBlank()) {
                binding.bivCustomerNumber.setSuccessState("")
                binding.btnCek.isEnabled = true
            }
            if (it.showLoading) {
                binding.btnCek.showProgress {
                    buttonTextRes = null
                    progressColor = Color.BLACK
                }
            } else {
                binding.btnCek.hideProgress(R.string.bt_cek)
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding?.bivCustomerNumber?.removeTextChanged()
        _binding = null
    }

    override fun setSelectedProduct(selectedProduct: PpobProduct, biller: Biller) {
        this.selectedProduct = selectedProduct
        this.selectedBiller = biller
        with(binding) {
            bivCustomerNumber.showView()
            bivNumber.showView()
            btnCek.showView()
            bivBiller.setText(selectedProduct.name.orEmpty())
            bivCustomerNumber.setFocus()
            Utils.showKeyboard(requireActivity())
            if (bivCustomerNumber.getText().isNotBlank() && viewModel.checkCustomerNumberValidation(
                    bivCustomerNumber.getText(),
                    selectedBiller
                )
            ) {
                addToCart()
            }
        }
    }

    private fun showPaymentDownBottomSheet(isServiceDown: Boolean, message: String? = null) {
        val paymentDownBottomSheet = PaymentDownBottomSheet.createInstance(isServiceDown, message)
        paymentDownBottomSheet.show(childFragmentManager, PaymentDownBottomSheet.TAG)
    }

    private fun trackFetchBillEvent(errorMessage: String = "") {
        val map = hashMapOf<String, String>()
        map[PpobAnalytics.ID] = binding.bivCustomerNumber.getText()
        map[PpobAnalytics.PROVIDER] = selectedProduct?.productInfo?.billerName.orEmpty()
        map[PpobAnalytics.TYPE] = PpobAnalytics.PPOB_MULTIFINANCE
        map[PpobAnalytics.STATUS] = if (errorMessage.isBlank()) {
            PpobAnalytics.STATUS_SUCCESS
        } else {
            PpobAnalytics.STATUS_FAILED
        }
        map[PpobAnalytics.REASON] = errorMessage
        map[PpobAnalytics.PHONE_FILLED] = binding.bivNumber.getText()
        Analytics.trackEvent(PpobAnalytics.EVENT_PPOB_FETCH_BILL, map)
    }

    private fun showBillDetailsBottomSheet() {
        val ppobBillDetailsBottomSheet =
            PpobBillDetailsBottomSheet.createInstance(addToCartResponse, category)
        ppobBillDetailsBottomSheet.show(childFragmentManager, PpobBillDetailsBottomSheet.TAG)
    }

    override fun setData(profilesItem: ProfilesItem) {
        showViewAndSetData(
            profilesItem.biller?.code.orEmpty(),
            profilesItem.details?.accountNumber.orEmpty(),
            profilesItem.details?.phoneNumber.orEmpty()
        )
    }

    override fun refreshFavouritesTab() {
        recentAndFavouriteFragment?.refreshFavAndRecentTab()
    }
}
