package com.bukuwarung.edc.ppob.common.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class BillerMetaData(
    @SerializedName("required_parameters")
    val requriedParametersList: List<BillerRequiredParameters>? = null,
    @SerializedName("icon")
    val icon: String? = null,
    @SerializedName("extra_data")
    val extraData: BillerExtraData? = null,
    @SerializedName("type")
    val type: String?
) : Parcelable

@Parcelize
data class BillerExtraData(
    @SerializedName("faq_link")
    val faqLink: String?,
    @SerializedName("banner_text")
    val bannerText: String?,
    @SerializedName("banner_image")
    val bannerImage: String?,
    @SerializedName("banner_title")
    val bannerTitle: String?,
    @SerializedName("info_box_text")
    val infoBoxText: String?,
    @SerializedName("info_box_cta")
    val infoBoxCta: String?,
    @SerializedName("layout_type")
    val layoutType: String?
) : Parcelable

@Parcelize
data class BillerRequiredParameters(
    @SerializedName("validation_regex")
    val validationRegex: String? = null,
    @SerializedName("error_message")
    val errorMessage: String? = null
) : Parcelable
