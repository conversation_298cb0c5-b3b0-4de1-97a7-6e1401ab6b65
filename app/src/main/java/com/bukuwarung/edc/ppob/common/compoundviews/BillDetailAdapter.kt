package com.bukuwarung.edc.ppob.common.compoundviews

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.LayoutBillDetailExpandedBinding
import com.bukuwarung.edc.databinding.LayoutBillDetailItemBinding
import com.bukuwarung.edc.ppob.common.model.BillDetailData
import com.bukuwarung.edc.util.getColorCompat
import com.bukuwarung.edc.util.getDrawableCompat
import com.bukuwarung.edc.util.singleClick

class BillDetailAdapter(
    private val expandButtonClick: (Boolean) -> Unit,
    private val favButtonClick: () -> Unit
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val ITEM_VIEW_TYPE = 1
        private const val BUTTON_VIEW_TYPE = 0
    }

    private var list: List<BillDetailData> = emptyList()
    private var showButtonItem: Boolean = false
    private var showExpandedItem: Boolean = false

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder =
        when (viewType) {
            ITEM_VIEW_TYPE -> {
                BillDetailViewHolder(
                    LayoutBillDetailItemBinding.inflate(
                        LayoutInflater.from(parent.context),
                        parent,
                        false
                    )
                )
            }

            else -> {
                ExpandedViewHolder(
                    LayoutBillDetailExpandedBinding.inflate(
                        LayoutInflater.from(parent.context),
                        parent,
                        false
                    )
                )
            }
        }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        (holder as? BillDetailViewHolder)?.bind(list[position])
        (holder as? ExpandedViewHolder)?.bind()
    }

    override fun getItemCount() = if (showButtonItem) {
        list.size + 1
    } else {
        list.size
    }

    override fun getItemViewType(position: Int): Int =
        if (showButtonItem && position == list.size) {
            BUTTON_VIEW_TYPE
        } else {
            ITEM_VIEW_TYPE
        }

    inner class BillDetailViewHolder(private val binding: LayoutBillDetailItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(item: BillDetailData) {
            with(binding) {
                tvKey.text = item.key
                tvValue.text = item.value
                item.textColor?.let {
                    tvValue.setTextAppearance(tvValue.context, R.style.SubHeading1)
                    tvValue.setTextColor(tvValue.context.getColorCompat(it))
                }
                if (item.setHeadingStyle) {
                    tvValue.setTextAppearance(tvValue.context, R.style.Heading3)
                } else {
                }
                tvValue.setCompoundDrawablesWithIntrinsicBounds(0, 0, item.rightDrawable, 0)
                if (item.isFavourite) {
                    tvValue.singleClick {
                        favButtonClick()
                    }
                    tvValue.setTextAppearance(tvValue.context, R.style.SubHeading1)
                } else {
                    tvValue.singleClick {
                    }
                }
            }
        }
    }

    inner class ExpandedViewHolder(private val binding: LayoutBillDetailExpandedBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind() {
            with(binding) {
                btnViewAll.icon =
                    btnViewAll.context.getDrawableCompat(
                        if (!showExpandedItem) {
                            R.drawable.ic_chevron_down
                        } else {
                            R.drawable.ic_chevron_up
                        }
                    )
                btnViewAll.text =
                    if (!showExpandedItem) {
                        btnViewAll.context.getString(R.string.view_all)
                    } else {
                        btnViewAll.context.getString(
                            R.string.label_close
                        )
                    }
                btnViewAll.singleClick {
                    expandButtonClick(!showExpandedItem)
                }
            }
        }
    }

    fun setData(
        list: List<BillDetailData>,
        showButtonItem: Boolean = false,
        showExpandedItem: Boolean = false
    ) {
        this.list = list
        this.showButtonItem = showButtonItem
        this.showExpandedItem = showExpandedItem
        notifyDataSetChanged()
    }
}
