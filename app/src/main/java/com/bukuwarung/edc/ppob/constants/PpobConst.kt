package com.bukuwarung.edc.ppob.constants

import com.bukuwarung.edc.R

object PpobConst {
    const val CATEGORY_PULSA = "PULSA"
    const val CATEGORY_LISTRIK = "LISTRIK" // To be used for category for Prepaid listrik
    const val CATEGORY_PLN_POSTPAID = "PLN_POSTPAID"
    const val CATEGORY_LISTRIK_POSTPAID =
        "LISTRIK_POSTPAID" // To be used for category for Postpaid listrik
    const val CATEGORY_EWALLET = "EMONEY"
    const val CATEGORY_PAKET_DATA = "PAKET_DATA"
    const val CATEGORY_PULSA_POSTPAID = "PASCABAYAR"
    const val CATEGORY_VOUCHER_GAME = "VOUCHER_GAME"
    const val CATEGORY_BPJS = "BPJS"
    const val CATEGORY_PDAM = "PDAM"
    const val CATEGORY_MULTIFINANCE = "ANGSURAN"
    const val CATEGORY_INTERNET_DAN_TV_CABLE = "INTERNET_DAN_TV_KABEL"
    const val CATEGORY_VEHICLE_TAX = "VEHICLE_TAX"
    const val CATEGORY_SET_SELLING_PRICE = "SET_SELLING_PRICE"
    const val CATEGORY_PROMOTIONS = "PROMOTIONS"
    const val CATEGORY_TRAIN_TICKET = "TRAIN_TICKET"
    const val ADULT_TYPE = "A"
    const val INFANT_TYPE = "I"

    const val VOUCHER_TYPE_TOPUP = "TOPUP"

    const val AXIS = "AXIS"
    const val INDOSAT = "INDOSAT"
    const val TELKOMSEL = "TELKOMSEL"
    const val XL = "XL"
    const val SMARTFREN = "SMARTFREN"
    const val TRI = "TRI"

    const val BILLER = "biller"

    const val ACTIVE = "ACTIVE"
    const val IN_ACTIVE = "INACTIVE"
    const val NOT_SUPPORTED = "NOT_SUPPORTED"
    const val BLOCKED = "BLOCKED"

    const val TELKOMSEL_HALO = "TELKOMSEL_HALO"
    const val INDOSAT_MATRIX = "INDOSAT_MATRIX"
    const val SMARTFREN_PASCABAYAR = "SMARTFREN_PASCABAYAR"
    const val TRI_PASCABAYAR = "TRI_PASCABAYAR"
    const val XL_PASCABAYAR = "XL_PASCABAYAR"

    const val REMINDER_ORDER_CREATED = "REMINDER_ORDER_CREATED"
    const val REMINDER_ORDER_FAILED = "REMINDER_ORDER_FAILED"
    const val REMINDER_ORDER_EXPIRED = "REMINDER_ORDER_EXPIRED"
    const val ELIGIBLE_FOR_REMINDER = "ELIGIBLE_FOR_REMINDER"
    const val REMINDER_BILL_CHECKED = "REMINDER_BILL_CHECKED"
    const val REMINDER_SENT = "REMINDER_SENT"
    const val REMINDER_PAID = "REMINDER_PAID"
    const val START = "start"
    const val QUERY_COUNT = "count"
    const val QUERY_PREFIX = "prefix"
    const val QUERY_STATUS = "status"
    const val TAB_BILLS_LIST = "BILLS_LIST"
    const val TAB_PAID_BILLS = "PAID_BILLS"
    const val SEMUA_FILTER = "semua_filter"
    const val ALL_CATEGORY = "all_category"
    const val LIST_COUNT = "10"
    const val ADMIN_FEE_TYPE = "ADMINFEE"
    const val CASHBACK_TYPE = "CASHBACK"
    const val SALDO_METHOD = "SALDO"
    const val ALL_METHOD = "ALL"
    const val SALDO_FAMILY_CODE = "BUKU"
    const val ADD_TO_FAV_NUDGE = "ADD_TO_FAV_NUDGE"
    const val UI_VARIANT_UNIVERSAL_CHECKOUT = "UI_VARIANT_UNIVERSAL_CHECKOUT"
    const val CHANGE_SELLING_PRICE_NUDGE = "CHANGE_SELLING_PRICE_NUDGE"
    const val DISABLE = "DISABLE"

    const val SCREEN_CATALOG_BOTTOM_SHEET = "SCREEN_CATALOG_BOTTOM_SHEET"
    const val SCREEN_CATALOG_ACTIVITY = "SCREEN_CATALOG_ACTIVITY"
    const val CHECKOUT_TOKEN = "CHECKOUT_TOKEN"

    const val RECOMMENDATION_CONTACTS_COUNT = 5
    const val BILL_ALREADY_PAID_CODE = 462

    const val USERTYPE_TREATMENT = "TREATMENT"
    const val USERTYPE_CONTROL = "CONTROL"

    private const val PREPAID_PULSA_ACTIVITY_CLASSPATH =
        "com.bukuwarung.payments.ppob.pulsa.view.PpobPulsaActivity"
    private const val LISTRIK_ACTIVITY_CLASSPATH =
        "com.bukuwarung.payments.ppob.listrik.view.PpobListrikActivity"
    private const val CHOOSE_PRODUCT_ACTIVITY_CLASSPATH =
        "com.bukuwarung.payments.ppob.choose.ChooseProductActivity"
    private const val POSTPAID_PULSA_ACTIVITY_CLASSPATH =
        "com.bukuwarung.payments.ppob.pulsa.view.PostpaidPulsaActivity"
    private const val BPJS_ACTVITY_ACTIVITY_CLASSPATH =
        "com.bukuwarung.payments.ppob.bpjs.view.PpobBpjsActivity"
    private const val PDAM_ACTIVITY_CLASSPATH =
        "com.bukuwarung.payments.ppob.waterbills.view.PpobWaterBillsActivity"
    private const val INTERNET_AND_TV_CABLE_ACTIVITY_CLASSPATH =
        "com.bukuwarung.payments.ppob.internetdantvcable.view.PpobInternetDanTvCableActivity"
    private const val MULTIFINANCE_ACTIVITY_CLASSPATH =
        "com.bukuwarung.payments.ppob.multifinance.view.PpobMultifinanceActivity"
    private const val VEHICLE_TAX_ACTIVITY_CLASSPATH =
        "com.bukuwarung.payments.ppob.vehicletax.view.PpobVehicleTaxActivity"
    private const val REMINDERS_ACTIVITY_CLASSPATH =
        "com.bukuwarung.payments.ppob.reminders.view.ReminderActivity"
    private const val SET_SELLING_PRICE_ACTIVITY_CLASSPATH =
        "com.bukuwarung.payments.ppob.catalog.view.CatalogActivity"
    private const val PROMOTIONS_ACTIVITY_CLASSPATH =
        "com.bukuwarung.payments.ppob.catalog.view.PromotionActivity"
    private const val WEBVIEW_ACTIVITY_CLASSPATH = "com.bukuwarung.activities.WebviewActivity"

    private const val VEHICLE_TAX_BILLER_SAMSAT_JATIM = "SAMSAT_JATIM"
    val VEHICLE_TAX_TYPE1_BILLERS = arrayListOf(VEHICLE_TAX_BILLER_SAMSAT_JATIM)

    const val REDIRECTION_URL_CONTAINS = "/redirect"
    const val DEFAULT_POLLING_TIME = 5 * 60
    const val DEFAULT_POLLING_INTERVAL = 10

    val PROVIDER_NUMBER_MAP = HashMap<String, String>().apply {
        put("0811", TELKOMSEL)
        put("0812", TELKOMSEL)
        put("0813", TELKOMSEL)
        put("0851", TELKOMSEL)
        put("0852", TELKOMSEL)
        put("0853", TELKOMSEL)
        put("0821", TELKOMSEL)
        put("0822", TELKOMSEL)
        put("0823", TELKOMSEL)
        put("0817", XL)
        put("0818", XL)
        put("0819", XL)
        put("0877", XL)
        put("0878", XL)
        put("0859", XL)
        put("0814", INDOSAT)
        put("0815", INDOSAT)
        put("0816", INDOSAT)
        put("0855", INDOSAT)
        put("0856", INDOSAT)
        put("0857", INDOSAT)
        put("0858", INDOSAT)
        put("0831", AXIS)
        put("0832", AXIS)
        put("0833", AXIS)
        put("0834", AXIS)
        put("0835", AXIS)
        put("0836", AXIS)
        put("0837", AXIS)
        put("0838", AXIS)
        put("0839", AXIS)
        put("0895", TRI)
        put("0896", TRI)
        put("0897", TRI)
        put("0898", TRI)
        put("0899", TRI)
        put("0881", SMARTFREN)
        put("0882", SMARTFREN)
        put("0883", SMARTFREN)
        put("0884", SMARTFREN)
        put("0885", SMARTFREN)
        put("0886", SMARTFREN)
        put("0887", SMARTFREN)
        put("0888", SMARTFREN)
        put("0889", SMARTFREN)
    }
    val PROVIDER_ICON_MAP = HashMap<String, Int>().apply {
        put(TELKOMSEL, R.drawable.ic_telkomsel)
        put(AXIS, R.mipmap.ic_axis)
        put(INDOSAT, R.drawable.ic_indosat_matrix)
        put(XL, R.mipmap.ic_xl)
        put(SMARTFREN, R.mipmap.ic_smartfren)
        put(TRI, R.mipmap.ic_tri)
    }
    val CATEGORY_NAME_MAP = HashMap<String, Int>().apply {
        put(CATEGORY_PULSA, R.string.pulsa)
        put(CATEGORY_LISTRIK, R.string.token_listrik)
        put(CATEGORY_EWALLET, R.string.ewallet)
        put(CATEGORY_PAKET_DATA, R.string.packet_data)
        put(CATEGORY_PLN_POSTPAID, R.string.listrik_postpaid)
        put(CATEGORY_LISTRIK_POSTPAID, R.string.listrik_postpaid)
        put(CATEGORY_PULSA_POSTPAID, R.string.tagihan_pascabayar_capital)
        put(CATEGORY_VOUCHER_GAME, R.string.voucher_game1)
        put(CATEGORY_BPJS, R.string.bpjs)
        put(CATEGORY_PDAM, R.string.title_pdam)
        put(CATEGORY_MULTIFINANCE, R.string.title_multifinance)
        put(CATEGORY_INTERNET_DAN_TV_CABLE, R.string.title_internet_dan_tv_cable)
        put(CATEGORY_VEHICLE_TAX, R.string.title_vehicle_tax)
        put(CATEGORY_TRAIN_TICKET, R.string.title_train)
    }
    val CATEGORY_ANALYTICS_MAP = HashMap<String?, String>().apply {
        put(CATEGORY_PULSA, PpobAnalytics.PPOB_PULSA)
        put(CATEGORY_LISTRIK, PpobAnalytics.PPOB_LISTRIK)
        put(CATEGORY_EWALLET, PpobAnalytics.PPOB_EWALLET)
        put(CATEGORY_PAKET_DATA, PpobAnalytics.PPOB_PACKET_DATA)
        put(CATEGORY_VOUCHER_GAME, PpobAnalytics.PPOB_GAMING_VOUCHERS)
        put(CATEGORY_PLN_POSTPAID, PpobAnalytics.LISTRIK_POSTPAID)
        put(CATEGORY_LISTRIK_POSTPAID, PpobAnalytics.LISTRIK_POSTPAID)
        put(CATEGORY_PULSA_POSTPAID, PpobAnalytics.PPOB_PULSA_POSTPAID)
        put(CATEGORY_BPJS, PpobAnalytics.PPOB_BPJS)
        put(CATEGORY_PDAM, PpobAnalytics.PPOB_PDAM)
        put(CATEGORY_MULTIFINANCE, PpobAnalytics.PPOB_MULTIFINANCE)
        put(CATEGORY_INTERNET_DAN_TV_CABLE, PpobAnalytics.PPOB_CABLE)
        put(CATEGORY_VEHICLE_TAX, PpobAnalytics.PPOB_VEHICLE_TAX)
        put(CATEGORY_SET_SELLING_PRICE, PpobAnalytics.SET_PPOB_SELLING_PRICE)
        put(CATEGORY_PROMOTIONS, PpobAnalytics.TOOLS_PROMOTION)
        put(CATEGORY_TRAIN_TICKET, PpobAnalytics.EVENT_TRAIN_TICKETS)
    }
    val CATEGORY_BUY_BUTTON_ANALYTICS_MAP = HashMap<String?, String>().apply {
        put(CATEGORY_PULSA, PpobAnalytics.EVENT_PPOB_PULSA_BUY_BUTTON)
        put(CATEGORY_PULSA_POSTPAID, PpobAnalytics.EVENT_PPOB_POSTPAID_PULSA_BUY_BUTTON)
        put(CATEGORY_LISTRIK, PpobAnalytics.EVENT_PPOB_LISTRIK_BUY_BUTTON)
        put(CATEGORY_PLN_POSTPAID, PpobAnalytics.EVENT_PPOB_POSTPAID_LISTRIK_BUY_BUTTON)
        put(CATEGORY_LISTRIK_POSTPAID, PpobAnalytics.EVENT_PPOB_POSTPAID_LISTRIK_BUY_BUTTON)
        put(CATEGORY_EWALLET, PpobAnalytics.EVENT_PPOB_EWALLET_BUY_BUTTON)
        put(CATEGORY_PAKET_DATA, PpobAnalytics.EVENT_PPOB_PAKET_DATA_BUY_BUTTON)
        put(CATEGORY_VOUCHER_GAME, PpobAnalytics.EVENT_PPOB_GAMING_VOUCHER_BUY_BUTTON)
        put(CATEGORY_BPJS, PpobAnalytics.EVENT_PPOB_BPJS_BUY_BUTTON)
        put(CATEGORY_PDAM, PpobAnalytics.EVENT_PPOB_PDAM_BUY_BUTTON)
        put(CATEGORY_MULTIFINANCE, PpobAnalytics.EVENT_PPOB_MULTIFINANCE_BUY_BUTTON)
        put(
            CATEGORY_INTERNET_DAN_TV_CABLE,
            PpobAnalytics.EVENT_PPOB_INTERNET_DAN_TV_CABLE_BUY_BUTTON
        )
        put(CATEGORY_VEHICLE_TAX, PpobAnalytics.EVENT_PPOB_VEHICLE_TAX_BUY_BUTTON)
        put(CATEGORY_SET_SELLING_PRICE, PpobAnalytics.PPOB_BUY_BUTTON)
        put(CATEGORY_PROMOTIONS, PpobAnalytics.PPOB_BUY_BUTTON)
        put(CATEGORY_TRAIN_TICKET, PpobAnalytics.PPOB_BUY_BUTTON)
    }

    val CATEGORY_DEFAULT_ICON = HashMap<String, Int>().apply {
        put(CATEGORY_MULTIFINANCE, R.mipmap.ic_multifinance_default)
        put(CATEGORY_INTERNET_DAN_TV_CABLE, R.mipmap.ic_internet_dan_tv_cable_default)
        put(CATEGORY_VEHICLE_TAX, R.drawable.ic_vehicle_tax_default)
    }

    val CATEGORY_ICON = HashMap<String, Int>().apply {
        put(CATEGORY_PULSA, R.drawable.ic_pulsa)
        put(CATEGORY_LISTRIK, R.drawable.ic_electricity)
        put(CATEGORY_EWALLET, R.drawable.ic_ewallet)
        put(CATEGORY_PAKET_DATA, R.drawable.ic_paket_data)
        put(CATEGORY_LISTRIK_POSTPAID, R.drawable.ic_listrik_postpaid)
        put(CATEGORY_PULSA_POSTPAID, R.drawable.ic_pascabayar)
        put(CATEGORY_VOUCHER_GAME, R.drawable.ic_voucher_game)
        put(CATEGORY_BPJS, R.drawable.ic_insurance)
        put(CATEGORY_PDAM, R.drawable.ic_water_works)
        put(CATEGORY_MULTIFINANCE, R.drawable.ic_multifinance)
        put(CATEGORY_INTERNET_DAN_TV_CABLE, R.drawable.ic_internet_dan_tv_cable)
        put(CATEGORY_VEHICLE_TAX, R.drawable.ic_vehicle_tax)
    }

    val CATEGORY_NAME = HashMap<String, Int>().apply {
        put(CATEGORY_PULSA, R.string.pulsa)
        put(CATEGORY_LISTRIK, R.string.listrik_category)
        put(CATEGORY_EWALLET, R.string.ewallet)
        put(CATEGORY_PAKET_DATA, R.string.packet_data)
        put(CATEGORY_PLN_POSTPAID, R.string.listrik_category)
        put(CATEGORY_LISTRIK_POSTPAID, R.string.listrik_category)
        put(CATEGORY_PULSA_POSTPAID, R.string.pulsa)
        put(CATEGORY_VOUCHER_GAME, R.string.voucher_game1)
        put(CATEGORY_BPJS, R.string.bpjs)
        put(CATEGORY_PDAM, R.string.title_pdam)
        put(CATEGORY_MULTIFINANCE, R.string.title_multifinance)
        put(CATEGORY_INTERNET_DAN_TV_CABLE, R.string.title_internet_dan_tv_cable)
        put(CATEGORY_VEHICLE_TAX, R.string.title_vehicle_tax)
        put(CATEGORY_TRAIN_TICKET, R.string.title_train)
    }

    val CATEGORY_HELP_URL = HashMap<String, String>().apply {
        put(CATEGORY_PULSA, PpobRemoteConfig.getPpobConfigs().supportUrls.pulsa)
        put(CATEGORY_LISTRIK, PpobRemoteConfig.getPpobConfigs().supportUrls.tokenListrik)
        put(CATEGORY_EWALLET, PpobRemoteConfig.getPpobConfigs().supportUrls.eWallet)
        put(CATEGORY_PAKET_DATA, PpobRemoteConfig.getPpobConfigs().supportUrls.packetData)
        put(CATEGORY_PLN_POSTPAID, PpobRemoteConfig.getPpobConfigs().supportUrls.postpaidListrik)
        put(
            CATEGORY_LISTRIK_POSTPAID,
            PpobRemoteConfig.getPpobConfigs().supportUrls.postpaidListrik
        )
        put(CATEGORY_PULSA_POSTPAID, PpobRemoteConfig.getPpobConfigs().supportUrls.pulsa)
        put(CATEGORY_VOUCHER_GAME, PpobRemoteConfig.getPpobConfigs().supportUrls.voucherGame)
        put(CATEGORY_BPJS, PpobRemoteConfig.getPpobConfigs().supportUrls.bpjs)
        put(CATEGORY_PDAM, PpobRemoteConfig.getPpobConfigs().supportUrls.pdam)
        put(CATEGORY_MULTIFINANCE, PpobRemoteConfig.getPpobConfigs().supportUrls.multiFinance)
        put(
            CATEGORY_INTERNET_DAN_TV_CABLE,
            PpobRemoteConfig.getPpobConfigs().supportUrls.internetTvCable
        )
        put(CATEGORY_VEHICLE_TAX, PpobRemoteConfig.getPpobConfigs().supportUrls.vehicleTax)
        put(CATEGORY_TRAIN_TICKET, PpobRemoteConfig.getPpobConfigs().supportUrls.trainTickets)
    }

    val CATEGORY_SHOW_REMINDER_ICON = HashMap<String, Boolean>().apply {
        put(CATEGORY_PULSA, false)
        put(CATEGORY_LISTRIK, false)
        put(CATEGORY_EWALLET, false)
        put(CATEGORY_PAKET_DATA, false)
        put(CATEGORY_PLN_POSTPAID, true)
        put(CATEGORY_LISTRIK_POSTPAID, true)
        put(CATEGORY_PULSA_POSTPAID, true)
        put(CATEGORY_VOUCHER_GAME, false)
        put(CATEGORY_BPJS, false)
        put(CATEGORY_PDAM, true)
        put(CATEGORY_MULTIFINANCE, false)
        put(CATEGORY_INTERNET_DAN_TV_CABLE, false)
        put(CATEGORY_VEHICLE_TAX, false)
        put(CATEGORY_TRAIN_TICKET, false)
    }

    val BILLER_MAP = HashMap<String?, String>().apply {
        put(TELKOMSEL, TELKOMSEL_HALO)
        put(INDOSAT, INDOSAT_MATRIX)
        put(XL, XL_PASCABAYAR)
        put(SMARTFREN, SMARTFREN_PASCABAYAR)
        put(TRI, TRI_PASCABAYAR)
    }

    fun getListOfTypesWithNotes() = arrayListOf(
        CATEGORY_PULSA_POSTPAID,
        CATEGORY_BPJS,
        CATEGORY_PDAM,
        CATEGORY_MULTIFINANCE,
        CATEGORY_INTERNET_DAN_TV_CABLE,
        CATEGORY_VEHICLE_TAX
    )

    const val PULSA_BANTUAN =
        "https://bukuwarung1.zohodesk.com/portal/id/kb/top-up-dan-tagihan/pulsa"
    const val TOKEN_LISTRIK_BANTUAN =
        "https://bukuwarung1.zohodesk.com/portal/id/kb/top-up-dan-tagihan/token-listrik"
    const val EWALLET_BANTUAN =
        "https://bukuwarung1.zohodesk.com/portal/id/kb/top-up-dan-tagihan/e-wallet"
    const val PAKET_DATA_BANTUAN =
        "https://bukuwarung1.zohodesk.com/portal/id/kb/top-up-dan-tagihan/paket-data"
    const val TAGIHAN_LISTRIK_BANTUAN =
        "https://bukuwarung1.zohodesk.com/portal/id/kb/top-up-dan-tagihan/tagihan-listrik"
    const val VOUCHER_GAME_BANTUAN =
        "https://bukuwarung1.zohodesk.com/portal/id/kb/top-up-dan-tagihan/voucher-game"
    const val BPJS_BANTUAN = "https://bukuwarung1.zohodesk.com/portal/id/kb/top-up-dan-tagihan/bpjs"
    const val PDAM_BANTUAN = "https://bukuwarung1.zohodesk.com/portal/id/kb/top-up-dan-tagihan/pdam"
    const val MULTIFINANCE_BANTUAN =
        "https://bukuwarung1.zohodesk.com/portal/id/kb/top-up-dan-tagihan/angsuran-kredit"
    const val INTERNET_DAN_TV_CABLE_BANTUAN =
        "https://bukuwarung1.zohodesk.com/portal/id/kb/top-up-dan-tagihan/internet-tv-kabel"
    const val VEHICLE_TAX_BANTUAN =
        "https://bukuwarung1.zohodesk.com/portal/id/kb/top-up-dan-tagihan/e-samsat"
    const val TRAIN_TICKET_BANTUAN =
        "https://bukuwarung1.zohodesk.com/portal/id/kb/top-up-dan-tagihan/tiket-kereta-api"
}
