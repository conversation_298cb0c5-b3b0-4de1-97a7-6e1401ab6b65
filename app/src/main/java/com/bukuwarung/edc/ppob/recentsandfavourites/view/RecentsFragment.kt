package com.bukuwarung.edc.ppob.recentsandfavourites.view

import android.app.Activity
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.FragmentRecentsBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.payments.ui.contacts.PaymentContactActivity
import com.bukuwarung.edc.payments.ui.history.OrderHistoryActivity
import com.bukuwarung.edc.ppob.common.model.PpobEvent
import com.bukuwarung.edc.ppob.constants.PpobAnalytics
import com.bukuwarung.edc.ppob.constants.PpobUtils
import com.bukuwarung.edc.ppob.recentsandfavourites.adapter.RecentsItemAdapter
import com.bukuwarung.edc.ppob.recentsandfavourites.model.ProfilesItem
import com.bukuwarung.edc.ppob.recentsandfavourites.viewmodel.RecentAndFavouriteViewModel
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.showView
import com.bukuwarung.ui_component.base.BaseErrorView
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class RecentsFragment : Fragment() {

    private val viewModel: RecentAndFavouriteViewModel by activityViewModels()
    private var fragmentRecentBinding: FragmentRecentsBinding? = null
    private val binding get() = fragmentRecentBinding!!
    private val category by lazy { arguments?.getString(CATEGORY).orEmpty() }
    private var profilesItem: ProfilesItem? = null
    private val recentAdapter: RecentsItemAdapter by lazy {
        RecentsItemAdapter(
            clickAction = {
                val map = HashMap<String, String>()
                map[PpobAnalytics.ENTRY_POINT] = PpobAnalytics.PPOB_BUY_PAGE
                Analytics.trackEvent(PpobAnalytics.EVENT_CLICK_REORDER, map)
                viewModel.setData(it)
            },
            seeAllClickAction = {
                startHistoryActivity()
            },
            clickFavourite = {
                loadUserContactActivity(it)
            },
            clickDeleteFavourite = {
                this.profilesItem = it
                PpobUtils.showRemoveFavouriteDialog(requireContext()) {
                    it.id?.let { id -> viewModel.removeFavourite(id, requireContext()) }
                }
            }
        )
    }

    companion object {
        private const val KEY_CUSTOMER_ID = "customerId"
        private const val BILLER = "billerCode"
        private const val MESSAGE = "message"
        private const val KEY_BOOK_ID = "bookId"
        private const val KEY_PAYMENT_TYPE = "paymentType"
        private const val CATEGORY = "category"
        private const val BILLER_CODE = "biller_code"
        fun createIntent(category: String = "", billerCode: String = ""): RecentsFragment {
            val bundle = Bundle().apply {
                putString(CATEGORY, category)
                putString(BILLER_CODE, billerCode)
            }
            return RecentsFragment().apply {
                arguments = bundle
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        fragmentRecentBinding = FragmentRecentsBinding.inflate(layoutInflater, container, false)
        viewModel.getPaymentList(category)
        setupView()
        subscribeState()
        return binding.root
    }

    private fun setupView() {
        with(binding) {
            bukuErrorView.addCallback(errorViewCallBack)
            rvRecent.layoutManager = LinearLayoutManager(context)
            rvRecent.adapter = recentAdapter
        }
    }

    private fun subscribeState() {
        viewModel.eventStatus.observe(viewLifecycleOwner) {
            when (it) {
                is PpobEvent.RefreshFavourite -> {
                    Toast.makeText(
                        context,
                        it.message,
                        Toast.LENGTH_LONG
                    ).show()
                    if (it.refreshFavourite) {
                        refreshRecentsTab()
                    }
                }

                else -> {}
            }
        }

        viewModel.observeEventState.observe(viewLifecycleOwner) {
            when (it) {
                is RecentAndFavouriteViewModel.Event.ShowPaymentList -> {
                    if (category == it.category) {
                        binding.rvRecent.showView()
                        binding.bukuErrorView.hideView()
                        binding.includeEmpty.root.hideView()
                        recentAdapter.setData(it.list)
                    }
                }

                is RecentAndFavouriteViewModel.Event.ShowRecentError -> {
                    with(binding) {
                        bukuErrorView.showView()
                        rvRecent.hideView()
                        includeEmpty.root.hideView()
                        if (it.showServerError) {
                            bukuErrorView.setErrorType(
                                BaseErrorView.Companion.ErrorType.CUSTOM,
                                getString(R.string.sorry_disturbance),
                                getString(R.string.try_later),
                                getString(R.string.reload),
                                R.mipmap.ic_server_busy
                            )
                        } else {
                            bukuErrorView.setErrorType(
                                BaseErrorView.Companion.ErrorType.CUSTOM,
                                getString(R.string.no_connection_title),
                                getString(R.string.no_connection_message),
                                getString(R.string.reload),
                                R.mipmap.ic_no_inet
                            )
                        }
                    }
                }

                is RecentAndFavouriteViewModel.Event.ShowRecentEmpty -> {
                    with(binding) {
                        rvRecent.hideView()
                        bukuErrorView.hideView()
                        includeEmpty.root.showView()
                    }
                }

                else -> {}
            }
        }
    }

    private var errorViewCallBack = object : BaseErrorView.Callback {
        override fun ctaClicked() {
            viewModel.getPaymentList(category)
        }

        override fun messageClicked() {}
    }

    override fun onDestroyView() {
        super.onDestroyView()
        fragmentRecentBinding = null
    }

    private fun refreshRecentsTab() {
        viewModel.getPaymentList(category)
        viewModel.invalidateDataSource()
    }

    private fun startHistoryActivity(paymentType: Int = 5) {
        context?.openActivity(OrderHistoryActivity::class.java) {
            putString(OrderHistoryActivity.BOOK_ID, Utils.getPaymentAccountId())
        }
    }

    private fun loadUserContactActivity(orderId: String) {
        startPaymentContactActivityForResult.launch(
            PaymentContactActivity.createIntent(
                requireContext(),
                orderId,
                PpobAnalytics.PPOB_BUY_PAGE
            )
        )
    }

    private val startPaymentContactActivityForResult = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result: ActivityResult ->
        if (result.resultCode == Activity.RESULT_OK) {
            Toast.makeText(
                context,
                result.data?.getStringExtra(MESSAGE),
                Toast.LENGTH_LONG
            ).show()
            viewModel.getPaymentList(category)
            viewModel.invalidateDataSource()
        }
    }
}
