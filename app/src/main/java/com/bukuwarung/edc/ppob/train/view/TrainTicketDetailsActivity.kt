package com.bukuwarung.edc.ppob.train.view

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.CountDownTimer
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.ActivityTrainTicketDetailsBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.homepage.ui.home.HomePageActivity
import com.bukuwarung.edc.payments.data.model.OrderResponse
import com.bukuwarung.edc.payments.data.model.TrainPassenger
import com.bukuwarung.edc.ppob.baseactivity.viewmodel.PpobViewModel
import com.bukuwarung.edc.ppob.common.model.FinproAddCartRequest
import com.bukuwarung.edc.ppob.common.model.FinproBeneficiary
import com.bukuwarung.edc.ppob.common.model.PpobEvent
import com.bukuwarung.edc.ppob.confirmation.view.PpobOrderFormActivity
import com.bukuwarung.edc.ppob.constants.PpobAnalytics
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.edc.ppob.constants.PpobRemoteConfig
import com.bukuwarung.edc.ppob.train.adapter.TrainPassengerAdapter
import com.bukuwarung.edc.ppob.train.adapter.TrainPassengerAdapter.Companion.MIN_SIZE
import com.bukuwarung.edc.ppob.train.dialog.TrainWebviewExitDialog
import com.bukuwarung.edc.util.DateTimeUtils
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.getDrawableCompat
import com.bukuwarung.edc.util.goToDestination
import com.bukuwarung.edc.util.isNotNullOrEmpty
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.singleClick
import com.bukuwarung.edc.webview.WebviewActivity
import com.bukuwarung.edc.webview.constant.ClassConstants
import com.bukuwarung.ui_component.base.BaseErrorView
import com.bukuwarung.ui_component.utils.hideView
import com.bukuwarung.ui_component.utils.setSingleClickListener
import com.bukuwarung.ui_component.utils.showView
import com.bumptech.glide.Glide
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class TrainTicketDetailsActivity : AppCompatActivity() {

    lateinit var binding: ActivityTrainTicketDetailsBinding
    private val viewModel: PpobViewModel by viewModels()
    private var orderResponse: OrderResponse? = null
    private var adapter: TrainPassengerAdapter? = null
    private var enrollmentId = ""
    private var timer: CountDownTimer? = null

    companion object {
        const val ENROLLMENT_ID = "enrollment_id"
        fun createIntent(context: Context, enrollmentId: String = ""): Intent {
            val i = Intent(context, TrainTicketDetailsActivity::class.java)
            i.putExtra(ENROLLMENT_ID, enrollmentId)
            i.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            return i
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityTrainTicketDetailsBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setupView()
        subscribeState()
    }

    private fun setupView() {
        setToolBarView()
        binding.bukuErrorView.addCallback(errorViewCallBack)
        fetchTrainTicketDetails()
        binding.btnCompletePayment.setSingleClickListener { navigateToOrderFormScreen() }
    }

    private fun fetchTrainTicketDetails() {
        enrollmentId = intent?.getStringExtra(ENROLLMENT_ID).orEmpty()
        viewModel.addToCart(
            FinproAddCartRequest(
                sku = "",
                beneficiary = FinproBeneficiary(
                    category = PpobConst.CATEGORY_TRAIN_TICKET,
                    accountNumber = enrollmentId,
                    code = ""
                )
            )
        )
    }

    private fun navigateToOrderFormScreen() {
        startActivity(
            PpobOrderFormActivity.createIntent(
                context = this,
                orderDetail = orderResponse,
                type = PpobConst.CATEGORY_TRAIN_TICKET
            )
        )
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        enrollmentId = intent?.getStringExtra(ENROLLMENT_ID).orEmpty()
        fetchTrainTicketDetails()
    }

    private fun subscribeState() {
        viewModel.viewState.observe(this) {
            with(binding) {
                when {
                    it.showLoading -> {
                        pbProgress.showView()
                        svView.hideView()
                        btnCompletePayment.hideView()
                        bukuErrorView.hideView()
                    }
                }
            }
        }

        viewModel.observeEvent.observe(this) {
            with(binding) {
                when (it) {
                    is PpobEvent.ToDetail -> {
                        orderResponse = it.orderDetail
                        svView.showView()
                        bukuErrorView.hideView()
                        pbProgress.hideView()
                        btnCompletePayment.showView()
                        setTrainDetails(it.orderDetail)
                        val item = it.orderDetail?.items?.firstOrNull()
                        val timestamp = item?.details?.paymentExpirationTimestamp
                        if (timestamp.isNotNullOrEmpty()) {
                            setExpiryTimer(timestamp.orEmpty())
                        }
                        trackFetchBillEvent(PpobAnalytics.STATUS_SUCCESS)
                    }

                    is PpobEvent.InternetError -> {
                        showErrorView()
                        bukuErrorView.setErrorType(
                            BaseErrorView.Companion.ErrorType.CONNECTION_ERROR
                        )
                        trackFetchBillEvent(PpobAnalytics.STATUS_FAILED, it.message)
                    }

                    is PpobEvent.ServerError -> {
                        showErrorView()
                        bukuErrorView.setErrorType(BaseErrorView.Companion.ErrorType.SERVER_ERROR)
                        trackFetchBillEvent(PpobAnalytics.STATUS_FAILED, it.message)
                    }

                    is PpobEvent.OtherError -> {
                        showErrorView()
                        bukuErrorView.setErrorType(
                            BaseErrorView.Companion.ErrorType.SERVER_UNREACHABLE
                        )
                        trackFetchBillEvent(PpobAnalytics.STATUS_FAILED, it.message)
                    }

                    else -> {}
                }
            }
        }
    }

    private fun trackFetchBillEvent(status: String, reason: String? = "") {
        val map = hashMapOf<String, String>()
        map[PpobAnalytics.ID] = enrollmentId
        map[PpobAnalytics.TYPE] = PpobAnalytics.EVENT_TRAIN_TICKETS
        map[PpobAnalytics.STATUS] = status
        map[PpobAnalytics.REASON] = reason.orEmpty()
        Analytics.trackEvent(PpobAnalytics.EVENT_PPOB_FETCH_BILL, map)
    }

    private fun showErrorView() = binding.apply {
        svView.hideView()
        bukuErrorView.showView()
        pbProgress.hideView()
        btnCompletePayment.hideView()
    }

    private fun setTrainDetails(orderResponse: OrderResponse?) {
        with(binding) {
            val details = orderResponse?.items?.get(0)?.details
            Glide.with(this@TrainTicketDetailsActivity)
                .load(orderResponse?.metadata?.logo)
                .error(
                    PpobConst.CATEGORY_DEFAULT_ICON[PpobConst.CATEGORY_TRAIN_TICKET]
                        ?: R.mipmap.ic_biller_default
                )
                .into(ivLogo)
            tvTrainName.text = details?.trainName
            tvTrainClass.text = details?.trainWagonName
            tvSrcStation.text = details?.trainOriginStationCode
            tvSrcStationCode.text = String.format("(%s)", details?.trainOriginStationCode)
            tvDestStation.text = details?.trainDestinationStationName
            tvDestStationCode.text = String.format("(%s)", details?.trainDestinationStationCode)
            tvDepartTime.text = details?.trainDepartureTime
            billDetailView.binding.tvDetailTransaksi.text = getString(R.string.order_details_train)
            billDetailView.setView(orderResponse, isTrainBillDetails = true)
            rvPassengerDetail.layoutManager = LinearLayoutManager(this@TrainTicketDetailsActivity)
            var passengers = details?.trainPassenger.orEmpty() as ArrayList
            adapter = TrainPassengerAdapter(callback, passengers.size)
            if (passengers.size >= MIN_SIZE) {
                passengers = ArrayList(passengers.subList(0, MIN_SIZE - 2))
                passengers.add(TrainPassenger(viewType = 1))
            }
            rvPassengerDetail.adapter = adapter
            adapter?.setData(passengers)
        }
    }

    private fun setExpiryTimer(expiredAt: String) {
        binding.includeTimer.root.showView()
        timer = object : CountDownTimer(DateTimeUtils.getInvoiceExpiryTime(expiredAt), 1000) {
            override fun onTick(millisUntilFinished: Long) {
                binding.includeTimer.tvExpiredTime.text =
                    DateTimeUtils.millisecondsToTime(millisUntilFinished)
            }

            override fun onFinish() {
                binding.btnCompletePayment.isEnabled = false
                PaymentExpiredDialog(
                    context = this@TrainTicketDetailsActivity,
                    action = {
                        goToDestination(HomePageActivity::class.java)
                    }
                ).show()
                timer?.cancel()
            }
        }
        timer?.start()
    }

    private var errorViewCallBack = object : BaseErrorView.Callback {
        override fun ctaClicked() {
            fetchTrainTicketDetails()
        }

        override fun messageClicked() {
            fetchTrainTicketDetails()
        }
    }

    private val callback = object : TrainPassengerAdapter.Callback {
        override fun loadMoreCtaClicked() {
            val list = orderResponse?.items?.get(0)?.details?.trainPassenger.orEmpty() as ArrayList
            list.add(TrainPassenger(viewType = 2))
            adapter?.setData(list)
        }

        override fun loadLessCtaClicked() {
            var passengers =
                orderResponse?.items?.get(0)?.details?.trainPassenger.orEmpty() as ArrayList
            passengers.removeAll {
                it.viewType == 2
            }
            if (passengers.size >= MIN_SIZE) {
                passengers = ArrayList(passengers.subList(0, MIN_SIZE - 2))
                passengers.add(TrainPassenger(viewType = 1))
            }
            adapter?.setData(passengers)
        }
    }

    private fun setToolBarView() {
        with(binding.includeToolBar) {
            toolBarLabel.text = getString(R.string.train_ticket_title)
            tbPpob.navigationIcon = getDrawableCompat(R.drawable.ic_arrow_back)
            tbPpob.setNavigationOnClickListener {
                Utils.hideKeyboard(this@TrainTicketDetailsActivity)
                showDialog()
            }
            ivHelp.singleClick {
                Utils.hideKeyboard(this@TrainTicketDetailsActivity)
                openActivity(WebviewActivity::class.java) {
                    putString(
                        ClassConstants.WEBVIEW_URL,
                        PpobRemoteConfig.getPpobConfigs().supportUrls.trainTickets
                    )
                }
            }
        }
    }

    private fun showDialog() {
        val dialog = TrainWebviewExitDialog(this, {
            if (!it) {
                viewModel.cancelOrder(Utils.getPaymentAccountId(), orderResponse?.orderId.orEmpty())
                goToDestination(HomePageActivity::class.java)
            }
        }, {})
        dialog.show()
    }

    override fun onBackPressed() {
        super.onBackPressed()
        showDialog()
    }

    override fun onDestroy() {
        timer?.cancel()
        super.onDestroy()
    }
}
