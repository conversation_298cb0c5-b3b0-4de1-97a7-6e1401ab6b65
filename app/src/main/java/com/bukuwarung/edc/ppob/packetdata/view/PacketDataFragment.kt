package com.bukuwarung.edc.ppob.packetdata.view

import android.content.res.ColorStateList
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.graphics.BlendModeColorFilterCompat
import androidx.core.graphics.BlendModeCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.FragmentPacketDataBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.payments.data.model.OrderResponse
import com.bukuwarung.edc.ppob.common.adapter.PpobProductAdapter
import com.bukuwarung.edc.ppob.common.model.FinproAddCartRequest
import com.bukuwarung.edc.ppob.common.model.FinproBeneficiary
import com.bukuwarung.edc.ppob.common.model.PpobEvent
import com.bukuwarung.edc.ppob.common.view.PaymentDownBottomSheet
import com.bukuwarung.edc.ppob.confirmation.view.PpobOrderFormActivity
import com.bukuwarung.edc.ppob.constants.PpobAnalytics
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.edc.ppob.packetdata.viewmodel.PacketDataViewModel
import com.bukuwarung.edc.ppob.recentsandfavourites.model.ProfilesItem
import com.bukuwarung.edc.ppob.recentsandfavourites.view.RecentAndFavouriteFragment
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.afterTextChanged
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.getColorCompat
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.isNotNullOrEmpty
import com.bukuwarung.edc.util.setDrawable
import com.bukuwarung.edc.util.showView
import com.bukuwarung.payments.data.model.PpobProduct
import com.bukuwarung.ui_component.utils.setSingleClickListener
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class PacketDataFragment :
    Fragment(),
    RecentAndFavouriteFragment.IRecentAndFavCommunicator {
    private var fragmentPacketDataBinding: FragmentPacketDataBinding? = null
    private val binding get() = fragmentPacketDataBinding!!
    private val viewModel: PacketDataViewModel by viewModels()
    private lateinit var adapter: PpobProductAdapter
    private var ppobProductList: List<PpobProduct>? = null
    private var category = PpobConst.CATEGORY_PAKET_DATA
    private var isSpecialProductSelected = false

    companion object {
        private const val ARG_CUSTOMER_ID = "customer_id"
        private const val FROM = "from"
        fun createIntent(from: String, customerId: String? = ""): PacketDataFragment {
            val bundle = Bundle().apply {
                putString(ARG_CUSTOMER_ID, customerId)
                putString(FROM, from)
            }
            return PacketDataFragment().apply {
                arguments = bundle
            }
        }
    }

    override fun onCreateView(
        layoutInflater: LayoutInflater,
        viewGroup: ViewGroup?,
        bundle: Bundle?
    ): View {
        fragmentPacketDataBinding =
            FragmentPacketDataBinding.inflate(layoutInflater, viewGroup, false)
        setupView()
        subscribeState()
        return binding.root
    }

    override fun onResume() {
        super.onResume()
        addRecentsAndFavourites()
    }

    private fun setupView() {
        with(binding) {
            adapter = PpobProductAdapter({
                Utils.hideKeyboard(requireActivity())
                it.sku?.let { sku ->
                    viewModel.addToCart(
                        FinproAddCartRequest(
                            sku = sku,
                            beneficiary = FinproBeneficiary(
                                category = category,
                                number = bivCustomerNumber.getText(),
                                code = it.productInfo?.biller.orEmpty()
                            ),
                            userType = PpobConst.USERTYPE_TREATMENT
                        )
                    )
                }
            }, category)
            bivCustomerNumber.requestFocus()
            rvProducts.layoutManager = LinearLayoutManager(requireContext())
            rvProducts.adapter = adapter
            //noinspection AndroidLintClickableViewAccessibility
            rvProducts.setOnTouchListener { _, _ ->
                Utils.hideKeyboard(requireActivity())
                false
            }
            bivCustomerNumber.onTextChanged {
                bivCustomerNumber.setClearDrawable()
                viewModel.checkCustomerNumberValidation(it)
            }

            setSearchIconDrawableColor(R.color.black_10)
            binding.etSearch.afterTextChanged { searchText ->
                if (searchText.isNotBlank()) {
                    binding.tvSearchResult.showView()
                    setSearchIconDrawableColor(R.color.colorPrimary)
                } else {
                    binding.tvSearchResult.hideView()
                    setSearchIconDrawableColor(R.color.black_10)
                }
                if (!ppobProductList.isNullOrEmpty()) {
                    viewModel.onSearchTextChanged(searchText, ppobProductList!!)
                }
            }

            tvListText.text = getString(R.string.choose_ppob_packet_data)
            val customerId = arguments?.getString(ARG_CUSTOMER_ID)
            if (customerId.isNotNullOrEmpty()) {
                bivCustomerNumber.setText(customerId)
            }

            includePpobFilters.chipRegularProducts.setSingleClickListener {
                etSearch.hideView()
                isSpecialProductSelected = false
                viewModel.changeProductsBasedOnFilterSelected(false)
            }
            includePpobFilters.chipPromotionalProducts.setSingleClickListener {
                etSearch.hideView()
                isSpecialProductSelected = true
                viewModel.changeProductsBasedOnFilterSelected(true)
            }
        }
    }

    private fun setSearchIconDrawableColor(drawable: Int) {
        binding.etSearch.backgroundTintList =
            ColorStateList.valueOf(requireContext().getColorCompat(drawable))
        val drawables = binding.etSearch.compoundDrawables
        if (drawables.isNotEmpty() && drawables[0] != null) {
            drawables[0].colorFilter = BlendModeColorFilterCompat.createBlendModeColorFilterCompat(
                requireContext().getColorCompat(drawable),
                BlendModeCompat.SRC_ATOP
            )
        }
    }

    private fun addRecentsAndFavourites() {
        childFragmentManager.beginTransaction().add(
            binding.flRecentAndFav.id,
            RecentAndFavouriteFragment.createIntent(category)
        ).commit()
    }

    private fun subscribeState() {
        viewModel.observeEvent.observe(viewLifecycleOwner) {
            binding.layoutEmptyPlaceholder.root.hideView()
            when (it) {
                is PpobEvent.ShowProductsList -> {
                    with(binding) {
                        flRecentAndFav.hideView()
                        rvProducts.showView()
                        tvListText.showView()
                        if (!it.isFilteredCall) {
                            ppobProductList = it.list
                            etSearch.text = null
                            when {
                                isSpecialProductSelected -> {
                                    etSearch.hideView()
                                }

                                it.list.isNotEmpty() -> {
                                    etSearch.showView()
                                }

                                else -> {
                                    etSearch.hideView()
                                    tvSearchResult.hideView()
                                }
                            }
                            if (it.list.isEmpty()) {
                                binding.layoutEmptyPlaceholder.root.showView()
                            }
                        } else {
                            val listSize = it.list.size
                            if (listSize > 0) {
                                tvSearchResult.text =
                                    getString(R.string.search_product_list, listSize)
                                etSearch.setTextColor(
                                    requireContext().getColorCompat(R.color.colorPrimary)
                                )
                            } else {
                                tvSearchResult.text = getString(R.string.search_product_empty_list)
                                etSearch.backgroundTintList =
                                    ColorStateList.valueOf(
                                        requireContext().getColorCompat(R.color.red_80)
                                    )
                                etSearch.setTextColor(
                                    requireContext().getColorCompat(R.color.red_80)
                                )
                                setSearchIconDrawableColor(R.color.red_80)
                            }
                        }
                    }
                    adapter.setData(it.list, it.searchTerm)
                }

                is PpobEvent.ServerError -> {
                    showPaymentDownBottomSheet(true, it.message)
                }

                is PpobEvent.InternetError -> {
                    showPaymentDownBottomSheet(false, it.message)
                }

                is PpobEvent.ToDetail -> {
                    trackBillFetchEvent(it.orderDetail)
                    startActivity(
                        PpobOrderFormActivity.createIntent(
                            context = requireActivity(),
                            orderDetail = it.orderDetail,
                            type = category
                        )
                    )
                }

                else -> {
                }
            }
        }
        viewModel.viewState.observe(viewLifecycleOwner) {
            with(binding) {
                progressBar.visibility = it.showLoading.asVisibility()
                if (it.showShimmer) {
                    includeShimmer.sflLayout.showShimmer(true)
                    includeShimmer.root.showView()
                    rvProducts.hideView()
                    binding.layoutEmptyPlaceholder.root.hideView()
                } else {
                    includeShimmer.sflLayout.hideShimmer()
                    includeShimmer.root.hideView()
                }
                when {
                    it.numberInvalid -> {
                        bivCustomerNumber.setErrorMessage(getString(R.string.phone_number_invalid))
                        adapter.enableButtonToggle(false)
                    }

                    it.numberLengthInvalid -> {
                        bivCustomerNumber.setErrorMessage(
                            getString(R.string.phone_number_length_invalid)
                        )
                        adapter.enableButtonToggle(false)
                    }

                    else -> {
                        bivCustomerNumber.setSuccessState("")
                        adapter.enableButtonToggle(true)
                    }
                }

                if (it.providerLogo == 0) {
                    tvListText.hideView()
                    rvProducts.hideView()
                    flRecentAndFav.showView()
                    binding.layoutEmptyPlaceholder.root.hideView()
                } else {
                    tvListText.showView()
                    tvListText.setDrawable(right = it.providerLogo)
                }
            }
        }
    }

    private fun trackBillFetchEvent(orderDetail: OrderResponse?) {
        val map = hashMapOf<String, String>()
        map[PpobAnalytics.NOMINAL] = orderDetail?.amount.toString()
        map[PpobAnalytics.TYPE] = PpobAnalytics.PPOB_PACKET_DATA
        Analytics.trackEvent(PpobAnalytics.EVENT_PPOB_SELECT_PACK, map)
    }

    private fun showPaymentDownBottomSheet(isServiceDown: Boolean, message: String? = null) {
        val paymentDownBottomSheet = PaymentDownBottomSheet.createInstance(isServiceDown, message)
        paymentDownBottomSheet.show(childFragmentManager, PaymentDownBottomSheet.TAG)
    }

    override fun setData(profilesItem: ProfilesItem) {
        binding.bivCustomerNumber.setText(profilesItem.details?.accountNumber)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        fragmentPacketDataBinding?.bivCustomerNumber?.removeTextChanged()
        fragmentPacketDataBinding?.rvProducts?.adapter = null
        fragmentPacketDataBinding = null
    }
}
