package com.bukuwarung.edc.ppob.recentsandfavourites.view

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.FragmentFavouriteBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.payments.data.model.PagingStatus
import com.bukuwarung.edc.ppob.common.model.PpobEvent
import com.bukuwarung.edc.ppob.constants.PpobAnalytics
import com.bukuwarung.edc.ppob.constants.PpobUtils
import com.bukuwarung.edc.ppob.recentsandfavourites.adapter.FavouriteItemAdapter
import com.bukuwarung.edc.ppob.recentsandfavourites.model.ProfilesItem
import com.bukuwarung.edc.ppob.recentsandfavourites.viewmodel.RecentAndFavouriteViewModel
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.showView
import com.bukuwarung.ui_component.base.BaseErrorView
import com.bukuwarung.ui_component.utils.setSingleClickListener
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class FavouriteFragment : Fragment() {

    private val viewModel: RecentAndFavouriteViewModel by activityViewModels()
    private var fragmentFavouriteBinding: FragmentFavouriteBinding? = null
    private val binding get() = fragmentFavouriteBinding!!
    private var profilesItem: ProfilesItem? = null
    private val favouriteProductAdapter by lazy {
        FavouriteItemAdapter(removeFavourite = { profilesItem ->
            this.profilesItem = profilesItem
            PpobUtils.showRemoveFavouriteDialog(requireContext()) {
                profilesItem?.id?.let { viewModel.removeFavourite(it, requireContext()) }
            }
        }, clickAction = { profilesItem ->
            val map = HashMap<String, String>()
            map[PpobAnalytics.ENTRY_POINT] = PpobAnalytics.PPOB_BUY_PAGE
            Analytics.trackEvent(PpobAnalytics.EVENT_CLICK_REORDER, map)
            viewModel.setData(profilesItem)
        })
    }

    companion object {
        private const val CATEGORY = "category"
        fun createIntent(category: String = ""): FavouriteFragment {
            val bundle = Bundle().apply {
                putString(CATEGORY, category)
            }
            return FavouriteFragment().apply {
                arguments = bundle
            }
        }
    }

    override fun onCreateView(
        layoutInflater: LayoutInflater,
        viewGroup: ViewGroup?,
        bundle: Bundle?
    ): View {
        fragmentFavouriteBinding =
            FragmentFavouriteBinding.inflate(layoutInflater, viewGroup, false)
        setupView()
        subscribeState()
        return binding.root
    }

    private fun setupView() {
        binding.bukuErrorView.addCallback(errorViewCallBack)
        binding.rvFavourite.apply {
            layoutManager = LinearLayoutManager(context)
            itemAnimator = DefaultItemAnimator()
            adapter = favouriteProductAdapter
        }
    }

    private fun refreshFavourite() {
        viewModel.invalidateDataSource()
    }

    private fun subscribeState() {
        viewModel.eventStatus.observe(viewLifecycleOwner) {
            when (it) {
                is PpobEvent.RefreshFavourite -> {
                    Toast.makeText(
                        context,
                        it.message,
                        Toast.LENGTH_LONG
                    ).show()
                    if (it.refreshFavourite) {
                        refreshFavourite()
                    }
                }

                else -> {}
            }
        }

        viewModel.productData?.observe(viewLifecycleOwner) {
            favouriteProductAdapter.submitList(it)
        }

        viewModel.pagingStatus.observe(viewLifecycleOwner) { status ->
            when (status) {
                PagingStatus.Loading -> {
                    with(binding) {
                        tvTitle.hideView()
                        bukuErrorView.hideView()
                        includeLoading.root.showView()
                        includeEmpty.root.hideView()
                        rvFavourite.hideView()
                        includeLoading.root.startShimmer()
                        includeLoading.root.showShimmer(true)
                    }
                }

                is PagingStatus.Loaded -> {
                    binding.apply {
                        tvTitle.showView()
                        bukuErrorView.hideView()
                        rvFavourite.showView()
                        includeLoading.root.stopShimmer()
                        includeLoading.root.showShimmer(false)
                        includeLoading.root.hideView()
                        includeEmpty.root.hideView()
                    }
                }

                PagingStatus.Empty -> {
                    binding.apply {
                        tvTitle.hideView()
                        bukuErrorView.hideView()
                        includeEmpty.root.showView()
                        rvFavourite.hideView()
                        includeLoading.root.stopShimmer()
                        includeLoading.root.showShimmer(false)
                        includeLoading.root.hideView()
                        includeEmpty.btFavourite.setSingleClickListener {
                            viewModel.showRecent(hideKeyboard = false)
                        }
                    }
                }

                is PagingStatus.Error -> {
                    with(binding) {
                        tvTitle.hideView()
                        bukuErrorView.showView()
                        rvFavourite.hideView()
                        includeEmpty.root.hideView()
                        includeLoading.root.stopShimmer()
                        includeLoading.root.showShimmer(false)
                        includeLoading.root.hideView()
                        bukuErrorView.setErrorType(
                            BaseErrorView.Companion.ErrorType.CUSTOM,
                            getString(R.string.sorry_disturbance),
                            getString(R.string.try_later),
                            getString(R.string.reload),
                            R.mipmap.ic_server_busy
                        )
                    }
                }

                PagingStatus.NoInternet -> {
                    with(binding) {
                        tvTitle.hideView()
                        bukuErrorView.showView()
                        includeEmpty.root.hideView()
                        rvFavourite.hideView()
                        includeLoading.root.stopShimmer()
                        includeLoading.root.showShimmer(false)
                        includeLoading.root.hideView()
                        bukuErrorView.setErrorType(
                            BaseErrorView.Companion.ErrorType.CUSTOM,
                            getString(R.string.no_connection_title),
                            getString(R.string.no_connection_message),
                            getString(R.string.reload),
                            R.mipmap.ic_no_inet
                        )
                    }
                }

                else -> {}
            }
        }
    }

    private var errorViewCallBack = object : BaseErrorView.Callback {
        override fun ctaClicked() {
            refreshFavourite()
        }

        override fun messageClicked() {}
    }

    override fun onDestroyView() {
        super.onDestroyView()
        fragmentFavouriteBinding = null
    }
}
