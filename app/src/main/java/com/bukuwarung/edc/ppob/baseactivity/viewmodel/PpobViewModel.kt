package com.bukuwarung.edc.ppob.baseactivity.viewmodel

import android.content.Context
import androidx.fragment.app.Fragment
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.edc.BuildConfig
import com.bukuwarung.edc.R
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.payments.usecase.FinproUseCase
import com.bukuwarung.edc.ppob.baseactivity.model.PpobErrorResponse
import com.bukuwarung.edc.ppob.bpjs.view.BpjsFragment
import com.bukuwarung.edc.ppob.common.model.FinproAddCartRequest
import com.bukuwarung.edc.ppob.common.model.PpobEvent
import com.bukuwarung.edc.ppob.common.model.PpobViewState
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.edc.ppob.constants.PpobRemoteConfig
import com.bukuwarung.edc.ppob.ewallet.view.EWalletFragment
import com.bukuwarung.edc.ppob.internetandtvcable.view.InternetAndTvCableFragment
import com.bukuwarung.edc.ppob.listrik.postpaid.view.PostpaidListrikFragment
import com.bukuwarung.edc.ppob.listrik.prepaid.view.PrepaidListrikFragment
import com.bukuwarung.edc.ppob.multifinance.view.MultifinanceFragment
import com.bukuwarung.edc.ppob.packetdata.view.PacketDataFragment
import com.bukuwarung.edc.ppob.pdam.view.PdamFragment
import com.bukuwarung.edc.ppob.pulsa.postpaid.view.PostpaidPulsaFragment
import com.bukuwarung.edc.ppob.pulsa.prepaid.view.PrepaidPulsaFragment
import com.bukuwarung.edc.ppob.train.model.TrainEnrollmentRequest
import com.bukuwarung.edc.ppob.vehicletax.view.VehicleTaxFragment
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.errorMessage
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.orNil
import com.bukuwarung.payments.data.model.PpobListItem
import com.bukuwarung.payments.data.model.PpobProduct
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.gson.Gson
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

@HiltViewModel
open class PpobViewModel @Inject constructor(private val finproUseCase: FinproUseCase) :
    ViewModel() {

    val eventStatus = MutableLiveData<PpobEvent>()
    val observeEvent: LiveData<PpobEvent> = eventStatus
    protected fun currentViewState(): PpobViewState? = viewState.value
    val viewState: MutableLiveData<PpobViewState> = MutableLiveData(PpobViewState())

    private suspend fun setEventStatus(event: PpobEvent) = withContext(Dispatchers.Main) {
        eventStatus.value = event
    }

    private suspend fun setViewState(ppobViewState: PpobViewState?) =
        withContext(Dispatchers.Main) {
            viewState.value = ppobViewState!!
        }

    // To get PPOB products V2 API
    fun getPpobProductsWithBillerDetails(
        category: String,
        map: Map<String, String>,
        isSpecialProduct: Boolean? = null
    ) = viewModelScope.launch {
        setViewState(currentViewState()?.copy(showShimmer = true))
        try {
            finproUseCase.getPpobProductsWithBillerDetails(category, map, isSpecialProduct).let {
                if (it.isSuccessful) {
                    it.body()?.productsList?.let { productsList ->
                        val list = productsList.toMutableList()
                        val popularIndex = productsList.indexOfFirst { ppobProduct ->
                            ppobProduct.productInfo?.isPopular?.toBoolean() == true
                        }
                        if (popularIndex > 0) {
                            val product = productsList[popularIndex]
                            list.removeAt(popularIndex)
                            list.add(0, product)
                        }
                        setViewState(currentViewState()?.copy(showShimmer = false))
                        setEventStatus(
                            PpobEvent.ShowProductsList(
                                list,
                                billerDetails = it.body()?.billerDetails
                            )
                        )
                    }
                } else {
                    setViewState(currentViewState()?.copy(showShimmer = false))
                    if (it.errorMessage() != Constant.NO_INTERNET_ERROR_MESSAGE) {
                        setEventStatus(PpobEvent.ServerError(it.errorMessage()))
                        setEventStatus(PpobEvent.SearchServerError(it.errorMessage()))
                    } else {
                        setEventStatus(PpobEvent.InternetError(it.errorMessage()))
                        setEventStatus(PpobEvent.SearchInternetError(it.errorMessage()))
                    }
                }
            }
        } catch (e: Exception) {
            setEventStatus(PpobEvent.ServerError(""))
            setEventStatus(PpobEvent.SearchServerError(""))
        }
    }

    fun addToCart(request: FinproAddCartRequest) = viewModelScope.launch {
        setViewState(currentViewState()?.copy(showLoading = true))
        try {
            finproUseCase.addItemToCart(Utils.getPaymentAccountId(), request).let {
                if (it.isSuccessful) {
                    setViewState(currentViewState()?.copy(showLoading = false))
                    setEventStatus(PpobEvent.ToDetail(it.body()))
                } else {
                    setViewState(currentViewState()?.copy(showLoading = false))
                    val errorResponse = Gson().fromJson(
                        it.errorBody()?.string(),
                        PpobErrorResponse::class.java
                    )
                    if (errorResponse.message != Constant.NO_INTERNET_ERROR_MESSAGE) {
                        /* statusCode:
                        461 -> invalid number, 462 -> bill already paid, 467 -> failure in the provider system, 468 -> Inquiry Failed
                         */
                        if (it.code() in 400..499) {
                            setEventStatus(PpobEvent.OtherError(errorResponse.message))
                        } else {
                            setEventStatus(PpobEvent.ServerError(errorResponse.message))
                        }
                    }
                }
            }
        } catch (e: Exception) {
            setEventStatus(PpobEvent.OtherError(""))
        }
    }

    fun getTrainTicketUrl() = viewModelScope.launch {
        val request = TrainEnrollmentRequest(productCategory = PpobConst.CATEGORY_TRAIN_TICKET)
        try {
            finproUseCase.getTrainTicketUrl(Utils.getPaymentAccountId(), request).let {
                if (it.isSuccessful) {
                    setViewState(currentViewState()?.copy(showLoading = false))
                    setEventStatus(PpobEvent.ShowTrainWebView(it.body()))
                } else {
                    setViewState(currentViewState()?.copy(showLoading = false))
                    if (it.errorMessage() != Constant.NO_INTERNET_ERROR_MESSAGE) {
                        setEventStatus(PpobEvent.ServerError(it.errorMessage()))
                    } else {
                        setEventStatus(PpobEvent.InternetError(it.errorMessage()))
                    }
                }
            }
        } catch (e: Exception) {
            setEventStatus(PpobEvent.ServerError(""))
        }
    }

    fun getTrainEnrollmentDetail(enrollmentId: String) = viewModelScope.launch(Dispatchers.IO) {
        try {
            finproUseCase.getTrainEnrollmentDetail(Utils.getPaymentAccountId(), enrollmentId).let {
                if (it.isSuccessful) {
                    it.body()?.let { response ->
                        setEventStatus(PpobEvent.ShowTrainTicketDetail(response))
                    }
                }
            }
        } catch (_: Exception) {
        }
    }

    fun setFragmentList(
        versionFilteredList: List<PpobListItem>?,
        from: String,
        accountNumber: String,
        phoneNumber: String,
        code: String,
        layoutType: String,
        machineNumber: String,
        frameNumber: String,
        category: String
    ): List<Fragment> {
        val ppobFragmentList = ArrayList<Fragment>()
        versionFilteredList?.forEach {
            when (it.category) {
                PpobConst.CATEGORY_PULSA -> {
                    ppobFragmentList.add(
                        PrepaidPulsaFragment.createIntent(
                            from,
                            getValueBasedOnCategory(category, it.category, accountNumber)
                        )
                    )
                }

                PpobConst.CATEGORY_EWALLET -> {
                    ppobFragmentList.add(
                        EWalletFragment.createIntent(
                            from,
                            getValueBasedOnCategory(category, it.category, accountNumber),
                            getValueBasedOnCategory(category, it.category, code)
                        )
                    )
                }

                PpobConst.CATEGORY_INTERNET_DAN_TV_CABLE -> {
                    ppobFragmentList.add(
                        InternetAndTvCableFragment.createIntent(
                            from,
                            getValueBasedOnCategory(category, it.category, accountNumber),
                            getValueBasedOnCategory(category, it.category, code),
                            getValueBasedOnCategory(category, it.category, phoneNumber)
                        )
                    )
                }

                PpobConst.CATEGORY_PULSA_POSTPAID -> {
                    ppobFragmentList.add(
                        PostpaidPulsaFragment.createIntent(
                            from,
                            getValueBasedOnCategory(category, it.category, accountNumber)
                        )
                    )
                }

                PpobConst.CATEGORY_MULTIFINANCE -> {
                    ppobFragmentList.add(
                        MultifinanceFragment.createIntent(
                            from,
                            getValueBasedOnCategory(category, it.category, accountNumber),
                            getValueBasedOnCategory(category, it.category, code),
                            getValueBasedOnCategory(category, it.category, phoneNumber)
                        )
                    )
                }

                PpobConst.CATEGORY_VEHICLE_TAX -> {
                    ppobFragmentList.add(
                        VehicleTaxFragment.createIntent(
                            from,
                            getValueBasedOnCategory(category, it.category, accountNumber),
                            getValueBasedOnCategory(category, it.category, code),
                            getValueBasedOnCategory(category, it.category, phoneNumber),
                            getValueBasedOnCategory(category, it.category, layoutType),
                            getValueBasedOnCategory(category, it.category, machineNumber),
                            getValueBasedOnCategory(category, it.category, frameNumber)
                        )
                    )
                }

                PpobConst.CATEGORY_PLN_POSTPAID, PpobConst.CATEGORY_LISTRIK_POSTPAID -> {
                    ppobFragmentList.add(
                        PostpaidListrikFragment.createIntent(
                            from,
                            getValueBasedOnCategory(category, it.category, accountNumber),
                            getValueBasedOnCategory(category, it.category, phoneNumber)
                        )
                    )
                }

                PpobConst.CATEGORY_LISTRIK -> {
                    ppobFragmentList.add(
                        PrepaidListrikFragment.createIntent(
                            from,
                            getValueBasedOnCategory(category, it.category, accountNumber),
                            getValueBasedOnCategory(category, it.category, phoneNumber)
                        )
                    )
                }

                PpobConst.CATEGORY_PAKET_DATA -> {
                    ppobFragmentList.add(
                        PacketDataFragment.createIntent(
                            from,
                            getValueBasedOnCategory(category, it.category, accountNumber)
                        )
                    )
                }

                PpobConst.CATEGORY_BPJS -> {
                    ppobFragmentList.add(
                        BpjsFragment.createIntent(
                            from,
                            getValueBasedOnCategory(category, it.category, accountNumber),
                            getValueBasedOnCategory(category, it.category, code),
                            getValueBasedOnCategory(category, it.category, phoneNumber)
                        )
                    )
                }

                PpobConst.CATEGORY_PDAM -> {
                    ppobFragmentList.add(
                        PdamFragment.createIntent(
                            from,
                            getValueBasedOnCategory(category, it.category, accountNumber),
                            getValueBasedOnCategory(category, it.category, code),
                            getValueBasedOnCategory(category, it.category, phoneNumber)
                        )
                    )
                }

                PpobConst.CATEGORY_VOUCHER_GAME -> {
                    ppobFragmentList.add(Fragment())
                }

                PpobConst.CATEGORY_TRAIN_TICKET -> {
                    ppobFragmentList.add(Fragment())
                }
            }
        }
        return ppobFragmentList
    }

    private fun getValueBasedOnCategory(
        selectedCategory: String,
        category: String,
        value: String
    ): String = if (selectedCategory == category) value else ""

    fun getVersionList() = PpobRemoteConfig.getPpobConfigs().ppobList?.filter {
        it.startVersion.orNil <=
            BuildConfig.VERSION_CODE &&
            (it.endVersion.orNil >= BuildConfig.VERSION_CODE || it.endVersion == -1)
    }
        ?.sortedBy { it.rank }

    fun checkPhoneNumberValidity(number: String): Boolean {
        var phoneNumber = Utils.beautifyPhoneNumber(number)
        if (phoneNumber?.startsWith("8").isTrue) phoneNumber = "0$phoneNumber"
        viewState.value =
            currentViewState()?.copy(
                numberLengthInvalid =
                phoneNumber?.length.orNil > 13 || phoneNumber?.length.orNil < 10
            )
        return !(phoneNumber?.length.orNil > 13 || phoneNumber?.length.orNil < 10)
    }

    fun onSearchTextChanged(searchText: String, ppobProductList: List<PpobProduct>) =
        viewModelScope.launch {
            if (searchText.isNotBlank()) {
                setEventStatus(
                    PpobEvent.ShowProductsList(
                        list = ppobProductList.filter {
                            it.name?.contains(
                                searchText,
                                ignoreCase = true
                            ) ?: false
                        },
                        isFilteredCall = true,
                        searchTerm = searchText
                    )
                )
            } else {
                setEventStatus(
                    PpobEvent.ShowProductsList(
                        list = ppobProductList,
                        isFilteredCall = true,
                        searchTerm = searchText
                    )
                )
            }
        }

    fun removeFavourite(favouriteId: String, context: Context) =
        viewModelScope.launch(Dispatchers.IO) {
            try {
                finproUseCase.deleteFavourite(Utils.getPaymentAccountId(), favouriteId).let {
                    if (it.isSuccessful) {
                        setEventStatus(
                            PpobEvent.RefreshFavourite(
                                message = it.body()?.message
                                    ?: context.getString(R.string.remove_favourite_success_msg),
                                refreshFavourite = true
                            )
                        )
                    } else {
                        setEventStatus(
                            PpobEvent.RefreshFavourite(
                                message = it.errorMessage(),
                                false
                            )
                        )
                    }
                }
            } catch (e: Exception) {
                setEventStatus(
                    PpobEvent.RefreshFavourite(
                        message = "",
                        false
                    )
                )
            }
        }

    fun isVoucherGame(category: String) = category == PpobConst.CATEGORY_VOUCHER_GAME
    fun isTrainTicket(category: String) = category == PpobConst.CATEGORY_TRAIN_TICKET

    fun cancelOrder(accountId: String, orderId: String) = viewModelScope.launch(Dispatchers.IO) {
        try {
            finproUseCase.cancelOrder(accountId, orderId)
        } catch (ex: Exception) {
            FirebaseCrashlytics.getInstance().recordException(ex)
        }
    }
}
