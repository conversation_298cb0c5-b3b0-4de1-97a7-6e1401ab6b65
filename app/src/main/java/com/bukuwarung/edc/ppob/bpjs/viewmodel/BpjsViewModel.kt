package com.bukuwarung.edc.ppob.bpjs.viewmodel

import android.content.Context
import com.bukuwarung.edc.R
import com.bukuwarung.edc.payments.usecase.FinproUseCase
import com.bukuwarung.edc.ppob.baseactivity.viewmodel.PpobViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class BpjsViewModel @Inject constructor(finproUseCase: FinproUseCase) :
    PpobViewModel(finproUseCase) {

    fun checkCustomerNumberValidation(customerNumber: String = "", context: Context): Boolean {
        val numberLengthInvalid: Boolean = customerNumber.length !in 11..16
        viewState.value = currentViewState()?.copy(
            numberLengthInvalid = numberLengthInvalid,
            errorMessage = context.getString(R.string.invalid_card_number)
        )
        return numberLengthInvalid
    }
}
