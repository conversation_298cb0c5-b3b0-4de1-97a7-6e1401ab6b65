package com.bukuwarung.edc.ppob.pdam.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.LayoutWaterBillItemBinding
import com.bukuwarung.edc.util.getColorCompat
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick
import com.bukuwarung.payments.data.model.PpobProduct

class WaterBillAreaAdapter(
    private var list: List<PpobProduct>,
    private val clickAction: (PpobProduct) -> Unit
) : RecyclerView.Adapter<WaterBillAreaAdapter.AreaViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AreaViewHolder {
        val itemBinding =
            LayoutWaterBillItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return AreaViewHolder(itemBinding, clickAction)
    }

    override fun onBindViewHolder(holder: AreaViewHolder, position: Int) {
        holder.bind(list[position])
    }

    override fun getItemCount(): Int = list.size

    fun setData(list: List<PpobProduct>) {
        this.list = list
        notifyDataSetChanged()
    }

    class AreaViewHolder(
        private val binding: LayoutWaterBillItemBinding,
        private val clickAction: (PpobProduct) -> Unit
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind(ppobProduct: PpobProduct) {
            binding.tvName.text = ppobProduct.name
            binding.tvPdamName.text = ppobProduct.productInfo?.billerName
            if (ppobProduct.active == true) {
                binding.tvError.hideView()
                binding.root.singleClick {
                    clickAction(ppobProduct)
                }
            } else {
                binding.tvError.showView()
                binding.root.singleClick {
                }
            }
            binding.tvPdamName.setTextColor(
                ContextCompat.getColor(
                    binding.root.context,
                    if (ppobProduct.active.isTrue) R.color.black_40 else R.color.black_10
                )
            )
            if (ppobProduct.isSelected) {
                binding.tvName.setTextColor(
                    binding.clItem.context.getColorCompat(R.color.colorPrimary)
                )
                binding.clItem.setBackgroundColor(
                    binding.clItem.context.getColorCompat(R.color.blue_5)
                )
                binding.ivSelected.showView()
            } else {
                binding.tvName.setTextColor(
                    binding.clItem.context.getColorCompat(
                        if (ppobProduct.active.isTrue) R.color.black_80 else R.color.black_20
                    )
                )
                binding.clItem.setBackgroundColor(
                    binding.clItem.context.getColorCompat(R.color.white)
                )
                binding.ivSelected.hideView()
            }
        }
    }
}
