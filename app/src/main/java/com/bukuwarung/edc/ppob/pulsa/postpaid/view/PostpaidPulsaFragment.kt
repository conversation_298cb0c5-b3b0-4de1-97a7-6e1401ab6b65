package com.bukuwarung.edc.ppob.pulsa.postpaid.view

import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.FragmentPostpaidPulsaBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.payments.data.model.OrderResponse
import com.bukuwarung.edc.ppob.common.model.FinproAddCartRequest
import com.bukuwarung.edc.ppob.common.model.FinproBeneficiary
import com.bukuwarung.edc.ppob.common.model.PpobEvent
import com.bukuwarung.edc.ppob.common.view.PaymentDownBottomSheet
import com.bukuwarung.edc.ppob.common.view.PpobBillDetailsBottomSheet
import com.bukuwarung.edc.ppob.constants.PpobAnalytics
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.edc.ppob.pulsa.postpaid.viewmodel.PostpaidPulsaViewModel
import com.bukuwarung.edc.ppob.recentsandfavourites.model.ProfilesItem
import com.bukuwarung.edc.ppob.recentsandfavourites.view.RecentAndFavouriteFragment
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.isNotNullOrEmpty
import com.bukuwarung.ui_component.utils.setSingleClickListener
import com.github.razir.progressbutton.hideProgress
import com.github.razir.progressbutton.showProgress
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class PostpaidPulsaFragment :
    Fragment(),
    RecentAndFavouriteFragment.IRecentAndFavCommunicator,
    PpobBillDetailsBottomSheet.PpobBillDetailsBsListener {

    private var _binding: FragmentPostpaidPulsaBinding? = null
    private val binding get() = _binding!!
    private val category = PpobConst.CATEGORY_PULSA_POSTPAID
    private var addToCartResponse: OrderResponse? = null
    private var recentAndFavouriteFragment: RecentAndFavouriteFragment? = null

    private val viewModel: PostpaidPulsaViewModel by viewModels()

    private val accountNumber by lazy { arguments?.getString(ACCOUNT_NUMBER) }
    private val from by lazy { arguments?.getString(FROM) }

    companion object {
        private const val ACCOUNT_NUMBER = "ACCOUNT_NUMBER"
        private const val FROM = "FROM"
        fun createIntent(from: String, accountNumber: String): PostpaidPulsaFragment {
            val bundle = Bundle().apply {
                putString(ACCOUNT_NUMBER, accountNumber)
                putString(FROM, from)
            }
            return PostpaidPulsaFragment().apply {
                arguments = bundle
            }
        }
    }

    override fun onCreateView(
        layoutInflater: LayoutInflater,
        viewGroup: ViewGroup?,
        bundle: Bundle?
    ): View {
        _binding = FragmentPostpaidPulsaBinding.inflate(layoutInflater, viewGroup, false)
        setupView()
        subscribeState()
        return binding.root
    }

    private fun setupView() {
        addRecentsAndFavourites()
        with(binding) {
            bivCustomerNumber.setFocus()
            Utils.showKeyboard(requireActivity())
            bivCustomerNumber.onTextChanged {
                bivCustomerNumber.setClearDrawable()
                viewModel.checkPhoneNumberValidity(bivCustomerNumber.getText())
            }
            if (accountNumber.isNotNullOrEmpty()) {
                bivCustomerNumber.setText(accountNumber)
                btnCek.callOnClick()
            }
            btnCek.setSingleClickListener {
                Utils.hideKeyboard(requireActivity())
                addToCart()
            }
        }
    }

    private fun addToCart() {
        val phoneNumber = Utils.beautifyPhoneNumber(binding.bivCustomerNumber.getText())
        val lastPhonePrefix = phoneNumber?.substring(0, 4)
        val biller = PpobConst.PROVIDER_NUMBER_MAP[lastPhonePrefix]
        viewModel.addToCart(
            FinproAddCartRequest(
                sku = "",
                beneficiary = FinproBeneficiary(
                    category = category,
                    accountNumber = binding.bivCustomerNumber.getText(),
                    code = PpobConst.BILLER_MAP[biller] ?: biller ?: ""
                ),
                userType = PpobConst.USERTYPE_TREATMENT
            )
        )
    }

    private fun addRecentsAndFavourites() {
        recentAndFavouriteFragment = RecentAndFavouriteFragment.createIntent(category).also {
            childFragmentManager.beginTransaction().add(
                binding.flRecentAndFav.id,
                it
            ).commit()
        }
    }

    private fun subscribeState() {
        viewModel.observeEvent.observe(viewLifecycleOwner) {
            when (it) {
                is PpobEvent.ToDetail -> {
                    addToCartResponse = it.orderDetail
                    trackFetchBillEvent()
                    binding.bivCustomerNumber.setSuccessState("")
                    showBillDetailsBottomSheet()
                }

                is PpobEvent.ServerError -> {
                    trackFetchBillEvent(it.message.orEmpty())
                    binding.bivCustomerNumber.setSuccessState("")
                    showPaymentDownBottomSheet(true, it.message)
                }

                is PpobEvent.InternetError -> {
                    trackFetchBillEvent(it.message.orEmpty())
                    binding.bivCustomerNumber.setSuccessState("")
                    showPaymentDownBottomSheet(false, it.message)
                }

                is PpobEvent.OtherError -> {
                    trackFetchBillEvent(it.message.orEmpty())
                    binding.bivCustomerNumber.setErrorMessage(it.message.orEmpty())
                }

                else -> {
                    binding.bivCustomerNumber.setSuccessState("")
                }
            }
        }
        viewModel.viewState.observe(viewLifecycleOwner) {
            if (it.numberLengthInvalid) {
                binding.bivCustomerNumber.setErrorMessage(
                    getString(R.string.phone_number_length_invalid)
                )
                binding.btnCek.isEnabled = false
            } else if (binding.bivCustomerNumber.getText().isNotBlank()) {
                binding.bivCustomerNumber.setSuccessState("")
                binding.btnCek.isEnabled = true
            }
            if (it.showLoading) {
                binding.btnCek.showProgress {
                    buttonTextRes = null
                    progressColor = Color.BLACK
                }
            } else {
                binding.btnCek.hideProgress(R.string.bt_cek)
            }
        }
    }

    private fun showBillDetailsBottomSheet() {
        val ppobBillDetailsBottomSheet =
            PpobBillDetailsBottomSheet.createInstance(addToCartResponse, category)
        ppobBillDetailsBottomSheet.show(childFragmentManager, PpobBillDetailsBottomSheet.TAG)
    }

    private fun trackFetchBillEvent(reason: String = "") {
        val map = hashMapOf<String, String>()
        map[PpobAnalytics.PARAM_PHONE_NUMBER] = binding.bivCustomerNumber.getText()
        map[PpobAnalytics.TYPE] = PpobAnalytics.PPOB_PULSA_POSTPAID
        map[PpobAnalytics.STATUS] =
            if (reason.isBlank()) PpobAnalytics.STATUS_SUCCESS else PpobAnalytics.STATUS_FAILED
        map[PpobAnalytics.REASON] = reason
        Analytics.trackEvent(PpobAnalytics.EVENT_PPOB_FETCH_BILL, map)
    }

    private fun showPaymentDownBottomSheet(isServiceDown: Boolean, message: String? = null) {
        val paymentDownBottomSheet = PaymentDownBottomSheet.createInstance(isServiceDown, message)
        paymentDownBottomSheet.show(childFragmentManager, PaymentDownBottomSheet.TAG)
    }

    override fun setData(profilesItem: ProfilesItem) {
        binding.bivCustomerNumber.setText(profilesItem.details?.accountNumber)
        binding.btnCek.callOnClick()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding?.bivCustomerNumber?.removeTextChanged()
        recentAndFavouriteFragment = null
        _binding = null
    }

    override fun refreshFavouritesTab() {
        recentAndFavouriteFragment?.refreshFavAndRecentTab()
    }
}
