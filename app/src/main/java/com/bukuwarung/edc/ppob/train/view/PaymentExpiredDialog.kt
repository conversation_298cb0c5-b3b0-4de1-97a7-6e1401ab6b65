package com.bukuwarung.edc.ppob.train.view

import android.content.Context
import android.os.Bundle
import com.bukuwarung.edc.databinding.PaymentExpiredDialogBinding
import com.bukuwarung.edc.global.base.BaseDialog
import com.bukuwarung.edc.global.enums.BaseDialogType
import com.bukuwarung.ui_component.utils.setSingleClickListener

class PaymentExpiredDialog(context: Context, private val action: () -> Unit) :
    BaseDialog(context, BaseDialogType.POPUP) {

    private val binding by lazy {
        PaymentExpiredDialogBinding.inflate(layoutInflater).also {
            setupViewBinding(it.root)
        }
    }

    init {
        this.setUseFullWidth(false)
        this.setCancellable(false)
    }

    override val resId: Int = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val minWidth = (context.resources.displayMetrics.widthPixels * 0.90).toInt()
        setMinWidth(minWidth)
        setupView()
    }

    private fun setupView() {
        binding.bbBack.setSingleClickListener {
            action()
            dismiss()
        }
    }
}
