package com.bukuwarung.edc.ppob.ewallet.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.LayoutPpobBillerItemBinding
import com.bukuwarung.edc.ppob.common.model.Biller
import com.bukuwarung.edc.util.getColorCompat
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick
import com.bumptech.glide.Glide

class EwalletBillersAdapter(
    private var list: List<Biller>,
    private val selectedBiller: Biller?,
    private val clickAction: (Biller) -> Unit
) : RecyclerView.Adapter<EwalletBillersAdapter.EwalletBillerViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): EwalletBillerViewHolder {
        val itemBinding = LayoutPpobBillerItemBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return EwalletBillerViewHolder(itemBinding, selectedBiller, clickAction)
    }

    override fun getItemCount(): Int = list.size

    override fun onBindViewHolder(holder: EwalletBillerViewHolder, position: Int) {
        holder.bind(list[position])
    }

    class EwalletBillerViewHolder(
        private val binding: LayoutPpobBillerItemBinding,
        private val selectedBiller: Biller?,
        private val clickAction: (Biller) -> Unit
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind(biller: Biller) {
            with(binding) {
                tvName.text = biller.name.orEmpty()
                Glide.with(ivBiller.context).load(biller.icon.orEmpty()).error(R.drawable.ic_bank)
                    .into(ivBiller)
                tvError.hideView()
                root.singleClick { clickAction(biller) }
                if (biller.code.equals(selectedBiller?.code)) {
                    tvName.setTextColor(binding.clItem.context.getColorCompat(R.color.colorPrimary))
                    clItem.setBackgroundColor(binding.clItem.context.getColorCompat(R.color.blue_5))
                    ivSelected.showView()
                } else {
                    tvName.setTextColor(binding.clItem.context.getColorCompat(R.color.black_80))
                    clItem.setBackgroundColor(binding.clItem.context.getColorCompat(R.color.white))
                    ivSelected.hideView()
                }
            }
        }
    }
}
