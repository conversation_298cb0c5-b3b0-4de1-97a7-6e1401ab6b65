package com.bukuwarung.edc.ppob.bpjs.view

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.DialogMonthListBinding
import com.bukuwarung.edc.global.base.BaseDialogFragment
import com.bukuwarung.edc.ppob.bpjs.adapter.MonthListAdapter
import com.bukuwarung.edc.util.DateTimeUtils
import com.bukuwarung.edc.util.hideView
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class MonthListDialog : BaseDialogFragment() {

    private var dialogMonthListBinding: DialogMonthListBinding? = null
    private val binding get() = dialogMonthListBinding!!
    private var iCommunicator: ICommunicator? = null

    companion object {
        const val TAG = "MonthListDialog"
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        parentFragment?.let { iCommunicator = it as? ICommunicator }
        if (context is ICommunicator) iCommunicator = context
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        dialogMonthListBinding = DialogMonthListBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        with(binding) {
            includeToolBar.ivHelp.hideView()
            includeToolBar.tvHelp.hideView()
            includeToolBar.tbPpob.navigationIcon =
                ContextCompat.getDrawable(requireContext(), R.drawable.ic_arrow_back)
            includeToolBar.tbPpob.setNavigationOnClickListener {
                dismiss()
            }
            includeToolBar.toolBarLabel.text = getString(R.string.select_payment_period)
            rvMonth.layoutManager = LinearLayoutManager(context)
            rvMonth.adapter =
                MonthListAdapter(DateTimeUtils.getMonthAndYearNameList(), ::clickAction)
        }
    }

    private fun clickAction(selectedMonth: String) {
        iCommunicator?.setSelectedMonth(selectedMonth)
        dismiss()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        dialogMonthListBinding = null
    }

    interface ICommunicator {
        fun setSelectedMonth(selectedMonth: String)
    }
}
