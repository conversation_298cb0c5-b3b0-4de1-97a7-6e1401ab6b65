package com.bukuwarung.edc.ppob.common.model

import android.os.Parcelable
import com.bukuwarung.payments.data.model.PpobDetailParam
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize
import java.io.Serializable

@Parcelize
data class PpobProductDetail(
    @SerializedName("sku")
    val sku: String,
    @SerializedName("name")
    val name: String,
    @SerializedName("amount")
    val amount: Double,
    @SerializedName("fee")
    val fee: Double,
    @SerializedName("admin_fee")
    val adminFee: Double,
    @SerializedName("discounted_fee")
    val discountedFee: Double,
    @SerializedName("details")
    val details: PpobDetailParam,
    @SerializedName("beneficiary")
    val beneficiary: FinproBeneficiary,
    @SerializedName("selling_price")
    val sellingPrice: Double,
    @SerializedName("disbursableType")
    val disbursableType: String? = null
) : Parcelable,
    Serializable
