package com.bukuwarung.edc.ppob.vehicletax.view

import android.graphics.Color
import android.os.Bundle
import android.text.InputType
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.FragmentVehicleTaxBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.payments.data.model.OrderResponse
import com.bukuwarung.edc.ppob.common.model.Biller
import com.bukuwarung.edc.ppob.common.model.FinproAddCartRequest
import com.bukuwarung.edc.ppob.common.model.FinproBeneficiary
import com.bukuwarung.edc.ppob.common.model.PpobEvent
import com.bukuwarung.edc.ppob.common.view.PaymentDownBottomSheet
import com.bukuwarung.edc.ppob.common.view.PpobBillDetailsBottomSheet
import com.bukuwarung.edc.ppob.common.view.PpobBillersDialog
import com.bukuwarung.edc.ppob.constants.PpobAnalytics
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.edc.ppob.recentsandfavourites.model.ProfilesItem
import com.bukuwarung.edc.ppob.recentsandfavourites.view.RecentAndFavouriteFragment
import com.bukuwarung.edc.ppob.vehicletax.viewmodel.VehicleTaxViewModel
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.getColorCompat
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.isFalseOrNull
import com.bukuwarung.edc.util.isNotNullOrBlank
import com.bukuwarung.edc.util.isNotNullOrEmpty
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.showView
import com.bukuwarung.payments.data.model.PpobProduct
import com.bukuwarung.ui_component.utils.setSingleClickListener
import com.github.razir.progressbutton.hideProgress
import com.github.razir.progressbutton.showProgress
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class VehicleTaxFragment :
    Fragment(),
    PpobBillersDialog.ICommunicator,
    RecentAndFavouriteFragment.IRecentAndFavCommunicator,
    PpobBillDetailsBottomSheet.PpobBillDetailsBsListener {

    private var _binding: FragmentVehicleTaxBinding? = null
    private val binding get() = _binding!!
    private var selectedProduct: PpobProduct? = null
    private var addToCartResponse: OrderResponse? = null
    private val category = PpobConst.CATEGORY_VEHICLE_TAX
    private var recentBiller = ""
    private var isLayoutType1: Boolean? = null
    private var recentAndFavouriteFragment: RecentAndFavouriteFragment? = null

    private val viewModel: VehicleTaxViewModel by viewModels()

    private val from by lazy { arguments?.getString(FROM).orEmpty() }
    private val accountNumber by lazy { arguments?.getString(ACCOUNT_NUMBER).orEmpty() }
    private val code by lazy { arguments?.getString(CODE).orEmpty() }
    private val phoneNumber by lazy { arguments?.getString(PHONE_NUMBER).orEmpty() }
    private val layoutType by lazy { arguments?.getString(LAYOUT_TYPE).orEmpty() }
    private val machineNumber by lazy { arguments?.getString(MACHINE_NUMBER).orEmpty() }
    private val frameNumber by lazy { arguments?.getString(FRAME_NUMBER).orEmpty() }

    companion object {
        private const val FROM = "FROM"
        private const val ACCOUNT_NUMBER = "ACCOUNT_NUMBER"
        const val LAYOUT_TYPE_1 = "TYPE_1" // for showing contact, frame, machine, policy numbers
        const val LAYOUT_TYPE_2 = "TYPE_2" // for showing payment code
        private const val CODE = "code"
        private const val PHONE_NUMBER = "phone_number"
        private const val LAYOUT_TYPE = "layout_type"
        private const val MACHINE_NUMBER = "machine_number"
        private const val FRAME_NUMBER = "frame_numer"
        fun createIntent(
            from: String,
            accountNumber: String,
            code: String,
            phoneNumber: String,
            layoutType: String,
            machineNumber: String,
            frameNumber: String
        ): VehicleTaxFragment {
            val bundle = Bundle().apply {
                putString(FROM, from)
                putString(ACCOUNT_NUMBER, accountNumber)
                putString(CODE, code)
                putString(PHONE_NUMBER, phoneNumber)
                putString(LAYOUT_TYPE, layoutType)
                putString(MACHINE_NUMBER, machineNumber)
                putString(FRAME_NUMBER, frameNumber)
            }
            return VehicleTaxFragment().apply {
                arguments = bundle
            }
        }
    }

    override fun onCreateView(
        layoutInflater: LayoutInflater,
        viewGroup: ViewGroup?,
        bundle: Bundle?
    ): View {
        _binding = FragmentVehicleTaxBinding.inflate(layoutInflater, viewGroup, false)
        setupView()
        subscribeState()
        return binding.root
    }

    override fun onResume() {
        super.onResume()
        recentAndFavouriteFragment = RecentAndFavouriteFragment.createIntent(category).also {
            childFragmentManager.beginTransaction().add(
                binding.flRecentAndFav.id,
                it
            ).commit()
        }
    }

    private fun setupView() {
        with(binding) {
            bivBiller.setDropDownDrawable { showVehicleTaxProductsDialog() }
            bivNumber.onTextChanged { bivNumber.setClearDrawable() }
            if (isLayoutType1.isFalseOrNull) bivNumber.setTitleHint(getString(R.string.optional))
            bivNumber.setBottomText(
                if (isLayoutType1.isTrue) {
                    getString(R.string.vehicle_tax_number_hint)
                } else {
                    getString(R.string.phone_number_for_sending_message)
                }
            )
            bivPolicyNumber.onTextChanged { bivPolicyNumber.setClearDrawable() }
            bivMachineNumber.onTextChanged { bivMachineNumber.setClearDrawable() }
            bivFrameNumber.onTextChanged { bivFrameNumber.setClearDrawable() }
            bivPaymentCode.onTextChanged { bivPaymentCode.setClearDrawable() }
            btnCek.setSingleClickListener {
                Utils.hideKeyboard(requireActivity())
                addToCart()
            }
            binding.bivPolicyNumber.setInputType(InputType.TYPE_CLASS_TEXT)
            binding.bivMachineNumber.setInputType(InputType.TYPE_CLASS_TEXT)
            binding.bivFrameNumber.setInputType(InputType.TYPE_CLASS_TEXT)
            if (code.isNotNullOrEmpty()) {
                showViewAndSetData(
                    code,
                    accountNumber,
                    layoutType,
                    phoneNumber,
                    machineNumber,
                    frameNumber
                )
            }
        }
    }

    private fun addToCart() {
        viewModel.addToCart(
            FinproAddCartRequest(
                sku = selectedProduct?.sku.orEmpty(),
                beneficiary = FinproBeneficiary(
                    category = category,
                    phoneNumber = binding.bivNumber.getText(),
                    accountNumber = if (isLayoutType1.isTrue) {
                        binding.bivPolicyNumber.getText()
                    } else {
                        binding.bivPaymentCode.getText()
                    },
                    code = recentBiller
                ),
                frameNumber = if (isLayoutType1.isTrue) {
                    binding.bivFrameNumber.getText()
                } else {
                    null
                },
                machineNumber = if (isLayoutType1.isTrue) {
                    binding.bivMachineNumber.getText()
                } else {
                    null
                },
                userType = PpobConst.USERTYPE_TREATMENT
            )
        )
    }

    private fun showVehicleTaxProductsDialog() {
        Utils.hideKeyboard(requireActivity())
        PpobBillersDialog.getInstance(selectedProduct, category).show(
            childFragmentManager,
            PpobBillersDialog.TAG
        )
    }

    private fun subscribeState() {
        viewModel.observeEvent.observe(viewLifecycleOwner) {
            when (it) {
                is PpobEvent.ToDetail -> {
                    addToCartResponse = it.orderDetail
                    trackFetchBillEvent()
                    showBillDetailsBottomSheet()
                }

                is PpobEvent.ServerError -> {
                    trackFetchBillEvent(it.message.orEmpty())
                    showPaymentDownBottomSheet(true, it.message)
                }

                is PpobEvent.InternetError -> {
                    trackFetchBillEvent(it.message.orEmpty())
                    showPaymentDownBottomSheet(false, it.message)
                }

                is PpobEvent.OtherError -> {
                    trackFetchBillEvent(it.message.orEmpty())
                    if (isLayoutType1.isTrue) {
                        binding.bivFrameNumber.setErrorMessage(it.message.orEmpty())
                    } else {
                        binding.bivPaymentCode.setErrorMessage(it.message.orEmpty())
                    }
                }

                else -> {
                    binding.bivFrameNumber.setSuccessState(getString(R.string.frame_number_hint))
                    binding.bivPaymentCode.setSuccessState(getString(R.string.pay_code_hint))
                }
            }
        }
        viewModel.viewState.observe(viewLifecycleOwner) {
            if (it.showLoading) {
                binding.btnCek.showProgress {
                    buttonTextRes = null
                    progressColor = Color.BLACK
                }
            } else {
                binding.btnCek.hideProgress(R.string.bt_cek)
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun setSelectedProduct(selectedProduct: PpobProduct, biller: Biller) {
        this.selectedProduct = selectedProduct
        setVehicleTaxInfoText(biller)
        with(binding) {
            bivBiller.setText(selectedProduct.name.orEmpty())
            btnCek.showView()
            recentBiller = selectedProduct.productInfo?.biller.orEmpty()
            if (biller.billerMetaData?.extraData?.layoutType.equals(LAYOUT_TYPE_1, true)) {
                isLayoutType1 = true
                grpForm1.showView()
                bivPaymentCode.hideView()
                bivNumber.setFocus()
            } else if (biller.billerMetaData?.extraData?.layoutType.equals(LAYOUT_TYPE_2, true)) {
                isLayoutType1 = false
                grpForm1.hideView()
                bivPaymentCode.showView()
                bivPaymentCode.setFocus()
            }
            bivNumber.showView()
            Utils.showKeyboard(requireActivity())
        }
    }

    private fun setVehicleTaxInfoText(biller: Biller?) {
        biller?.billerMetaData?.extraData?.let {
            with(binding) {
                if (it.infoBoxText?.isNotNullOrBlank().isTrue) {
                    tvInfo.apply {
                        showView()
                        text = Utils.makeSectionOfTextClickable(
                            it.infoBoxText.orEmpty(),
                            it.infoBoxCta.orEmpty(),
                            object : ClickableSpan() {
                                override fun onClick(widget: View) {
                                    Utils.hideKeyboard(requireActivity())
                                    VehicleTaxInfoBottomSheet.createInstance(it)
                                        .show(childFragmentManager, VehicleTaxInfoBottomSheet.TAG)
                                }

                                override fun updateDrawState(ds: TextPaint) {
                                    super.updateDrawState(ds)
                                    ds.isUnderlineText = false
                                    ds.color = context.getColorCompat(R.color.colorPrimary)
                                }
                            }
                        )
                        movementMethod = LinkMovementMethod.getInstance()
                    }
                }
            }
        }
    }

    private fun showPaymentDownBottomSheet(isServiceDown: Boolean, message: String? = null) {
        val paymentDownBottomSheet = PaymentDownBottomSheet.createInstance(isServiceDown, message)
        paymentDownBottomSheet.show(childFragmentManager, PaymentDownBottomSheet.TAG)
    }

    private fun trackFetchBillEvent(errorMessage: String = "") {
        val item = addToCartResponse?.items?.getOrNull(0)?.details
        val id = binding.bivNumber.getText()
        val map = hashMapOf<String, String>()
        map[PpobAnalytics.ID] = id
        map[PpobAnalytics.PROVIDER] = selectedProduct?.productInfo?.billerName.orEmpty()
        map[PpobAnalytics.MACHINE_NUMBER] = item?.machineNumber.orEmpty()
        map[PpobAnalytics.VIN_NUMBER] = item?.frameNumber.orEmpty()
        map[PpobAnalytics.POLICY_NUMBER] = item?.policyNumber.orEmpty()
        map[PpobAnalytics.SAMSAT_DOMICILE] = item?.address.orEmpty()
        map[PpobAnalytics.TYPE] = PpobAnalytics.PPOB_VEHICLE_TAX
        map[PpobAnalytics.STATUS] = if (errorMessage.isBlank()) {
            PpobAnalytics.STATUS_SUCCESS
        } else {
            PpobAnalytics.STATUS_FAILED
        }
        map[PpobAnalytics.REASON] = errorMessage
        Analytics.trackEvent(PpobAnalytics.EVENT_PPOB_FETCH_BILL, map)
    }

    private fun showBillDetailsBottomSheet() {
        val ppobBillDetailsBottomSheet =
            PpobBillDetailsBottomSheet.createInstance(addToCartResponse, category)
        ppobBillDetailsBottomSheet.show(childFragmentManager, PpobBillDetailsBottomSheet.TAG)
    }

    override fun setData(profilesItem: ProfilesItem) {
        setVehicleTaxInfoText(profilesItem.biller)
        showViewAndSetData(
            profilesItem.biller?.code.orEmpty(),
            profilesItem.details?.accountNumber.orEmpty(),
            if (PpobConst.VEHICLE_TAX_TYPE1_BILLERS.contains(
                    profilesItem.biller?.code.orEmpty()
                )
            ) {
                LAYOUT_TYPE_1
            } else {
                LAYOUT_TYPE_2
            },
            profilesItem.details?.phoneNumber.orEmpty(),
            profilesItem.details?.machineNumber.orEmpty(),
            profilesItem.details?.nik.orEmpty()
        )
    }

    override fun refreshFavouritesTab() {
        recentAndFavouriteFragment?.refreshFavAndRecentTab()
    }

    private fun showViewAndSetData(
        code: String,
        accountNumber: String,
        layoutType: String,
        phoneNumber: String,
        machineNumber: String,
        frameNumber: String
    ) {
        recentBiller = code
        with(binding) {
            bivBiller.setText(code)
            if (layoutType == LAYOUT_TYPE_1) {
                isLayoutType1 = true
                bivFrameNumber.setText(frameNumber)
                bivMachineNumber.setText(machineNumber)
                bivPolicyNumber.setText(accountNumber)
                bivNumber.setText(phoneNumber)
                grpForm1.showView()
                bivPaymentCode.hideView()
            } else {
                isLayoutType1 = false
                grpForm1.hideView()
                bivPaymentCode.showView()
                bivPaymentCode.setText(accountNumber)
            }
            bivNumber.showView()
            btnCek.showView()
            btnCek.callOnClick()
        }
    }
}
