package com.bukuwarung.edc.ppob.listrik.postpaid.view

import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.FragmentPostpaidListrikBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.payments.data.model.OrderResponse
import com.bukuwarung.edc.ppob.common.model.FinproAddCartRequest
import com.bukuwarung.edc.ppob.common.model.FinproBeneficiary
import com.bukuwarung.edc.ppob.common.model.PpobEvent
import com.bukuwarung.edc.ppob.common.view.PaymentDownBottomSheet
import com.bukuwarung.edc.ppob.common.view.PpobBillDetailsBottomSheet
import com.bukuwarung.edc.ppob.constants.PpobAnalytics
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.edc.ppob.listrik.postpaid.viewmodel.PostpaidListrikViewModel
import com.bukuwarung.edc.ppob.recentsandfavourites.model.ProfilesItem
import com.bukuwarung.edc.ppob.recentsandfavourites.view.RecentAndFavouriteFragment
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.isNotNullOrBlank
import com.bukuwarung.edc.util.isNotNullOrEmpty
import com.bukuwarung.ui_component.utils.setSingleClickListener
import com.github.razir.progressbutton.hideProgress
import com.github.razir.progressbutton.showProgress
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class PostpaidListrikFragment :
    Fragment(),
    RecentAndFavouriteFragment.IRecentAndFavCommunicator,
    PpobBillDetailsBottomSheet.PpobBillDetailsBsListener {

    private var _binding: FragmentPostpaidListrikBinding? = null
    private val binding get() = _binding!!
    private val category = PpobConst.CATEGORY_PLN_POSTPAID
    private var addToCartResponse: OrderResponse? = null
    private var recentAndFavouriteFragment: RecentAndFavouriteFragment? = null

    private val viewModel: PostpaidListrikViewModel by viewModels()

    private val from by lazy { arguments?.getString(FROM) }
    private val accountNumber by lazy { arguments?.getString(ACCOUNT_NUMBER) }
    private val phoneNumber by lazy { arguments?.getString(PHONE_NUMBER) }

    companion object {
        private const val FROM = "FROM"
        private const val ACCOUNT_NUMBER = "ACCOUNT_NUMBER"
        private const val PHONE_NUMBER = "PHONE_NUMBER"
        fun createIntent(
            from: String,
            accountNumber: String,
            phoneNumber: String
        ): PostpaidListrikFragment {
            val bundle = Bundle().apply {
                putString(FROM, from)
                putString(ACCOUNT_NUMBER, accountNumber)
                putString(PHONE_NUMBER, phoneNumber)
            }
            return PostpaidListrikFragment().apply {
                arguments = bundle
            }
        }
    }

    override fun onCreateView(
        layoutInflater: LayoutInflater,
        viewGroup: ViewGroup?,
        bundle: Bundle?
    ): View {
        _binding = FragmentPostpaidListrikBinding.inflate(layoutInflater, viewGroup, false)
        setupView()
        subscribeState()
        return binding.root
    }

    override fun onResume() {
        super.onResume()
        addRecentsAndFavourites()
    }

    private fun setupView() {
        with(binding) {
            bivCustomerNumber.setFocus()
            bivNumber.onTextChanged { bivNumber.setClearDrawable() }
            Utils.showKeyboard(requireActivity())
            bivCustomerNumber.onTextChanged {
                bivCustomerNumber.setClearDrawable()
                viewModel.checkCustomerNumberValidation(
                    bivCustomerNumber.getText(),
                    requireContext()
                )
            }
            if (phoneNumber.isNotNullOrBlank()) bivNumber.setText(phoneNumber)
            if (accountNumber.isNotNullOrEmpty()) {
                bivCustomerNumber.setText(accountNumber)
                btnCek.callOnClick()
            }

            btnCek.setSingleClickListener {
                Utils.hideKeyboard(requireActivity())
                addToCart()
            }
            btnCek.isEnabled = false
        }
    }

    private fun addToCart() {
        viewModel.addToCart(
            FinproAddCartRequest(
                sku = "",
                beneficiary = FinproBeneficiary(
                    category = PpobConst.CATEGORY_LISTRIK,
                    accountNumber = binding.bivCustomerNumber.getText(),
                    code = PpobConst.CATEGORY_PLN_POSTPAID,
                    phoneNumber = binding.bivNumber.getText()
                ),
                userType = PpobConst.USERTYPE_TREATMENT
            )
        )
    }

    private fun addRecentsAndFavourites() {
        recentAndFavouriteFragment = RecentAndFavouriteFragment.createIntent(category).also {
            childFragmentManager.beginTransaction().add(
                binding.flRecentAndFav.id,
                it
            ).commit()
        }
    }

    private fun subscribeState() {
        viewModel.observeEvent.observe(viewLifecycleOwner) {
            when (it) {
                is PpobEvent.ToDetail -> {
                    addToCartResponse = it.orderDetail
                    trackFetchBillEvent()
                    binding.bivCustomerNumber.setSuccessState("")
                    showBillDetailsBottomSheet()
                }

                is PpobEvent.ServerError -> {
                    trackFetchBillEvent(it.message.orEmpty())
                    binding.bivCustomerNumber.setSuccessState("")
                    showPaymentDownBottomSheet(true, it.message)
                }

                is PpobEvent.InternetError -> {
                    trackFetchBillEvent(it.message.orEmpty())
                    showPaymentDownBottomSheet(false, it.message)
                    binding.bivCustomerNumber.setSuccessState("")
                }

                is PpobEvent.OtherError -> {
                    trackFetchBillEvent(it.message.orEmpty())
                    binding.bivCustomerNumber.setErrorMessage(it.message.orEmpty())
                }

                else -> {
                    binding.bivCustomerNumber.setSuccessState("")
                }
            }
        }
        viewModel.viewState.observe(viewLifecycleOwner) {
            if (it.numberLengthInvalid) {
                binding.bivCustomerNumber.setErrorMessage(
                    getString(R.string.phone_number_length_invalid)
                )
                binding.btnCek.isEnabled = false
            } else if (binding.bivCustomerNumber.getText().isNotBlank()) {
                binding.bivCustomerNumber.setSuccessState("")
                binding.btnCek.isEnabled = true
            }
            if (it.showLoading) {
                binding.btnCek.showProgress {
                    buttonTextRes = null
                    progressColor = Color.BLACK
                }
            } else {
                binding.btnCek.hideProgress(R.string.bt_cek)
            }
        }
    }

    private fun showBillDetailsBottomSheet() {
        val ppobBillDetailsBottomSheet =
            PpobBillDetailsBottomSheet.createInstance(addToCartResponse, category)
        ppobBillDetailsBottomSheet.show(childFragmentManager, PpobBillDetailsBottomSheet.TAG)
    }

    private fun trackFetchBillEvent(errorMessage: String = "") {
        val map = hashMapOf<String, String>()
        map[PpobAnalytics.PARAM_PLN] = binding.bivCustomerNumber.getText()
        map[PpobAnalytics.PHONE_FILLED] = binding.bivNumber.getText()
        map[PpobAnalytics.TYPE] = PpobAnalytics.LISTRIK_POSTPAID
        map[PpobAnalytics.STATUS] = if (errorMessage.isBlank()) {
            PpobAnalytics.STATUS_SUCCESS
        } else {
            PpobAnalytics.STATUS_FAILED
        }
        map[PpobAnalytics.REASON] = errorMessage
        Analytics.trackEvent(PpobAnalytics.EVENT_PPOB_FETCH_BILL, map)
    }

    private fun showPaymentDownBottomSheet(isServiceDown: Boolean, message: String? = null) {
        val paymentDownBottomSheet = PaymentDownBottomSheet.createInstance(isServiceDown, message)
        paymentDownBottomSheet.show(childFragmentManager, PaymentDownBottomSheet.TAG)
    }

    override fun setData(profilesItem: ProfilesItem) {
        binding.bivCustomerNumber.setText(profilesItem.details?.accountNumber)
        binding.btnCek.callOnClick()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding?.bivCustomerNumber?.removeTextChanged()
        _binding = null
    }

    override fun refreshFavouritesTab() {
        recentAndFavouriteFragment?.refreshFavAndRecentTab()
    }
}
