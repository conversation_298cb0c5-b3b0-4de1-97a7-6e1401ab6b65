package com.bukuwarung.edc.ppob.train.dialog

import android.content.Context
import android.os.Bundle
import com.bukuwarung.edc.R

class TrainWebviewExitDialog(
    context: Context,
    onPromptClicked: ((Boolean) -> Unit),
    onCrossClicked: () -> Unit
) : BasePromptDialog(context, onPromptClicked, true, onCrossClicked) {

    init {
        this.setUseFullWidth(false)
        this.setCancellable(false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setTitle(context.getString(R.string.exit_confirmation_message))
        setContent(context.getString(R.string.data_lost_warning_message))
        setNegativeText(context.getString(R.string.sign_out))
        setPositiveText(context.getString(R.string.batal))
        showCrossBtn()
    }
}
