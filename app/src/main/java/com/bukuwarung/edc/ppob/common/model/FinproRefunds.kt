package com.bukuwarung.payments.data.model

import android.os.Parcelable
import com.bukuwarung.edc.ppob.common.model.RefundBankAccount
import com.google.gson.annotations.SerializedName
import java.io.Serializable
import kotlinx.parcelize.Parcelize

@Parcelize
data class FinproRefunds(
    @SerializedName("payment_channel")
    val paymentChannel: String?,
    @SerializedName("payment_method")
    val refundPaymentMethod: List<RefundBankAccount>? = null
) : Parcelable,
    Serializable
