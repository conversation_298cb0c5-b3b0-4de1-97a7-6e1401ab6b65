package com.bukuwarung.edc.ppob.bpjs.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.edc.databinding.LayoutMonthItemBinding
import com.bukuwarung.edc.util.singleClick

class MonthListAdapter(private val list: List<String>, private val clickAction: (String) -> Unit) :
    RecyclerView.Adapter<MonthListAdapter.MonthViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MonthViewHolder {
        val itemBinding =
            LayoutMonthItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return MonthViewHolder(itemBinding, clickAction)
    }

    override fun onBindViewHolder(holder: MonthViewHolder, position: Int) {
        holder.bind(list[position])
    }

    override fun getItemCount(): Int = list.size

    class MonthViewHolder(
        private val binding: LayoutMonthItemBinding,
        private val clickAction: (String) -> Unit
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind(month: String) {
            binding.tvMonth.text = month
            binding.tvMonth.singleClick {
                clickAction(month)
            }
        }
    }
}
