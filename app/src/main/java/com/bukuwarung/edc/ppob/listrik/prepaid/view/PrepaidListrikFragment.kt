package com.bukuwarung.edc.ppob.listrik.prepaid.view

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.FragmentPrepaidListrikBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.payments.data.model.OrderResponse
import com.bukuwarung.edc.ppob.common.adapter.PpobProductAdapter
import com.bukuwarung.edc.ppob.common.model.FinproAddCartRequest
import com.bukuwarung.edc.ppob.common.model.FinproBeneficiary
import com.bukuwarung.edc.ppob.common.model.PpobEvent
import com.bukuwarung.edc.ppob.common.view.PaymentDownBottomSheet
import com.bukuwarung.edc.ppob.common.view.PpobBillDetailsBottomSheet
import com.bukuwarung.edc.ppob.constants.PpobAnalytics
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.edc.ppob.listrik.prepaid.viewmodel.PrepaidListrikViewModel
import com.bukuwarung.edc.ppob.recentsandfavourites.model.ProfilesItem
import com.bukuwarung.edc.ppob.recentsandfavourites.view.RecentAndFavouriteFragment
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.isNotNullOrEmpty
import com.bukuwarung.edc.util.showView
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class PrepaidListrikFragment :
    Fragment(),
    RecentAndFavouriteFragment.IRecentAndFavCommunicator {
    private var fragmentListrikPrepaidBinding: FragmentPrepaidListrikBinding? = null
    private val binding get() = fragmentListrikPrepaidBinding!!

    private val viewModel: PrepaidListrikViewModel by viewModels()
    private lateinit var adapter: PpobProductAdapter
    private var category = PpobConst.CATEGORY_LISTRIK

    companion object {
        private const val ARG_CUSTOMER_ID = "customer_id"
        private const val ARG_MOBILE_NUMBER = "mobile_number"
        private const val FROM = "from"
        fun createIntent(
            from: String,
            customerId: String = "",
            mobileNumber: String = ""
        ): PrepaidListrikFragment {
            val bundle = Bundle().apply {
                putString(ARG_CUSTOMER_ID, customerId)
                putString(ARG_MOBILE_NUMBER, mobileNumber)
                putString(FROM, from)
            }
            return PrepaidListrikFragment().apply {
                arguments = bundle
            }
        }
    }

    override fun onCreateView(
        layoutInflater: LayoutInflater,
        viewGroup: ViewGroup?,
        bundle: Bundle?
    ): View {
        fragmentListrikPrepaidBinding =
            FragmentPrepaidListrikBinding.inflate(layoutInflater, viewGroup, false)
        setupView()
        subscribeState()
        return binding.root
    }

    override fun onResume() {
        super.onResume()
        addRecentAndFavourites()
    }

    private fun setupView() {
        with(binding) {
            adapter = PpobProductAdapter({
                Utils.hideKeyboard(requireActivity())
                it.sku?.let { sku ->
                    viewModel.addToCart(
                        FinproAddCartRequest(
                            sku = sku,
                            beneficiary = FinproBeneficiary(
                                category = category,
                                accountNumber = binding.bivCustomerId.getText(),
                                phoneNumber = bivCustomerNumber.getText(),
                                code = it.productInfo?.biller.orEmpty()
                            ),
                            userType = PpobConst.USERTYPE_TREATMENT
                        )
                    )
                }
            }, category)
            rvProducts.layoutManager = LinearLayoutManager(requireContext())
            rvProducts.adapter = adapter
            bivCustomerId.requestFocus()
            //noinspection AndroidLintClickableViewAccessibility
            rvProducts.setOnTouchListener { _, _ ->
                Utils.hideKeyboard(requireActivity())
                false
            }
            bivCustomerNumber.setSelection()
            bivCustomerNumber.onTextChanged {
                bivCustomerNumber.setClearDrawable()
                viewModel.checkCustomerNumberValidation(it)
            }
            val customerId = arguments?.getString(ARG_CUSTOMER_ID)
            if (customerId.isNotNullOrEmpty()) {
                bivCustomerId.setText(customerId)
            }
            val mobileNumber = arguments?.getString(ARG_MOBILE_NUMBER)
            if (mobileNumber.isNotNullOrEmpty()) {
                bivCustomerNumber.setText(mobileNumber)
            }
        }
    }

    private fun addRecentAndFavourites() {
        childFragmentManager.beginTransaction().add(
            binding.flRecentAndFav.id,
            RecentAndFavouriteFragment.createIntent(category)
        ).commit()
    }

    private fun subscribeState() {
        viewModel.observeEvent.observe(viewLifecycleOwner) {
            with(binding) {
                layoutEmptyPlaceholder.root.hideView()
                when (it) {
                    is PpobEvent.ShowProductsList -> {
                        bivCustomerNumber.setSuccessState("")
                        flRecentAndFav.hideView()
                        tvListTitle.showView()
                        if (it.list.isEmpty()) {
                            rvProducts.hideView()
                            layoutEmptyPlaceholder.root.showView()
                        } else {
                            rvProducts.showView()
                            adapter.setData(it.list)
                        }
                    }

                    is PpobEvent.ServerError -> {
                        bivCustomerNumber.setSuccessState("")
                        showPaymentDownBottomSheet(true, it.message)
                    }

                    is PpobEvent.OtherError -> {
                        bivCustomerNumber.setErrorMessage(it.message.orEmpty())
                        showPaymentDownBottomSheet(true, it.message)
                    }

                    is PpobEvent.InternetError -> {
                        bivCustomerNumber.setSuccessState("")
                        showPaymentDownBottomSheet(false, it.message)
                    }

                    is PpobEvent.ToDetail -> {
                        bivCustomerNumber.setSuccessState("")
                        trackBillFetchEvent(it.orderDetail)
                        PpobBillDetailsBottomSheet.createInstance(it.orderDetail, category)
                            .show(parentFragmentManager, PpobBillDetailsBottomSheet.TAG)
                    }

                    else -> {
                        bivCustomerNumber.setSuccessState("")
                    }
                }
            }
        }
        viewModel.viewState.observe(viewLifecycleOwner) {
            with(binding) {
                progressBar.visibility = it.showLoading.asVisibility()
                if (it.showShimmer) {
                    includeShimmer.sflLayout.showShimmer(true)
                    includeShimmer.root.showView()
                    rvProducts.hideView()
                    layoutEmptyPlaceholder.root.hideView()
                } else {
                    includeShimmer.sflLayout.hideShimmer()
                    includeShimmer.root.hideView()
                }
                if (it.numberLengthInvalid) {
                    bivCustomerNumber.setErrorMessage(
                        getString(R.string.phone_number_length_invalid)
                    )
                    adapter.enableButtonToggle(false)
                } else {
                    bivCustomerNumber.setSuccessState(getString(R.string.listrik_mobile_number))
                    adapter.enableButtonToggle(true)
                }

                if (it.customerIdLengthInvalid) {
                    bivCustomerId.setErrorMessage(
                        getString(R.string.listrik_customer_id_limit_message)
                    )
                    adapter.enableButtonToggle(false)
                } else {
                    bivCustomerId.setSuccessState("")
                    adapter.enableButtonToggle(true)
                }
            }
        }
    }

    private fun showPaymentDownBottomSheet(isServiceDown: Boolean, message: String? = null) {
        val paymentDownBottomSheet = PaymentDownBottomSheet.createInstance(isServiceDown, message)
        paymentDownBottomSheet.show(childFragmentManager, PaymentDownBottomSheet.TAG)
    }

    private fun trackBillFetchEvent(orderDetail: OrderResponse?) {
        val map = hashMapOf<String, String>()
        map[PpobAnalytics.NOMINAL] = orderDetail?.amount.toString()
        map["ppob_type"] = PpobAnalytics.PPOB_LISTRIK
        Analytics.trackEvent(PpobAnalytics.EVENT_PPOB_SELECT_PACK, map)
    }

    override fun setData(profilesItem: ProfilesItem) {
        with(binding) {
            bivCustomerId.setText(profilesItem.details?.accountNumber)
            bivCustomerNumber.setText(profilesItem.details?.phoneNumber)
            bivCustomerNumber.setSelection()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        fragmentListrikPrepaidBinding?.bivCustomerNumber?.removeTextChanged()
        fragmentListrikPrepaidBinding?.bivCustomerId?.removeTextChanged()
        fragmentListrikPrepaidBinding?.rvProducts?.adapter = null
        fragmentListrikPrepaidBinding = null
    }
}
