package com.bukuwarung.edc.ppob.pulsa.prepaid.view

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.FragmentPrepaidPulsaBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.payments.data.model.OrderResponse
import com.bukuwarung.edc.ppob.common.adapter.PpobProductAdapter
import com.bukuwarung.edc.ppob.common.model.FinproAddCartRequest
import com.bukuwarung.edc.ppob.common.model.FinproBeneficiary
import com.bukuwarung.edc.ppob.common.model.PpobEvent
import com.bukuwarung.edc.ppob.common.view.PaymentDownBottomSheet
import com.bukuwarung.edc.ppob.confirmation.view.PpobOrderFormActivity
import com.bukuwarung.edc.ppob.constants.PpobAnalytics
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.edc.ppob.pulsa.prepaid.viewmodel.PrepaidPulsaViewModel
import com.bukuwarung.edc.ppob.recentsandfavourites.model.ProfilesItem
import com.bukuwarung.edc.ppob.recentsandfavourites.view.RecentAndFavouriteFragment
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.isNotNullOrEmpty
import com.bukuwarung.edc.util.setDrawable
import com.bukuwarung.edc.util.showView
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class PrepaidPulsaFragment :
    Fragment(),
    RecentAndFavouriteFragment.IRecentAndFavCommunicator {
    private var fragmentPrepaidPulsaBinding: FragmentPrepaidPulsaBinding? = null
    private val binding get() = fragmentPrepaidPulsaBinding!!
    private val viewModel: PrepaidPulsaViewModel by viewModels()
    private lateinit var adapter: PpobProductAdapter
    private var category = PpobConst.CATEGORY_PULSA

    companion object {
        private const val ARG_CUSTOMER_ID = "customer_id"
        private const val FROM = "from"
        fun createIntent(from: String, customerId: String? = ""): PrepaidPulsaFragment {
            val bundle = Bundle().apply {
                putString(ARG_CUSTOMER_ID, customerId)
                putString(FROM, from)
            }
            return PrepaidPulsaFragment().apply {
                arguments = bundle
            }
        }
    }

    override fun onCreateView(
        layoutInflater: LayoutInflater,
        viewGroup: ViewGroup?,
        bundle: Bundle?
    ): View {
        fragmentPrepaidPulsaBinding =
            FragmentPrepaidPulsaBinding.inflate(layoutInflater, viewGroup, false)
        setupView()
        subscribeState()
        return binding.root
    }

    override fun onResume() {
        super.onResume()
        addRecentsAndFavourites()
    }

    private fun setupView() {
        with(binding) {
            adapter = PpobProductAdapter({
                Utils.hideKeyboard(requireActivity())
                it.sku?.let { sku ->
                    viewModel.addToCart(
                        FinproAddCartRequest(
                            sku = sku,
                            beneficiary = FinproBeneficiary(
                                category = category,
                                number = bivCustomerNumber.getText(),
                                code = it.productInfo?.biller.orEmpty()
                            ),
                            userType = PpobConst.USERTYPE_TREATMENT
                        )
                    )
                }
            }, category)
            bivCustomerNumber.requestFocus()
            rvProducts.layoutManager = LinearLayoutManager(requireContext())
            rvProducts.adapter = adapter
            //noinspection AndroidLintClickableViewAccessibility
            rvProducts.setOnTouchListener { _, _ ->
                Utils.hideKeyboard(requireActivity())
                false
            }
            bivCustomerNumber.onTextChanged {
                bivCustomerNumber.setClearDrawable()
                viewModel.checkCustomerNumberValidation(it)
            }
            tvListText.text = getString(R.string.choose_ppob, getString(R.string.pulsa))
            val customerId = arguments?.getString(ARG_CUSTOMER_ID)
            if (customerId.isNotNullOrEmpty()) {
                bivCustomerNumber.setText(customerId)
            }
        }
    }

    private fun addRecentsAndFavourites() {
        childFragmentManager.beginTransaction().add(
            binding.flRecentAndFav.id,
            RecentAndFavouriteFragment.createIntent(category)
        ).commit()
    }

    private fun subscribeState() {
        viewModel.observeEvent.observe(viewLifecycleOwner) {
            with(binding) {
                layoutEmptyPlaceholder.root.hideView()
                when (it) {
                    is PpobEvent.ShowProductsList -> {
                        bivCustomerNumber.setSuccessState("")
                        flRecentAndFav.hideView()
                        rvProducts.showView()
                        tvListText.showView()
                        if (it.list.isEmpty()) {
                            rvProducts.hideView()
                            layoutEmptyPlaceholder.root.showView()
                        } else {
                            adapter.setData(it.list)
                            rvProducts.showView()
                        }
                    }

                    is PpobEvent.ServerError -> {
                        bivCustomerNumber.setSuccessState("")
                        showPaymentDownBottomSheet(true, it.message)
                    }

                    is PpobEvent.InternetError -> {
                        bivCustomerNumber.setSuccessState("")
                        showPaymentDownBottomSheet(false, it.message)
                    }

                    is PpobEvent.OtherError -> {
                        bivCustomerNumber.setErrorMessage(it.message.orEmpty())
                    }

                    is PpobEvent.ToDetail -> {
                        bivCustomerNumber.setSuccessState("")
                        trackBillFetchEvent(it.orderDetail)
                        startActivity(
                            PpobOrderFormActivity.createIntent(
                                context = requireActivity(),
                                orderDetail = it.orderDetail,
                                type = category
                            )
                        )
                    }

                    else -> {
                        bivCustomerNumber.setSuccessState("")
                    }
                }
            }
        }
        viewModel.viewState.observe(viewLifecycleOwner) {
            with(binding) {
                if (it.showShimmer) {
                    includeShimmer.sflLayout.showShimmer(true)
                    includeShimmer.root.showView()
                    rvProducts.hideView()
                    layoutEmptyPlaceholder.root.hideView()
                } else {
                    includeShimmer.sflLayout.hideShimmer()
                    includeShimmer.root.hideView()
                }
                progressBar.visibility = it.showLoading.asVisibility()
                when {
                    it.numberInvalid -> {
                        bivCustomerNumber.setErrorMessage(getString(R.string.phone_number_invalid))
                        adapter.enableButtonToggle(false)
                    }

                    it.numberLengthInvalid -> {
                        bivCustomerNumber.setErrorMessage(
                            getString(R.string.phone_number_length_invalid)
                        )
                        adapter.enableButtonToggle(false)
                    }

                    else -> {
                        bivCustomerNumber.setSuccessState("")
                        adapter.enableButtonToggle(true)
                    }
                }

                if (it.providerLogo == 0) {
                    tvListText.hideView()
                    rvProducts.hideView()
                    flRecentAndFav.showView()
                    layoutEmptyPlaceholder.root.hideView()
                } else {
                    tvListText.showView()
                    tvListText.setDrawable(right = it.providerLogo)
                }
            }
        }
    }

    private fun trackBillFetchEvent(orderDetail: OrderResponse?) {
        val map = HashMap<String, String>()
        map[PpobAnalytics.NOMINAL] = orderDetail?.amount.toString()
        map[PpobAnalytics.TYPE] = PpobAnalytics.PPOB_PULSA
        Analytics.trackEvent(PpobAnalytics.EVENT_PPOB_SELECT_PACK, map)
    }

    private fun showPaymentDownBottomSheet(isServiceDown: Boolean, message: String? = null) {
        val paymentDownBottomSheet = PaymentDownBottomSheet.createInstance(isServiceDown, message)
        paymentDownBottomSheet.show(childFragmentManager, PaymentDownBottomSheet.TAG)
    }

    override fun setData(profilesItem: ProfilesItem) {
        binding.bivCustomerNumber.setText(profilesItem.details?.accountNumber)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        fragmentPrepaidPulsaBinding?.bivCustomerNumber?.removeTextChanged()
        fragmentPrepaidPulsaBinding?.rvProducts?.adapter = null
        fragmentPrepaidPulsaBinding = null
    }
}
