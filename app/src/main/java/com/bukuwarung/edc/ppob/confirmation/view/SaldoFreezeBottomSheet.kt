package com.bukuwarung.edc.ppob.confirmation.view

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.edc.ZohoChat
import com.bukuwarung.edc.databinding.SaldoFreezeBottomSheetBinding
import com.bukuwarung.edc.global.base.BaseBottomSheetDialogFragment
import com.bukuwarung.edc.util.singleClick

class SaldoFreezeBottomSheet : BaseBottomSheetDialogFragment() {
    private var _binding: SaldoFreezeBottomSheetBinding? = null
    private val binding get() = _binding!!

    companion object {
        const val TAG = "SaldoFreezeBottomSheet"
        fun createInstance() = SaldoFreezeBottomSheet()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = SaldoFreezeBottomSheetBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.tvHelp.singleClick {
            ZohoChat.openZohoChat("saldo_freeze_bottom_sheet")
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
