package com.bukuwarung.edc.ppob.pdam.view

import android.graphics.Color
import android.os.Bundle
import android.text.InputType
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.FragmentPdamBinding
import com.bukuwarung.edc.payments.data.model.OrderResponse
import com.bukuwarung.edc.payments.ui.history.bottomsheet.ErrorBottomSheet
import com.bukuwarung.edc.ppob.common.model.FinproAddCartRequest
import com.bukuwarung.edc.ppob.common.model.FinproBeneficiary
import com.bukuwarung.edc.ppob.common.model.PpobEvent
import com.bukuwarung.edc.ppob.common.view.PpobBillDetailsBottomSheet
import com.bukuwarung.edc.ppob.constants.PpobAnalytics
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.edc.ppob.pdam.viewmodel.PdamViewModel
import com.bukuwarung.edc.ppob.recentsandfavourites.model.ProfilesItem
import com.bukuwarung.edc.ppob.recentsandfavourites.view.RecentAndFavouriteFragment
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.isNotNullOrBlank
import com.bukuwarung.payments.data.model.PpobProduct
import com.bukuwarung.ui_component.utils.isNotNullOrEmpty
import com.bukuwarung.ui_component.utils.setSingleClickListener
import com.bukuwarung.ui_component.utils.showView
import com.github.razir.progressbutton.hideProgress
import com.github.razir.progressbutton.showProgress
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class PdamFragment :
    Fragment(),
    WaterBillAreaDialog.ICommunicator,
    RecentAndFavouriteFragment.IRecentAndFavCommunicator,
    PpobBillDetailsBottomSheet.PpobBillDetailsBsListener {

    private var _binding: FragmentPdamBinding? = null
    private val binding get() = _binding!!
    private var addToCartResponse: OrderResponse? = null
    private val category = PpobConst.CATEGORY_PDAM
    private var selectedProduct: PpobProduct? = null
    private var recentBiller = ""

    private val viewModel: PdamViewModel by viewModels()

    private val from by lazy { arguments?.getString(FROM).orEmpty() }
    private val accountNumber by lazy { arguments?.getString(ACCOUNT_NUMBER).orEmpty() }
    private val phoneNumber by lazy { arguments?.getString(PHONE_NUMBER).orEmpty() }
    private val billerCode by lazy { arguments?.getString(BILLER_CODE).orEmpty() }
    private var recentAndFavouriteFragment: RecentAndFavouriteFragment? = null

    companion object {
        private const val FROM = "FROM"
        private const val ACCOUNT_NUMBER = "ACCOUNT_NUMBER"
        private const val PHONE_NUMBER = "PHONE_NUMBER"
        private const val BILLER_CODE = "BILLER_CODE"
        private const val NUMERIC = "NUMERIC"
        private const val ALPHA_NUMERIC = "ALPHANUMERIC"
        fun createIntent(
            from: String,
            accountNumber: String,
            code: String,
            phoneNumber: String
        ): PdamFragment {
            val bundle = Bundle().apply {
                putString(FROM, from)
                putString(ACCOUNT_NUMBER, accountNumber)
                putString(BILLER_CODE, code)
                putString(PHONE_NUMBER, phoneNumber)
            }
            return PdamFragment().apply {
                arguments = bundle
            }
        }
    }

    override fun onCreateView(
        layoutInflater: LayoutInflater,
        viewGroup: ViewGroup?,
        bundle: Bundle?
    ): View {
        _binding = FragmentPdamBinding.inflate(layoutInflater, viewGroup, false)
        setupView()
        subscribeState()
        return binding.root
    }

    override fun onResume() {
        super.onResume()
        recentAndFavouriteFragment = RecentAndFavouriteFragment.createIntent(category).also {
            childFragmentManager.beginTransaction().add(
                binding.flRecentAndFav.id,
                it
            ).commit()
        }
    }

    private fun setupView() {
        with(binding) {
            tvChooseArea.setSingleClickListener {
                showDialog()
            }
            bivCustomerNumber.onTextChanged {
                bivCustomerNumber.setClearDrawable()
                viewModel.checkCustomerNumberValidation(
                    bivCustomerNumber.getText(),
                    requireContext(),
                    selectedProduct
                )
            }
            bivNumber.onTextChanged { bivNumber.setClearDrawable() }
            btnCek.setSingleClickListener {
                Utils.hideKeyboard(requireActivity())
                addToCart()
            }
            if (accountNumber.isNotNullOrEmpty()) {
                showViewAndSetData(accountNumber, billerCode, phoneNumber)
            }
        }
    }

    private fun showViewAndSetData(accountNumber: String, code: String, phoneNumber: String) =
        with(binding) {
            bivCustomerNumber.showView()
            bivNumber.showView()
            btnCek.showView()
            bivCustomerNumber.setText(accountNumber)
            tvChooseArea.text = code
            tvPdamArea.visibility = code.isNotNullOrBlank().asVisibility()
            tvPdamArea.text = code
            bivNumber.setText(phoneNumber)
            recentBiller = code
            btnCek.callOnClick()
        }

    private fun addToCart() {
        viewModel.addToCart(
            FinproAddCartRequest(
                sku = "",
                beneficiary = FinproBeneficiary(
                    category = category,
                    accountNumber = binding.bivCustomerNumber.getText(),
                    code = recentBiller,
                    phoneNumber = binding.bivNumber.getText()
                ),
                userType = PpobConst.USERTYPE_TREATMENT
            )
        )
    }

    private fun showDialog() {
        Utils.hideKeyboard(requireActivity())
        WaterBillAreaDialog.getInstance(selectedProduct)
            .show(childFragmentManager, WaterBillAreaDialog.TAG)
    }

    private fun subscribeState() {
        viewModel.observeEvent.observe(viewLifecycleOwner) {
            when (it) {
                is PpobEvent.ToDetail -> {
                    addToCartResponse = it.orderDetail
                    trackFetchBillEvent()
                    binding.bivCustomerNumber.setSuccessState("")
                    showBillDetailsBottomSheet()
                }

                is PpobEvent.ServerError -> {
                    trackFetchBillEvent(it.message.orEmpty())
                    binding.bivCustomerNumber.setSuccessState("")
                    showPaymentDownBottomSheet(true, it.message)
                }

                is PpobEvent.InternetError -> {
                    trackFetchBillEvent(it.message.orEmpty())
                    binding.bivCustomerNumber.setSuccessState("")
                    showPaymentDownBottomSheet(false, it.message)
                }

                is PpobEvent.OtherError -> {
                    trackFetchBillEvent(it.message.orEmpty())
                    binding.bivCustomerNumber.setErrorMessage(it.message.orEmpty())
                }

                else -> {
                    binding.bivCustomerNumber.setSuccessState("")
                }
            }
        }
        viewModel.viewState.observe(viewLifecycleOwner) {
            if (it.showLoading) {
                binding.btnCek.showProgress {
                    buttonTextRes = null
                    progressColor = Color.BLACK
                }
            } else {
                binding.btnCek.hideProgress(R.string.bt_cek)
            }
            if (it.numberLengthInvalid) {
                binding.bivCustomerNumber.setErrorMessage(it.errorMessage)
                binding.btnCek.isEnabled = false
            } else if (binding.bivCustomerNumber.getText().isNotBlank()) {
                binding.bivCustomerNumber.setSuccessState("")
                binding.btnCek.isEnabled = true
            }
        }
    }

    private fun showPaymentDownBottomSheet(isServiceDown: Boolean, message: String? = null) {
        if (isServiceDown) {
            ErrorBottomSheet.createInstance(
                ErrorBottomSheet.Companion.ApiErrorType.API_ERROR,
                message?.ifEmpty { getString(R.string.try_again_or_wait) }
            ).show(childFragmentManager, ErrorBottomSheet.TAG)
        } else {
            ErrorBottomSheet.createInstance(
                ErrorBottomSheet.Companion.ApiErrorType.CONNECTION_ERROR,
                message
            ).show(childFragmentManager, ErrorBottomSheet.TAG)
        }
    }

    private fun trackFetchBillEvent(errorMessage: String = "") {
        val map = hashMapOf<String, String>()
        map[PpobAnalytics.ID] = binding.bivCustomerNumber.getText()
        map[PpobAnalytics.PHONE_FILLED] = binding.bivNumber.getText()
        map[PpobAnalytics.PDAM_DOMICILE] = binding.tvChooseArea.text.toString()
        map[PpobAnalytics.TYPE] = PpobAnalytics.PPOB_PDAM
        map[PpobAnalytics.STATUS] = if (errorMessage.isBlank()) {
            PpobAnalytics.STATUS_SUCCESS
        } else {
            PpobAnalytics.STATUS_FAILED
        }
        map[PpobAnalytics.REASON] = errorMessage
    }

    private fun showBillDetailsBottomSheet() {
        val ppobBillDetailsBottomSheet =
            PpobBillDetailsBottomSheet.createInstance(addToCartResponse, category)
        ppobBillDetailsBottomSheet.show(childFragmentManager, PpobBillDetailsBottomSheet.TAG)
    }

    override fun setData(profilesItem: ProfilesItem) {
        showViewAndSetData(
            profilesItem.details?.accountNumber.orEmpty(),
            profilesItem.biller?.code.orEmpty(),
            profilesItem.details?.phoneNumber.orEmpty()
        )
    }

    override fun setSelectedProduct(selectedProduct: PpobProduct) {
        this.selectedProduct = selectedProduct
        recentBiller = selectedProduct.productInfo?.biller.orEmpty()
        with(binding) {
            if (bivCustomerNumber.getText().isNotBlank() &&
                viewModel.checkCustomerNumberValidation(
                    bivCustomerNumber.getText(),
                    requireContext(),
                    selectedProduct
                )
            ) {
                addToCart()
            }
            tvChooseArea.text = selectedProduct.name.orEmpty()
            tvPdamArea.visibility =
                selectedProduct.productInfo?.billerName.isNotNullOrBlank().asVisibility()
            tvPdamArea.text = selectedProduct.productInfo?.billerName.orEmpty()
            bivCustomerNumber.setInputType(
                when (selectedProduct.validationInfo?.valueType) {
                    ALPHA_NUMERIC -> InputType.TYPE_CLASS_TEXT
                    NUMERIC -> InputType.TYPE_CLASS_NUMBER
                    else -> InputType.TYPE_CLASS_NUMBER
                }
            )
            bivCustomerNumber.showView()
            bivNumber.showView()
            btnCek.showView()
        }
    }

    override fun refreshFavouritesTab() {
        recentAndFavouriteFragment?.refreshFavAndRecentTab()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding?.bivCustomerNumber?.removeTextChanged()
        _binding = null
    }
}
