package com.bukuwarung.edc.ppob.ewallet.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.payments.usecase.FinproUseCase
import com.bukuwarung.edc.ppob.common.model.PpobEvent
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.edc.util.errorMessage
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

@HiltViewModel
class EwalletBillersViewModel @Inject constructor(private val finproUseCase: FinproUseCase) :
    ViewModel() {

    val eventStatus = MutableLiveData<PpobEvent>()

    private suspend fun setEventStatus(event: PpobEvent) = withContext(Dispatchers.Main) {
        eventStatus.value = event
    }

    fun getBillerList() = viewModelScope.launch(Dispatchers.IO) {
        try {
            finproUseCase.getBillers(PpobConst.CATEGORY_EWALLET).let {
                if (it.isSuccessful) {
                    setEventStatus(PpobEvent.ShowBillerList(it.body().orEmpty()))
                } else {
                    if (it.errorMessage() != Constant.NO_INTERNET_ERROR_MESSAGE) {
                        setEventStatus(PpobEvent.ServerError(it.errorMessage()))
                    } else {
                        setEventStatus(PpobEvent.InternetError(it.errorMessage()))
                    }
                }
            }
        } catch (e: Exception) {
            setEventStatus(PpobEvent.ServerError(""))
        }
    }
}
