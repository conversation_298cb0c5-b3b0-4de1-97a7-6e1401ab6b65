package com.bukuwarung.edc.ppob.train.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.ItemLoadMoreBinding
import com.bukuwarung.edc.databinding.ViewTrainPassengerBinding
import com.bukuwarung.edc.payments.data.model.TrainPassenger
import com.bukuwarung.edc.ppob.constants.PpobConst.ADULT_TYPE
import com.bukuwarung.edc.util.setDrawable
import com.bukuwarung.edc.util.singleClick

class TrainPassengerAdapter(private val callback: Callback, private val totalSize: Int) :
    RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val VIEW_TYPE_ITEM = 0
        const val VIEW_TYPE_SEE_ALL = 1
        const val VIEW_TYPE_SEE_LESS = 2
        const val MIN_SIZE = 6
    }

    private var list = emptyList<TrainPassenger>()
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder =
        if (viewType == VIEW_TYPE_ITEM) {
            TrainPassengerViewHolder(
                ViewTrainPassengerBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
            )
        } else {
            LoadMorePassengersViewHolder(
                ItemLoadMoreBinding.inflate(LayoutInflater.from(parent.context), parent, false)
            )
        }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder is TrainPassengerViewHolder) {
            holder.bind(list[position], position)
        } else if (holder is LoadMorePassengersViewHolder) {
            holder.bind(list[position].viewType)
        }
    }

    override fun getItemCount(): Int = list.size

    fun setData(passengers: List<TrainPassenger>) {
        this.list = passengers
        notifyDataSetChanged()
    }

    override fun getItemViewType(position: Int) = list[position].viewType

    class TrainPassengerViewHolder(private val binding: ViewTrainPassengerBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(item: TrainPassenger, pos: Int) = with(binding) {
            passengerDetailView.binding.tvDetailTransaksi.text = String.format(
                "%s %s: %s",
                root.context.getString(R.string.passenger),
                pos + 1,
                if (item.type == ADULT_TYPE) {
                    root.context.getString(R.string.adult)
                } else {
                    root.context.getString(R.string.baby)
                }
            )
            passengerDetailView.setTrainPassengerView(item)
        }
    }

    inner class LoadMorePassengersViewHolder(private val binding: ItemLoadMoreBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(viewType: Int) = binding.seeAllTxt.apply {
            if (viewType == VIEW_TYPE_SEE_ALL) {
                text = String.format(
                    "%s +%s %s",
                    context.getString(R.string.label_view),
                    totalSize - (list.size - 1),
                    context.getString(R.string.customers)
                )
                setDrawable(0, R.drawable.ic_chevron_up, 0, 0)
            } else {
                text = context.getString(R.string.label_close)
                setDrawable(0, R.drawable.ic_chevron_down, 0, 0)
            }
            singleClick {
                if (viewType == VIEW_TYPE_SEE_ALL) {
                    callback.loadMoreCtaClicked()
                } else {
                    callback.loadLessCtaClicked()
                }
            }
        }
    }

    interface Callback {
        fun loadMoreCtaClicked()
        fun loadLessCtaClicked()
    }
}
