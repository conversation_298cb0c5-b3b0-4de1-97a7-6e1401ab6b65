package com.bukuwarung.edc.ppob.pdam.view

import android.content.Context
import android.content.res.ColorStateList
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.DialogWaterBillAreaBinding
import com.bukuwarung.edc.global.base.BaseDialogFragment
import com.bukuwarung.edc.ppob.common.model.PpobEvent
import com.bukuwarung.edc.ppob.common.model.PpobViewState
import com.bukuwarung.edc.ppob.common.viewmodel.PpobBillersViewModel
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.edc.ppob.pdam.adapter.WaterBillAreaAdapter
import com.bukuwarung.edc.util.afterTextChanged
import com.bukuwarung.edc.util.getColorCompat
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.showView
import com.bukuwarung.payments.data.model.PpobProduct
import com.bukuwarung.ui_component.base.BaseErrorView
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class WaterBillAreaDialog : BaseDialogFragment() {

    private var dialogWaterBillAreaBinding: DialogWaterBillAreaBinding? = null
    private val binding get() = dialogWaterBillAreaBinding!!
    private var iCommunicator: ICommunicator? = null
    private var ppobProductList: List<PpobProduct>? = null
    private var selectedProduct: PpobProduct? = null
    private lateinit var adapter: WaterBillAreaAdapter

    private val viewModel: PpobBillersViewModel by viewModels()

    companion object {
        const val TAG = "WaterBillAreaDialog"
        private const val SELECTED_PRODUCT = "selected_product"
        fun getInstance(selectedProduct: PpobProduct?) = WaterBillAreaDialog().apply {
            arguments = Bundle().apply { putParcelable(SELECTED_PRODUCT, selectedProduct) }
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        parentFragment?.let { iCommunicator = it as? ICommunicator }
        if (context is ICommunicator) iCommunicator = context
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        dialogWaterBillAreaBinding = DialogWaterBillAreaBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        with(binding) {
            selectedProduct = arguments?.getParcelable(SELECTED_PRODUCT) as? PpobProduct
            viewModel.getPpobProductsWithBillerDetails(
                PpobConst.CATEGORY_PDAM,
                emptyMap()
            )
            bukuErrorView.addCallback(errorViewCallBack)
            etSearch.backgroundTintList =
                context?.getColorCompat(R.color.black_10)?.let { ColorStateList.valueOf(it) }
            etSearch.afterTextChanged { searchText ->
                if (!ppobProductList.isNullOrEmpty()) {
                    viewModel.onSearchTextChanged(searchText, ppobProductList!!)
                }
            }
            viewModel.observeEvent.observe(this@WaterBillAreaDialog) {
                when (it) {
                    is PpobEvent.ShowProductsList -> {
                        rvItem.showView()
                        tvEmpty.hideView()
                        it.list.forEach { product -> product.isSelected = false }
                        val product =
                            it.list.filter { product -> product.sku == selectedProduct?.sku }
                        product.getOrNull(0)?.isSelected = true
                        if (!it.isFilteredCall) {
                            ppobProductList = it.list
                            adapter = WaterBillAreaAdapter(it.list, ::clickAction)
                            rvItem.layoutManager = LinearLayoutManager(context)
                            rvItem.adapter = adapter
                        } else {
                            if (it.list.isNotEmpty()) {
                                adapter.setData(it.list)
                            } else {
                                rvItem.hideView()
                                tvEmpty.showView()
                            }
                        }
                    }

                    else -> {}
                }
            }
            viewModel.viewState.observe(this@WaterBillAreaDialog) {
                setViewState(it)
            }
            includeToolBar.tvHelp.hideView()
            includeToolBar.tbPpob.navigationIcon =
                ContextCompat.getDrawable(requireContext(), R.drawable.ic_arrow_back)
            includeToolBar.tbPpob.setNavigationOnClickListener {
                dismiss()
            }
            includeToolBar.toolBarLabel.text = getString(R.string.select_region)
        }
    }

    private fun setViewState(viewState: PpobViewState) {
        with(binding) {
            when {
                viewState.showLoading -> {
                    sflLayout.showShimmer(true)
                    sflLayout.showView()
                    bukuErrorView.hideView()
                }

                viewState.searchNoInternetError -> {
                    sflLayout.hideShimmer()
                    sflLayout.hideView()
                    bukuErrorView.showView()
                    bukuErrorView.setErrorType(
                        BaseErrorView.Companion.ErrorType.CUSTOM,
                        getString(R.string.no_connection_title),
                        getString(R.string.no_connection_message),
                        getString(R.string.reload),
                        R.mipmap.ic_no_inet
                    )
                }

                viewState.searchServerError -> {
                    sflLayout.hideShimmer()
                    sflLayout.hideView()
                    bukuErrorView.showView()
                    bukuErrorView.setErrorType(
                        BaseErrorView.Companion.ErrorType.CUSTOM,
                        getString(R.string.server_error_title),
                        getString(R.string.server_error_subtitle),
                        getString(R.string.reload),
                        R.mipmap.ic_server_down
                    )
                }

                !viewState.showLoading -> {
                    sflLayout.hideShimmer()
                    sflLayout.hideView()
                    bukuErrorView.hideView()
                }
            }
        }
    }

    private var errorViewCallBack = object : BaseErrorView.Callback {
        override fun ctaClicked() {
            viewModel.getPpobProductsWithBillerDetails(
                PpobConst.CATEGORY_PDAM,
                emptyMap()
            )
        }

        override fun messageClicked() {}
    }

    private fun clickAction(selectedProduct: PpobProduct) {
        this.selectedProduct = selectedProduct
        iCommunicator?.setSelectedProduct(selectedProduct)
        dismiss()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        dialogWaterBillAreaBinding = null
    }

    interface ICommunicator {
        fun setSelectedProduct(selectedProduct: PpobProduct)
    }
}
