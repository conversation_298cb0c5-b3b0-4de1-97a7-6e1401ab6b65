package com.bukuwarung.edc.ppob.common.view

import android.content.Context
import android.graphics.Outline
import android.graphics.drawable.ColorDrawable
import android.os.Build
import android.os.Bundle
import android.view.View
import android.view.ViewOutlineProvider
import com.bukuwarung.edc.databinding.TransactionsPopUpDialogBinding
import com.bukuwarung.edc.global.base.BaseDialog
import com.bukuwarung.edc.global.enums.BaseDialogType
import com.bumptech.glide.Glide

class TransactionsPopUpDialog(
    context: Context,
    private val action: () -> Unit,
    private val onDismissAction: () -> Unit,
    val image: Int,
    val headText: String,
    val bodyText: String,
    val buttonText: String,
    val imageUrl: String = ""
) : BaseDialog(context, BaseDialogType.POPUP) {

    private val binding by lazy {
        TransactionsPopUpDialogBinding.inflate(layoutInflater).also {
            setupViewBinding(it.root)
        }
    }

    private var transactionsImage = 0
    private var transactionsHeadText = ""
    private var transactionsBodyText = ""
    private var transactionsButtonText = ""

    init {
        transactionsImage = image
        transactionsHeadText = headText
        transactionsBodyText = bodyText
        transactionsButtonText = buttonText
        setCancellable(false)
        setUseFullWidth(false)
    }

    override val resId: Int = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setupView()
    }

    private fun setupView() {
        this.window?.setBackgroundDrawable(ColorDrawable(android.graphics.Color.TRANSPARENT))
        if (imageUrl.isNotBlank()) {
            Glide.with(context)
                .load(imageUrl)
                .placeholder(transactionsImage)
                .error(transactionsImage)
                .into(binding.transactionsImage)
        } else {
            binding.transactionsImage.setImageResource(transactionsImage)
        }
        binding.closeDialog.bringToFront()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            val provider: ViewOutlineProvider = object : ViewOutlineProvider() {
                override fun getOutline(view: View, outline: Outline) {
                    outline.setRoundRect(0, 0, view.getWidth(), view.getHeight() + 40, 40f)
                }
            }
            binding.transactionsImage.setOutlineProvider(provider)
            binding.transactionsImage.setClipToOutline(true)
        }
        if (transactionsHeadText == "") {
            binding.transactionsHeading.visibility = View.GONE
        } else {
            binding.transactionsHeading.text = transactionsHeadText
        }
        binding.transactionsBody.text = transactionsBodyText
        binding.button.text = transactionsButtonText
        binding.button.setOnClickListener {
            action()
            dismiss()
        }
        binding.closeDialog.setOnClickListener {
            onDismissAction()
            dismiss()
        }
    }
}
