package com.bukuwarung.edc.ppob.common.model

data class PpobViewState(
    val showLoading: <PERSON>olean = false,
    val numberInvalid: Boolean = false,
    val numberLengthInvalid: Boolean = false,
    val customerIdLengthInvalid: <PERSON>olean = false,
    val serverError: Boolean = false,
    val serverErrorMessage: String? = null,
    val noInternetError: <PERSON>olean = false,
    val providerLogo: Int = 0,
    val phoneNumberLength: Int = 0,
    val otherError: Boolean = false,
    val isMonthSelected: Boolean = false,
    val isAreaSelected: <PERSON>olean = false,
    val searchNoInternetError: Boolean = false,
    val searchServerError: Boolean = false,
    val errorMessage: String = "",
    val showShimmer: Boolean = false
)
