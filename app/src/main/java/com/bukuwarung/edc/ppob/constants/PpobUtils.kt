package com.bukuwarung.edc.ppob.constants

import android.content.Context
import android.content.Intent
import androidx.fragment.app.FragmentManager
import com.bukuwarung.edc.BuildConfig
import com.bukuwarung.edc.R
import com.bukuwarung.edc.ppob.baseactivity.view.PpobActivity
import com.bukuwarung.edc.ppob.common.model.PpobErrorPopupContent
import com.bukuwarung.edc.ppob.common.view.GenericConfirmationDialog
import com.bukuwarung.edc.ppob.common.view.PpobDialog
import com.bukuwarung.edc.ppob.common.view.TransactionsPopUpDialog
import com.bukuwarung.edc.ppob.train.view.TrainTicketDetailsActivity
import com.bukuwarung.edc.ppob.train.view.TrainTicketWebviewActivity
import com.bukuwarung.edc.util.DateTimeUtils
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.orNil
import com.bukuwarung.edc.util.returnObject
import com.bukuwarung.edc.webview.WebviewActivity
import com.bukuwarung.edc.webview.constant.ClassConstants
import com.bukuwarung.payments.data.model.State
import com.google.gson.reflect.TypeToken

object PpobUtils {

    fun showPpobUnAvailable(context: Context, category: String) {
        val ppobErrorPopupContent: PpobErrorPopupContent?
        var bodyText: String
        val ppobErrorPopupJson: String = PpobRemoteConfig.getPpobErrorPopupContent()
        val type = object : TypeToken<List<PpobErrorPopupContent>>() {}.type
        val ppobErrorPopupContentList: List<PpobErrorPopupContent>? =
            type.returnObject(ppobErrorPopupJson)
        ppobErrorPopupContent = ppobErrorPopupContentList?.firstOrNull {
            it.ppobCategory?.contains(category).isTrue
        }
        val startTimeInMillis = ppobErrorPopupContent?.startTimeMillis.orNil
        val waitTimeInMillis =
            DateTimeUtils.convertHoursToMillis(ppobErrorPopupContent?.errorWaitTimeHrs.orNil)
        val currentTimeInMillis = DateTimeUtils.getCurrentUTCTime()
        val diffWithCurrentTime = startTimeInMillis + waitTimeInMillis - currentTimeInMillis
        if (diffWithCurrentTime > 0) {
            bodyText = ppobErrorPopupContent?.tvPpobErrorBody?.replace(
                "{category}",
                context.getString(PpobConst.CATEGORY_NAME_MAP[category] ?: R.string.ppob)
            ) ?: context.getString(R.string.ppob_unavailable, category)
            val diffWithCurrentTimeInhours =
                DateTimeUtils.convertMillisToHours(diffWithCurrentTime) + 1
            val timerText = diffWithCurrentTimeInhours.toString()
            bodyText = bodyText.replace("{timer}", timerText)
        } else {
            bodyText = ppobErrorPopupContent?.textAfterTimerComplete
                ?: context.getString(R.string.ppob_unavailable, category)
        }
        val transactionsPopUpDialog = TransactionsPopUpDialog(
            context = context,
            action = {},
            onDismissAction = {},
            image = R.mipmap.ic_server_unreachable,
            headText = ppobErrorPopupContent?.tvPpobErrorHeading
                ?: context.getString(R.string.sorry_disturbance),
            bodyText = bodyText,
            buttonText = ppobErrorPopupContent?.tvPpobErrorButton
                ?: context.getString(R.string.back),
            imageUrl = ppobErrorPopupContent?.icPpobError.orEmpty()
        )
        transactionsPopUpDialog.window?.setBackgroundDrawableResource(
            R.drawable.round_corner_white_picture_picker
        )
        val minWidth =
            (context.resources.displayMetrics.widthPixels * 0.90).toInt()
        transactionsPopUpDialog.setMinWidth(minWidth)
        transactionsPopUpDialog.show()
    }

    fun showPpobComingSoonDialog(context: Context) {
        PpobDialog(
            context = context,
            action = {},
            onDismissAction = {},
            title = context.getString(R.string.feature_under_development),
            body = context.getString(R.string.feature_under_development_body),
            buttonText = context.getString(R.string.understand),
            image = R.drawable.ic_coming_soon
        ).show()
    }

    fun showRemoveFavouriteDialog(context: Context, action: () -> Unit) {
        context.let {
            GenericConfirmationDialog(
                context = context,
                titleRes = R.string.remove_favourite,
                bodyRes = R.string.remove_favourite_subtitle,
                btnLeftRes = R.string.batal,
                btnRightRes = R.string.delete,
                rightBtnCallback = { action() },
                leftBtnCallback = {}
            ).show()
        }
    }

    fun showRemoveBankDialog(context: Context, action: () -> Unit) {
        context.let {
            GenericConfirmationDialog(
                context = context,
                titleRes = R.string.remove_bank,
                bodyRes = R.string.remove_bank_subtitle,
                btnLeftRes = R.string.batal,
                btnRightRes = R.string.delete,
                rightBtnCallback = { action() },
                leftBtnCallback = {}
            ).show()
        }
    }

    fun getPpobCategoryActivityIntent(
        fragmentManager: FragmentManager,
        context: Context,
        category: String?,
        from: String = "",
        accountNumber: String = "",
        phoneNumber: String = "",
        code: String = "",
        layoutType: String = "",
        machineNumber: String = "",
        frameNumber: String = ""
    ): Intent? {
        val state =
            PpobRemoteConfig.getPpobConfigs().ppobList?.firstOrNull {
                it.category == category
            }?.state
        when {
            state == State.COMING_SOON -> {
                showPpobComingSoonDialog(context)
                return null
            }

            state == State.NOT_AVAILABLE -> {
                showPpobUnAvailable(
                    context,
                    context.getString(PpobConst.CATEGORY_NAME_MAP[category] ?: R.string.ppob)
                )
                return null
            }

            else -> {
                when (category) {
                    PpobConst.CATEGORY_TRAIN_TICKET -> {
                        return TrainTicketWebviewActivity.createIntent(context)
                    }

                    PpobConst.CATEGORY_VOUCHER_GAME -> {
                        return Intent(
                            context,
                            WebviewActivity::class.java
                        ).putExtra(
                            ClassConstants.WEBVIEW_URL,
                            BuildConfig.VOUCHER_GAME_URL + Utils.getPaymentAccountId()
                        )
                    }
                }
                return PpobActivity.createIntent(
                    context = context,
                    from = from,
                    category = category ?: PpobConst.CATEGORY_PULSA,
                    accountNumber = accountNumber,
                    phoneNumber = phoneNumber,
                    code = code,
                    layoutType = layoutType,
                    machineNumber = machineNumber,
                    frameNumber = frameNumber
                )
            }
        }
    }

    fun getPpobDetailActivityIntent(
        context: Context,
        category: String = "",
        accountNumber: String = ""
    ): Intent? = when (category) {
        PpobConst.CATEGORY_TRAIN_TICKET -> {
            TrainTicketDetailsActivity.createIntent(context, accountNumber)
        }

        else -> null
    }

    fun getReason(reasonCode: String?): String = when (reasonCode) {
        "101" -> "system down"
        "102" -> "lower min"
        "103" -> "higher max"
        "104" -> "not verified kyc"
        "105" -> "not available"
        else -> "other"
    }
}
