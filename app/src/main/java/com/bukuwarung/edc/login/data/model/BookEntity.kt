package com.bukuwarung.edc.login.data.model

data class BookEntity(
    var altBookId: String? = null,
    var createdAt: Long = 0,
    var createdByDevice: String? = null,
    var createdByUser: String? = null,
    var dirty: Int = 0,
    var serverSeq: Long = 0,
    var updatedAt: Long = 0,
    var updatedByDevice: String? = null,
    var updatedByUser: String? = null,
    var bookId: String = "",
    var bookName: String? = null,
    var bookType: Int? = null,
    var bookTypeName: String? = null,
    var businessAddress: String? = null,
    var businessEmail: String? = null,
    var businessImage: String? = null,
    var businessImageUploadPending: Int = 0,
    var businessName: String? = null,
    var businessOwnerName: String? = null,
    var businessPhone: String? = null,
    var businessTagLine: String? = null,
    var deleted: Int = 0,
    var enableSmsAlerts: Int = 0,
    var enableTxnDetailSharing: Int? = null,
    var language: Int = -1,
    var ownerId: String? = null,
    var enabledPayment: Int = 0,
    var isGuest: Int = 0,
    var businessLogo: String? = null,
    var businessLogoUploadPending: Int = 0,
    var isDailyBusinessUpdateSeen: Int = 0,
    var operatingHourStart: String = "",
    var operatingHourEnd: String = "",
    var operatingDays: String = "",
    var empCount: Int = 0,
    var outletCount: Int = 0,
    var establishmentYear: String = "",
    var profileCompletionProgress: Int = 0,
    var province: String = "",
    var city: String = "",
    var district: String = "",
    var subdistrict: String = "",
    var postalCode: String = "",
    var production: String = "",
    var productBuyer: String = "",
    var monthlyTurnover: Int = 0
) {
    // Default constructor is provided by Kotlin when all parameters have default values

    fun hasCompletedProfile(): Boolean = !businessName.isNullOrBlank() &&
        businessName != "Usaha Saya" &&
        !businessOwnerName.isNullOrBlank() &&
        businessOwnerName != "BukuWarung" &&
        bookType != null && bookType != -1

    fun hasCompletedProfileWithoutOwnerName(): Boolean = !businessName.isNullOrBlank() &&
        businessName != "Usaha Saya" &&
        businessName != "BukuWarung" &&
        bookType != null && bookType != -1

    fun hasCompletedProfileWithOwnerName(): Boolean = !businessOwnerName.isNullOrBlank() &&
        businessOwnerName != "Usaha Saya" &&
        businessOwnerName != "BukuWarung" &&
        businessOwnerName != "Profil Saya"

    fun hasCompletedCard(): Boolean = !businessName.isNullOrBlank() &&
        businessName != "Usaha Saya" &&
        !businessOwnerName.isNullOrBlank() &&
        businessOwnerName != "BukuWarung" &&
        bookType != null && bookType != -1 &&
        !businessTagLine.isNullOrBlank() &&
        !businessAddress.isNullOrBlank() &&
        !businessPhone.isNullOrBlank() &&
        !businessEmail.isNullOrBlank()

    fun hasCompletedCardNoEmail(): Boolean = !businessName.isNullOrBlank() &&
        businessName != "Usaha Saya" &&
        !businessOwnerName.isNullOrBlank() &&
        businessOwnerName != "BukuWarung" &&
        bookType != null &&
        !businessTagLine.isNullOrBlank() &&
        !businessAddress.isNullOrBlank() &&
        !businessPhone.isNullOrBlank()

    fun getInitial(): String = businessName?.firstOrNull()?.uppercase() ?: "?"
}
