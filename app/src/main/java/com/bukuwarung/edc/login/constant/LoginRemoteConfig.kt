package com.bukuwarung.edc.login.constant

import com.bukuwarung.edc.app.config.AppConfig
import com.bukuwarung.edc.util.RemoteConfigUtils

object LoginRemoteConfig {

    const val TNC = "tnc_url"
    const val PRIVACY = "privacy_policy_url"

    const val TNC_URL = "https://bukuwarung.com/syarat-dan-ketentuan-penggunaan-edc-bukuwarung/"
    const val PRIVACY_POLICY_URL = "https://bukuwarung.com/"

    const val TNC_URL_ATMPRO = "https://miniatmpro.com/terms-and-conditions"
    const val PRIVACY_POLICY_URL_ATMPRO = "https://miniatmpro.com/terms-and-conditions"

    fun tnc(): String = AppConfig.current.variantConfig.tncUrl
    fun privacyPolicy(): String = AppConfig.current.variantConfig.privacyPolicyUrl

    fun getTncUrl(): String = RemoteConfigUtils.remoteConfig.getString(TNC)
    fun getPrivacyPolicyUrl(): String = RemoteConfigUtils.remoteConfig.getString(PRIVACY)
}
