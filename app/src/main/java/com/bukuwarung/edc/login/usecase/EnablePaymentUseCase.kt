package com.bukuwarung.edc.login.usecase

import com.bukuwarung.edc.payments.data.model.EnableCustomerResponse
import com.bukuwarung.edc.payments.data.repository.PaymentRepository
import javax.inject.Inject
import retrofit2.Response

class EnablePaymentUseCase @Inject constructor(private val paymentRepository: PaymentRepository) {

    suspend fun enablePayment(
        accountId: String,
        businessName: String
    ): Response<EnableCustomerResponse> = paymentRepository.enablePaymentAccount(
        accountId,
        mapOf(Pair("business_name", businessName))
    )
}
