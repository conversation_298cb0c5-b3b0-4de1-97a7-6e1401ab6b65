package com.bukuwarung.edc.login.data.repository

import com.bukuwarung.edc.login.data.model.KycStatusResponse
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Path

interface JanusRemoteDataSource {

    @POST("janus/api/accounts")
    suspend fun createKycAccount(): Response<KycStatusResponse>

    @GET("janus/api/accounts/{account_id}")
    suspend fun getKycAccount(@Path("account_id") accountId: String): Response<KycStatusResponse>
}
