package com.bukuwarung.edc.login.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class KycChecks(
    @SerializedName("status")
    val status: String? = null,
    @SerializedName("attachments")
    val attachments: List<KycAttachments>? = null,
    @SerializedName("check_id")
    val checkId: String? = null,
    @SerializedName("check_type")
    val checkType: String? = null,
    @SerializedName("failure_reason")
    val failureReason: String? = null
) : Parcelable
