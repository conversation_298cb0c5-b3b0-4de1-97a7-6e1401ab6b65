package com.bukuwarung.edc.login.data.model

enum class KycStatus {
    INITIAL,
    PENDING_VERIFICATION,
    PENDING_MANUAL_VERIFICATION,
    MANUALLY_VERIFIED,
    VERIFIED,
    REJECTED
}

fun KycStatus?.isVerified(): Boolean =
    this == KycStatus.MANUALLY_VERIFIED || this == KycStatus.VERIFIED

fun KycStatus?.isPending(): Boolean =
    this == KycStatus.PENDING_MANUAL_VERIFICATION || this == KycStatus.PENDING_VERIFICATION
