package com.bukuwarung.edc.login.data.repository

import com.bukuwarung.edc.login.data.datasource.LoginDataSource
import com.bukuwarung.edc.login.data.model.LoginRequest
import javax.inject.Inject

class LoginRepository @Inject constructor(private val loginDataSource: LoginDataSource) {

    suspend fun fetchOtp(loginRequest: LoginRequest, phone: String) =
        loginDataSource.getOtp(loginRequest, phone)
}
