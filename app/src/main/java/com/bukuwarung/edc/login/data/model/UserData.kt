package com.bukuwarung.edc.login.data.model

import com.google.gson.annotations.SerializedName

data class UserData(
    val bankAccount: String,
    val bankName: String,
    val beneficiaryName: String,
    val deviceId: String,
    val janusAccountId: String,
    @Deprecated(message = "please use from ac/api/v2/edc/device-detail")
    val paymentAccountId: String,
    val serialNumber: String,
    val storeAddress: String,
    val storeName: String,
    val tid: String,
    val userId: String,
    @SerializedName("merchant_id")
    val merchantId: String? = null,
)
