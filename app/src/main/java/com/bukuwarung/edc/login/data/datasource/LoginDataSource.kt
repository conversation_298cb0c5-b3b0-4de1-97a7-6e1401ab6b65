package com.bukuwarung.edc.login.data.datasource

import com.bukuwarung.edc.login.data.model.LoginRequest
import com.bukuwarung.edc.login.data.model.LoginResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.POST
import retrofit2.http.Query

interface LoginDataSource {

    @POST("api/v2/auth/otp/send")
    suspend fun getOtp(
        @Body request: LoginRequest,
        @Query("phone_number") phone: String
    ): Response<LoginResponse>
}
