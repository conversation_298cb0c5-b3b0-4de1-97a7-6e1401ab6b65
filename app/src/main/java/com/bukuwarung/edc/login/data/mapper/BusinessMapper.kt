@file:Suppress("DEPRECATION")

package com.bukuwarung.data.business.implementation.mapper

import com.bukuwarung.edc.login.data.model.BookEntity
import com.bukuwarung.edc.login.data.model.Business
import java.util.Date

internal fun BookEntity.toBusiness(): Business = Business(
    id = null,
    businessId = bookId,
    ownerId = ownerId,
    ownerName = businessOwnerName,
    category = Business.Category(
        id = null,
        categoryId = "$bookType",
        ownerId = null,
        name = bookTypeName,
        displayName = Business.Category.Language(
            id = null,
            en = null
        ),
        image = null,
        displayOrder = null,
        deleted = null,
        createdAt = null,
        lastModifiedAt = null
    ),
    name = businessName,
    phone = businessPhone,
    email = businessEmail,
    address = Business.Address(
        name = businessAddress,
        province = province,
        city = city,
        district = district,
        subDistrict = subdistrict,
        postalCode = postalCode
    ),
    image = businessImage,
    logo = businessLogo,
    tagLine = businessTagLine,
    operational = Business.Operational(
        startHour = operatingHourStart,
        endHour = operatingHourEnd,
        days = operatingDays
    ),
    totalEmployee = empCount,
    totalOutlet = outletCount,
    year = establishmentYear,
    isBnpl = null,
    profileCompletion = profileCompletionProgress,
    deleted = deleted == 1,
    createdByDevice = createdByDevice,
    modifiedByDevice = updatedByDevice,
    createdAt = createdAt?.let(::Date),
    lastModifiedAt = updatedAt?.let(::Date),
    temp = Business.Temp(
        enableSmsAlerts = enableSmsAlerts == 1,
        businessImageUploadPending = businessImageUploadPending,
        createdByUser = createdByUser,
        updatedByUser = updatedByUser,
        dirty = dirty,
        serverSeq = serverSeq,
        enabledPayment = enabledPayment == 1,
        isGuest = isGuest == 1,
        isDailyBusinessUpdateSeen = isDailyBusinessUpdateSeen == 1,
        production = production,
        productBuyer = productBuyer,
        monthlyTurnover = monthlyTurnover
    )
)

internal fun Business.toBusinessEntity(): BookEntity = BookEntity().also { entity ->
    entity.bookId = businessId.orEmpty()
    entity.altBookId = null
    entity.ownerId = ownerId
    entity.bookName = name
    entity.bookType = category?.categoryId?.toIntOrNull()
    entity.bookTypeName = category?.name
    entity.businessName = name
    entity.businessOwnerName = ownerName
    entity.businessAddress = address?.name
    entity.businessImage = image
    entity.businessLogo = logo
    entity.businessPhone = phone
    entity.businessEmail = email
    entity.businessTagLine = tagLine
    entity.operatingHourStart = operational?.startHour.orEmpty()
    entity.operatingHourEnd = operational?.endHour.orEmpty()
    entity.operatingDays = operational?.days.orEmpty()
    entity.empCount = totalEmployee ?: 0
    entity.outletCount = totalOutlet ?: 0
    entity.establishmentYear = year.orEmpty()
    entity.province = address?.province.orEmpty()
    entity.city = address?.city.orEmpty()
    entity.district = address?.district.orEmpty()
    entity.subdistrict = address?.subDistrict.orEmpty()
    entity.postalCode = address?.postalCode.orEmpty()
    entity.deleted = deleted?.compareTo(false) ?: 0
    entity.profileCompletionProgress = profileCompletion ?: 0
    entity.enableSmsAlerts = temp?.enableSmsAlerts?.compareTo(false) ?: 0
    entity.enableTxnDetailSharing = null
    entity.language = -1
    entity.businessImageUploadPending = temp?.businessImageUploadPending ?: 0
    entity.businessLogoUploadPending = 0
    entity.createdByDevice = createdByDevice
    entity.updatedByDevice = modifiedByDevice
    entity.createdByUser = temp?.createdByUser
    entity.updatedByUser = temp?.updatedByUser
    entity.createdAt = createdAt?.time ?: 0L
    entity.updatedAt = lastModifiedAt?.time ?: 0L
    entity.dirty = temp?.dirty ?: 0
    entity.serverSeq = temp?.serverSeq ?: 0L
    entity.enabledPayment = temp?.enabledPayment?.compareTo(false) ?: 0
    entity.isGuest = temp?.isGuest?.compareTo(false) ?: 0
    entity.isDailyBusinessUpdateSeen = temp?.isDailyBusinessUpdateSeen?.compareTo(false) ?: 0
    entity.production = temp?.production.orEmpty()
    entity.productBuyer = temp?.productBuyer.orEmpty()
    entity.monthlyTurnover = temp?.monthlyTurnover ?: 0
}
