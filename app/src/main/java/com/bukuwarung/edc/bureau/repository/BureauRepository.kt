package com.bukuwarung.edc.bureau.repository

import com.bukuwarung.edc.bureau.datasource.BureauDataSource
import com.bukuwarung.edc.bureau.model.request.BureauTrackEventRequest
import javax.inject.Inject

class BureauRepository @Inject constructor(private val bureauDataSource: BureauDataSource) {

    suspend fun trackAuthEvent(sessionId: String, request: BureauTrackEventRequest) =
        bureauDataSource.trackAuthEvent(sessionId, request)
}
