package com.bukuwarung.edc.bureau.di

import com.bukuwarung.edc.bureau.datasource.BureauDataSource
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton
import retrofit2.Retrofit

@Module
@InstallIn(SingletonComponent::class)
class BureauModule {

    @Singleton
    @Provides
    fun provideBureauDataSource(@Named("normal") retrofit: Retrofit): BureauDataSource =
        retrofit.create(BureauDataSource::class.java)
}
