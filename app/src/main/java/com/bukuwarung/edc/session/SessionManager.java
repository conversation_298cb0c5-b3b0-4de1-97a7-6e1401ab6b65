package com.bukuwarung.edc.session;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.SharedPreferences.Editor;

import com.bukuwarung.edc.global.EdcApplication;

public class SessionManager {

    private static SessionManager sessionManager;
    public SharedPreferences sessionPref;
    public SharedPreferences syncPref;
    private Context context;
    private Editor editor;
    private Editor syncEditor;

    public SessionManager(Context context) {
        this.context = context;
        this.sessionPref = context.getSharedPreferences("BookBookPref", 0);
        this.syncPref = context.getSharedPreferences("BukuWarungSyncPrefs", 0);
        this.editor = this.sessionPref.edit();
        this.syncEditor = this.syncPref.edit();
    }

    public static SessionManager getInstance() {
        if (sessionManager == null) {
            sessionManager = new SessionManager(EdcApplication.Companion.getInstance());
        }
        return sessionManager;
    }

    public static SessionManager getInstance(Context ctx) {
        if (sessionManager == null) {
            sessionManager = new SessionManager(ctx);
        }
        return sessionManager;
    }

    public boolean hasExistingBusiness() {
        return this.syncPref.getBoolean("temp_existing_business", false);
    }

    public void setExistingBusinessFlag(boolean flag) {
        this.syncEditor.putBoolean("temp_existing_business", flag);
        this.syncEditor.apply();
    }

    public String getUserIdForExistingBusinessCheck() {
        return this.syncPref.getString("EXISTING_BUSINESS_CHECK_USER", null);
    }

    public void setUserIdForExistingBusinessCheck(String str) {
        this.syncEditor.putString("EXISTING_BUSINESS_CHECK_USER", str);
        this.syncEditor.apply();
    }

    public String getPaymentAccountIdForExistingBusinessCheck() {
        return this.syncPref.getString("EXISTING_BUSINESS_CHECK_PAYMENT_ACCOUNT_ID", null);
    }

    public void setPaymentAccountIdForExistingBusinessCheck(String str) {
        this.syncEditor.putString("EXISTING_BUSINESS_CHECK_PAYMENT_ACCOUNT_ID", str);
        this.syncEditor.apply();
    }

}
