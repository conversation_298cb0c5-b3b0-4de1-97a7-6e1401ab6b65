package com.bukuwarung.edc.app.config

import android.util.Size
import com.bukuwarung.edc.R

/**
 * Configuration for Buku variant
 * This represents the standard BukuAgen flow with yellow branding and standard behavior
 */
class BukuVariantConfig : VariantConfig {
    override val printerHeaderSize: Size = Size(384, 150)

    // Core variant identifier
    override val variantIdentifier: String = "BUKUAGEN"

    // Business logic properties
    override val appFlow: String = "bukuAgen"

    // Specific API and business logic properties (replacing orderFlowType)
    override val isPartnershipForOrderHistory: Boolean = false
    override val isPartnershipForAnalytics: Boolean = false
    override val isPartnershipForKycRedirection: Boolean = false
    override val isPartnershipForBluetoothBridge: Boolean = false
    override val settingsApiOrderFlowType: String = "NON_PARTNERSHIP"
    override val isPartnershipForTransactionHistory: Boolean = false

    // Communication preferences
    override val defaultCommunicationChannel: String = "SMS"

    // UI/UX properties
    override val shouldShowHelp: Boolean = true
    override val shouldShowFooterLogo: Boolean = true
    override val shouldShowSwitchOtpChannelButton: Boolean = true
    override val primaryColorRes: Int = R.color.yellow_60
    override val cardErrorDialogColorRes: Int = R.color.yellow_60
    override val logoRes: Int = R.drawable.buku_agent_print
    override val receiptLogoRes: Int = R.drawable.buku_agent_print
    override val receiptHeaderText: String = "BUKUAGEN"
    override val shouldShowBusinessAddress: Boolean = true

    // HomePageActivity specific
    override val appDisplayName: String = "BukuAgen"
    override val shouldShowLogo: Boolean = true // Show ivLogo and vwSeparator
    override val homeShouldPerformAccountSetup: Boolean = false
    override val homeShouldCreateSaldoAccount: Boolean = false
    override val homeShouldUseDynamicBusinessTitle: Boolean = false
    override val homeShouldHandlePartnerOrderDetails: Boolean = false
    override val homeShouldUsePartnerSchemaFallback: Boolean = false
    override val homeShouldShowPartnerActivationMessage: Boolean = false
    override val homeShouldShowActivationBottomSheet: Boolean = false
    override val shouldShowSettingsButton: Boolean = true
    override val isUsingMiniatmProTickerStyling: Boolean = false

    // Receipt/Transaction specific
    override val shouldApplyGradientBackground: Boolean = false
    override val shouldShowTransactionFooter: Boolean = true
    override val shouldAddWatermark: Boolean = true
    override val shouldShowReceiptBranding: Boolean = true
    override val shouldUsePartnerPendingMessagesForTransaction: Boolean = false

    // Settings specific
    override val shouldHideChangePinOption: Boolean = false
    override val shouldHighlightDeviceStatusInSettings: Boolean = false
    override val shouldCheckPinLength: Boolean = true
    override val shouldPerformUserOnboarding: Boolean = true
    override val shouldShowTickerFragment: Boolean = false
    override val shouldHideToolbarMenu: Boolean = false
    override val shouldShowKomisiAgenDialog: Boolean = true
    override val shouldShowDateFilterInHistory: Boolean = false
    override val shouldShowWarrantyConfig: Boolean = true

    // External service configs
    override val apiUrlSuffix: String = ""
    override val tncUrl: String = "https://bukuwarung.co.id/terms-and-conditions/"
    override val privacyPolicyUrl: String = "https://bukuwarung.co.id/privacy-policy/"

    // Device/activation behavior
    override val shouldShowSakuDeviceMessage: Boolean = true
    override val shouldRestrictNonSakuDevices: Boolean = true
    override val shouldShowDeviceActivationBS: Boolean = false

    // Money transfer & payments
    override val shouldShowContactCustomerCare: Boolean = false
    override val shouldShowTransferMoneyHelpButton: Boolean = true
    override val shouldShowBankAccountHelpButton: Boolean = true

    // Analytics & tracking
    override val analyticsAppFlow: String = "bukuAgen"
    override val analyticsCommunicationChannel: String = "SMS"
    override val analyticsOrderChannel: String = "BUKUAGEN"

    // Order & activation flow
    override val shouldShowOrderValue: Boolean = true
    override val shouldShowOrderWarning: Boolean = true
    override val shouldShowActivationStepFour: Boolean = true
    override val orderCompletionBehavior: String = "NON_PARTNERSHIP"

    // External pinpad
    override val shouldShowExternalPinpadHelpButton: Boolean = true

    // Bank account colors
    override val bankAccountColorRes: Int = R.color.yellow_60
    override val bankAccountPrimaryColor: Int = R.color.yellow_60

    // Order details screen specific
    override val shouldHideOrderHelpButton: Boolean = false
    override val shouldHideGradientBackground: Boolean = false
    override val shouldShowOrderBillingDetails: Boolean = true
    override val edcOrderDetailsVariantType: String = "NON_PARTNERSHIP"
    override val orderKycRedirectionUrl: String = "miniAtmEdcOrderKyc"
    override val orderKybRedirectionUrl: String = "accountVerificationUrl"
    override val shouldShowStepThree: Boolean = true
    override val stepFourNumber: String = "4"
    override val shouldShowRefundForRejectedOrders: Boolean = true
    override val shouldShowRefundHelpButton: Boolean = true
    override val shouldActivateViaSaku: Boolean = false
    override val shouldUseAndroidActivation: Boolean = true

    // SplashActivity specific
    override val shouldRedirectToLogin: Boolean = false // Buku goes to WelcomeActivity

    // CardTransactionHistoryActivity specific
    override val shouldShowWarrantyNudge: Boolean = true
    override val shouldShowDeviceInfo: Boolean = false
    override val shouldShowBuyEdcButton: Boolean = false
    override val shouldHideFiltersForOrders: Boolean = false
    override val historyScreenTitle: String = "EDC order history"

    // Single-responsibility properties for remaining files
    override val shouldShowMoneyTransferHelpButton: Boolean = true
    override val transactionHistoryDeviceLabel: Int = R.string.bukuwarung_order_type
    override val shouldUseKycFlow: Boolean = false
    override val usesMiniatmProHomepageSchema: Boolean = false
    override val shouldHideSettlementMenu: Boolean = false
    override val shouldReactivateTerminal: Boolean = false

    // DeeplinkHandlerActivity and DeeplinkHandlerViewModel specific
    override val shouldWaitForOpsOnDeeplink: Boolean = false
    override val shouldCheckOrderStatusOnDeeplink: Boolean =
        false // Buku doesn't check order status

    // Payment related components
    override val orderInvoiceLogo: Int = R.drawable.buku_agent_print
    override val shouldShowOrderInvoiceFooter: Boolean = false
    override val shouldShowEducationInSelectBank: Boolean = true
    override val shouldShowBankAccountEducation: Boolean = true
    override val cardErrorContactText: Int = R.string.contact_customer_care
    override val cardErrorButtonColor: Int = R.color.yellow_60
    override val cardErrorTextColor: Int = R.color.black_80

    // Receipt printer components
    override val usesMiniAtmReceiptLogic: Boolean = false // Buku uses standard receipt logic
    override val shouldShowBersamaFooterInReceipt: Boolean = false
    override val receiptBusinessAddress: String = "business_address_buku"

    // UI components
    override val tickerFragmentName: String = "ticker_fragment_edc" // Direct ticker fragment name
    override val shouldShowPromoCodeInHomeTile: Boolean = true

    // Settings and profile
    override val isPartnershipForProfile: Boolean = false // Buku requires business name validation
    override val profileAnalyticsFlow: String = "NON_PARTNERSHIP"

    // Account and settlement
    override val shouldHideSettlementToolbar: Boolean = false
    override val shouldShowAccountEducation: Boolean = true
    override val shouldShowSettlementMenu: Boolean = true

    // Card operations
    override val shouldShowCardErrorEducation: Boolean = true

    // Filter and history
    override val shouldUseDefaultVariantForHistoryFilter: Boolean = false

    // Authentication and verification
    override val verifyOtpAnalyticsFlow: String = "NON_PARTNERSHIP"
    override val shouldCheckOnboardingOnLogin: Boolean = true

    // Card activation
    override val cardActivationType: String = "NON_PARTNERSHIP"
}