package com.bukuwarung.edc.order.orderhistory.enums

enum class KybStatus {
    INITIAL,
    RESUBMISSION,
    PENDING_VERIFICATION,
    PENDING_MANUAL_VERIFICATION,
    MANUALLY_VERIFIED,
    VERIFIED,
    REJECTED,
    MANUALLY_REJECTED
}

fun KybStatus?.isVerified() = (this == KybStatus.MANUALLY_VERIFIED || this == KybStatus.VERIFIED)

fun KybStatus.isNotVerified() = !this.isVerified()

fun KybStatus?.isPending() =
    (this == KybStatus.PENDING_MANUAL_VERIFICATION || this == KybStatus.PENDING_VERIFICATION)

fun String?.isPending() = (
    this == KybStatus.PENDING_VERIFICATION.name ||
        this == KybStatus.PENDING_MANUAL_VERIFICATION.name ||
        this == KybStatus.RESUBMISSION.name
    )

fun String?.isRejected() =
    (this == KybStatus.REJECTED.name || this == KybStatus.MANUALLY_REJECTED.name)

fun KybStatus?.isInitial() = (this == KybStatus.INITIAL || this == null)
