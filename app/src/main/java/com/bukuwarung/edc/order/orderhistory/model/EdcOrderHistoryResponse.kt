package com.bukuwarung.edc.order.orderhistory.model

import androidx.annotation.Keep
import com.bukuwarung.edc.card.cardhistory.model.HistoryItem
import com.bukuwarung.edc.card.cardhistory.model.PaginationDetails
import com.google.gson.annotations.SerializedName

@Keep
data class EdcOrderHistoryResponse(
    val result: Bo<PERSON>an,
    @field:SerializedName("data")
    val edcOrderData: EdcOrderData

)

data class EdcOrderData(
    val phone: String,
    @field:SerializedName("order_history")
    val history: ArrayList<HistoryItem> = arrayListOf(),
    val paginationDetails: PaginationDetails? = null
)
