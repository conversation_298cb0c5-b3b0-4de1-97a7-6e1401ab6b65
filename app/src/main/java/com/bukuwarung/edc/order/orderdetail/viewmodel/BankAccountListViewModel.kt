package com.bukuwarung.edc.order.orderdetail.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.liveData
import androidx.lifecycle.viewModelScope
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.payments.data.model.BankAccount
import com.bukuwarung.edc.payments.usecase.PaymentUseCase
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.errorMessage
import javax.inject.Inject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject

class BankAccountListViewModel @Inject constructor(private val paymentUseCase: PaymentUseCase) :
    ViewModel() {

    sealed class Event {
        data class ShowBankList(val list: List<BankAccount>) : Event()
        object HasOngoingTransaction : Event()
        data class ApiError(val message: String?) : Event()
        data class ReturnSelectedAccount(val currentSelectedAccount: BankAccount) : Event()
        data class OnBackPressed(
            val hasDeletedAnAccount: Boolean,
            val currentSelectedAccount: BankAccount?
        ) : Event()
    }

    sealed class ProfileIncompleteEvent {
        object ShowProfileDialog : ProfileIncompleteEvent()
    }

    private val eventStatus = MutableLiveData<Event>()
    val observeEvent: LiveData<Event> = eventStatus
    val profileIncompleteEvent: MutableLiveData<ProfileIncompleteEvent> = MutableLiveData()
    private var bookId: String = Utils.getPaymentAccountId()
    private var entryPoint = ""
    private var deletedAccount = BankAccount()
    private var currentSelectedAccount: BankAccount? = null
    private var hasDeletedAnAccount = false
    private var bankAccounts = listOf<BankAccount>()
    private lateinit var liveDataBankAccount: LiveData<List<BankAccount>>
    var mediatorLiveData: MediatorLiveData<List<BankAccount>> = MediatorLiveData()
    private var hasAddedSource = false

    private var customerId: String? = null
    private var paymentType: String = ""
    private var selectedBankCode: String? = null
    private var selectedAccountNumber: String? = null

    fun init(
        paymentType: String,
        entryPoint: String,
        cstId: String?,
        bookId: String?,
        selectedBankAccountId: String?,
        selectedBankCode: String? = null,
        selectedAccountNumber: String? = null
    ) {
        this.entryPoint = entryPoint
        this.paymentType = paymentType
        this.selectedBankCode = selectedBankCode
        this.selectedAccountNumber = selectedAccountNumber
        customerId = cstId
        if (bookId != null) this.bookId = bookId
        getBankAccounts(selectedBankAccountId)
    }

    fun setTempDeletedAccount(bankAccount: BankAccount) {
        deletedAccount = bankAccount
    }

    fun addNewBankAccount(bankAccount: BankAccount?) {
        bankAccount ?: return
        val newList = bankAccounts.toMutableList().apply { add(bankAccount) }.toList()
        bankAccounts = newList
        eventStatus.value = Event.ShowBankList(newList)
    }

    private fun handleBankAccounts(selectedBankAccountId: String?, list: List<BankAccount>?) =
        viewModelScope.launch {
            val newList = list ?: emptyList()
            val size = newList.size
            for (i in newList.indices) {
                if (selectedBankAccountId == newList[i].bankAccountId ||
                    (
                        selectedBankCode == newList[i].bankCode &&
                            selectedAccountNumber == newList[i].accountNumber
                        )
                ) {
                    newList[i].isSelected = 1
                    currentSelectedAccount = newList[i]
                }
            }
            bankAccounts = newList
            mediatorLiveData.value = newList
            Utils.setBankAccount = size > 0
        }

    private fun getBankAccounts(selectedBankAccountId: String?) = viewModelScope.launch {
        withContext(Dispatchers.IO) {
            bookId ?: return@withContext
            if (isPaymentIn()) {
                val response = paymentUseCase.getMerchantBankAccounts(bookId)
                liveDataBankAccount = liveData {
                    if (response.isSuccessful) {
                        emit(response.body() ?: emptyList())
                    } else {
                        emit(emptyList())
                    }
                }

                if (!hasAddedSource) {
                    withContext(Dispatchers.Main) {
                        mediatorLiveData.addSource(liveDataBankAccount) {
                            handleBankAccounts(selectedBankAccountId, it)
                        }
                        hasAddedSource = true
                    }
                }
                return@withContext
            }
            val result = paymentUseCase.getCustomerBankAccounts(bookId, customerId!!, null)
            if (result.isSuccessful) {
                handleBankAccounts(selectedBankAccountId, result.body())
            } else {
                setEventStatus(Event.ApiError(result.errorMessage()))
            }
        }
    }

    fun setCurrentSelectedAccount(bankAccount: BankAccount) {
        currentSelectedAccount = bankAccount
    }

    fun getCurrentSelectedAccount() {
        currentSelectedAccount?.run {
            eventStatus.value = Event.ReturnSelectedAccount(this)
        }
    }

    fun onBackPressed() {
        eventStatus.value = Event.OnBackPressed(hasDeletedAnAccount, currentSelectedAccount)
    }

    fun deleteBankAccount(bankAccount: BankAccount = deletedAccount) = viewModelScope.launch {
        bankAccount.bankAccountId ?: return@launch
        withContext(Dispatchers.IO) {
            val response =
                paymentUseCase.deleteMerchantBankAccount(bookId, bankAccount.bankAccountId)

            if (response.isSuccessful) {
                // request succeed
//                AppAnalytics.trackEvent(if (isPaymentIn() || isForQris()) AnalyticsConst.EVENT_PAYMENT_DELETE_USER_BANK else AnalyticsConst.EVENT_PAYMENT_DELETE_RECIPIENT_BANK,
//                        AppAnalytics.PropBuilder()
//                                .put(AnalyticsConst.ENTRY_POINT2, entryPoint)
//                                .put(if (isPaymentIn() || isForQris()) "delete_user_bank" else "deleted_recipient_bank", bankAccount.bankCode))
                val currentList = bankAccounts.toMutableList()
                currentList.firstOrNull { it.bankAccountId == bankAccount.bankAccountId }?.let {
                    currentList.remove(it)
                }
                bankAccounts = currentList
                hasDeletedAnAccount = true
                if (currentSelectedAccount == null ||
                    currentSelectedAccount?.bankAccountId == bankAccount.bankAccountId
                ) {
                    currentSelectedAccount =
                        if (currentList.isNotEmpty()) currentList.first() else null
                    currentSelectedAccount?.isSelected = 1
                }
                setEventStatus(Event.ShowBankList(currentList))
                if (isPaymentIn() && currentSelectedAccount == null) {
                    Utils.setBankAccount = false
                }
            } else {
                val json = JSONObject(response.errorBody()?.string())
                var errorCode = ""
                if (json.has("code")) {
                    errorCode = json.getString("code")
                }
                var errorMessage = ""
                if (json.has("message")) {
                    errorMessage = json.getString("message")
                }
                if (errorCode == "422") {
                    setEventStatus(Event.HasOngoingTransaction)
                } else {
                    setEventStatus(Event.ApiError(errorMessage))
                }
            }
        }
    }

    private suspend fun setEventStatus(event: Event) = withContext(Dispatchers.Main) {
        eventStatus.value = event
    }

    fun isPaymentIn(): Boolean = paymentType == PaymentConst.TYPE_PAYMENT_IN
}
