package com.bukuwarung.activities.edc.orderdetail.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.bukuwarung.edc.databinding.EdcCancelOrderBsBinding
import com.bukuwarung.edc.global.base.BaseBottomSheetDialogFragment
import com.bukuwarung.edc.util.singleClick

class EdcCancelOrderBS(val clickAction: () -> Unit) : BaseBottomSheetDialogFragment() {

    companion object {
        const val TAG = "EdcCancelOrderBS"
    }

    private var _binding: EdcCancelOrderBsBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = EdcCancelOrderBsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        with(binding) {
            btnDismiss.singleClick { dismiss() }
            btnCancel.singleClick {
                dismiss()
                clickAction()
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
