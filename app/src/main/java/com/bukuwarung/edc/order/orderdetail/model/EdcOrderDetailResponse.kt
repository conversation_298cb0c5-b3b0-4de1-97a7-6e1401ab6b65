package com.bukuwarung.edc.order.orderdetail.model

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class EdcOrderDetailResponse(

    @field:SerializedName("result")
    val result: Boolean? = null,

    @field:SerializedName("data")
    val data: OrderData? = null
)

@Keep
data class BankDetails(

    @field:SerializedName("beneficiary_name")
    val beneficiaryName: String? = null,

    @field:SerializedName("bank_name")
    val bankName: String? = null,

    @field:SerializedName("bank_name_same_as_ktp_name")
    val bankNameSameAsKtpName: String? = null,

    @field:SerializedName("bank_account")
    val bankAccount: String? = null
)

@Keep
data class Address(

    @field:SerializedName("delivery_address")
    val deliveryAddress: DeliveryAddress? = null,

    @field:SerializedName("store_address")
    val storeAddress: StoreAddress? = null
)

@Keep
data class Attachments(

    @field:SerializedName("KTP")
    val kTP: String? = null,

    @field:SerializedName("STORE")
    val sTORE: String? = null
)

@Keep
data class Checks(

    @field:SerializedName("kyc_done")
    val kycDone: Boolean? = null,

    @field:SerializedName("kyb_done")
    val kybDone: Boolean? = null,

    @field:SerializedName("bank_account_added")
    val bankAccountAdded: Boolean? = null,

    @field:SerializedName("completion_expire_date")
    val completionExpireDate: String? = null,

    @SerializedName("kyc_status")
    val kycStatus: String? = null,

    @SerializedName("kyb_status")
    val kybStatus: String? = null
)

@Keep
data class PaymentDetails(

    @field:SerializedName("expired_at")
    val expiredAt: String? = null,

    @field:SerializedName("payment_link")
    val paymentLink: String? = null,

    @field:SerializedName("payment_method")
    val paymentMethod: String? = null,

    @field:SerializedName("status")
    val status: String? = null,

    @field:SerializedName("payment_date")
    val paymentDate: String? = null
)

@Keep
data class OrderAmountDetails(

    @SerializedName("product_price")
    val productPrice: Double? = null,
    @SerializedName("discount")
    val discount: Map<String, Double>? = null,
    @field:SerializedName("total_payable_amount")
    val totalPayableAmount: Double? = null
)

@Keep
data class DeviceMappingDetails(
    @SerializedName("type")
    val type: String? = null,
    @SerializedName("serial_number")
    val serialNumber: String? = null,
    @SerializedName("referral_code")
    val referralCode: String? = null,
    @SerializedName("phone_number")
    val phoneNumber: String? = null,
    @SerializedName("tiktok_order_id")
    val tiktokOrderId: String? = null,
    @SerializedName("vendor")
    val vendor: String? = null,
    @SerializedName("description")
    val description: String? = null
)

@Keep
data class DeliveryDetails(
    @SerializedName("awb")
    val awb: String? = null
)

@Keep
data class OrderData(
    @SerializedName("device_mapping_details")
    val deviceMappingDetails: DeviceMappingDetails? = null,

    @field:SerializedName("attachments")
    val attachments: Attachments? = null,

    @field:SerializedName("address")
    val address: Address? = null,

    @field:SerializedName("referral_details")
    val referralDetails: ReferralDetails? = null,

    @field:SerializedName("payment_details")
    val paymentDetails: PaymentDetails? = null,

    @field:SerializedName("bank_details")
    val bankDetails: BankDetails? = null,

    @field:SerializedName("beneficiary_account_details")
    val beneficiaryAccountDetails: BeneficiaryAccountDetails? = null,

    @field:SerializedName("coordinates")
    val coordinates: String? = null,

    @field:SerializedName("created_at")
    val createdAt: String? = null,

    @field:SerializedName("order_amount_details")
    val orderAmountDetails: OrderAmountDetails? = null,

    @SerializedName("delivery_details")
    val deliveryDetails: DeliveryDetails? = null,

    @field:SerializedName("user_acknowledgement")
    val userAcknowledgement: Boolean? = null,

    @field:SerializedName("cart_id")
    val cartId: String? = null,

    @field:SerializedName("checks")
    val checks: Checks? = null,

    @field:SerializedName("phone")
    val phone: String? = null,

    @field:SerializedName("janus_account_id")
    val janusAccountId: String? = null,

    @field:SerializedName("business_type")
    val businessType: String? = null,

    @field:SerializedName("name")
    val name: String? = null,

    @field:SerializedName("kyc_tier")
    val kycTier: String? = null,

    @field:SerializedName("order_id")
    val orderId: String? = null,

    @SerializedName("invoice_number")
    val invoiceNumber: String? = null,

    @field:SerializedName("tfcode_validated")
    val tfcodeValidated: Boolean? = null,

    @field:SerializedName("status")
    val status: String? = null,

    @SerializedName("client_name")
    val clientName: String? = null
)

@Keep
data class ReferralDetails(

    @field:SerializedName("ecom_user_id")
    val ecomUserId: String? = null,

    @field:SerializedName("purchase_referral")
    val purchaseReferral: String? = null,

    @field:SerializedName("referee_name")
    val refereeName: String? = null,

    @field:SerializedName("external_order_id")
    val externalOrderId: String? = null,

    @field:SerializedName("referee_phone")
    val refereePhone: String? = null
)

@Keep
data class StoreAddress(

    @field:SerializedName("address")
    val address: String? = null,

    @field:SerializedName("province")
    val province: String? = null,

    @field:SerializedName("city")
    val city: String? = null,

    @field:SerializedName("district")
    val district: String? = null,

    @field:SerializedName("ward")
    val ward: String? = null,

    @field:SerializedName("postal_code")
    val postalCode: String? = null
)

@Keep
data class BeneficiaryAccountDetails(

    @field:SerializedName("bank_code")
    val bankCode: String? = null,

    @field:SerializedName("beneficiary_name")
    val beneficiaryName: String? = null,

    @field:SerializedName("bank_account")
    val accountNumber: String? = null,

    @field:SerializedName("bank_name")
    var bankName: String? = null,

    @SerializedName("logo_url")
    var logoUrl: String? = null
)

@Keep
data class DeliveryAddress(

    @field:SerializedName("delivery_address_same_as_store_address")
    val deliveryAddressSameAsStoreAddress: Boolean? = null,

    @field:SerializedName("address")
    val address: String? = null,

    @field:SerializedName("province")
    val province: String? = null,

    @field:SerializedName("city")
    val city: String? = null,

    @field:SerializedName("district")
    val district: String? = null,

    @field:SerializedName("ward")
    val ward: String? = null,

    @field:SerializedName("postal_code")
    val postalCode: String? = null
)
