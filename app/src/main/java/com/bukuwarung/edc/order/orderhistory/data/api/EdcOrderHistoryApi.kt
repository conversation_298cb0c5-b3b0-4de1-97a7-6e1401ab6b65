package com.bukuwarung.edc.order.orderhistory.data.api

import com.bukuwarung.edc.order.orderhistory.model.EdcOrderHistoryResponse
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Query

interface EdcOrderHistoryApi {

    @GET("ac/api/v2/edc/order/history")
    suspend fun getEdcOrderHistory(
        @Query("plan_type") type: String?,
        @Query("page_number") pageNumber: Int,
        @Query("page_size") pageSize: Int,
        @Query("sort") order: String?,
        @Query("start_date") startDate: String?,
        @Query("end_date") endDate: String?,
        @Query("order_flow_type") orderFlowType: String?
    ): Response<EdcOrderHistoryResponse>
}
