package com.bukuwarung.edc.order.orderdetail.repo

import com.bukuwarung.edc.order.orderdetail.api.EdcOrderDetailApi
import com.bukuwarung.edc.order.orderdetail.model.EdcOrderDetailResponse
import com.bukuwarung.edc.order.orderdetail.model.EdcProductDetails
import com.bukuwarung.edc.order.orderdetail.model.OrderCancelResponse
import com.bukuwarung.edc.order.orderdetail.model.RefundRequest
import com.bukuwarung.edc.order.orderdetail.model.RefundResponse
import com.bukuwarung.edc.order.orderdetail.model.RegeneratePaymentLinkResponse
import com.bukuwarung.network.utils.ResourceState
import com.bukuwarung.network.utils.safeApiCall
import javax.inject.Inject

class EdcOrderDetailRepo @Inject constructor(private val edcOrderDetailApi: EdcOrderDetailApi) {

    suspend fun getEdcOrderDetailByPhoneNumber(): ResourceState<EdcOrderDetailResponse> =
        safeApiCall {
            edcOrderDetailApi.getEdcOrderDetailByPhoneNumber()
        }

    suspend fun getEdcOrderDetailsByOrderId(
        orderId: String
    ): ResourceState<EdcOrderDetailResponse> = safeApiCall {
        edcOrderDetailApi.getEdcOrderDetailByOrderId(orderId)
    }

    suspend fun getEdcProductDetails(orderId: String): ResourceState<EdcProductDetails> =
        safeApiCall {
            edcOrderDetailApi.getEdcProductDetails(orderId)
        }

    suspend fun cancelEdcOrder(orderId: String): ResourceState<OrderCancelResponse> = safeApiCall {
        edcOrderDetailApi.cancelEdcOrder(orderId)
    }

    suspend fun regeneratePaymentLink(
        orderId: String
    ): ResourceState<RegeneratePaymentLinkResponse> = safeApiCall {
        edcOrderDetailApi.regeneratePaymentLink(orderId)
    }

    suspend fun refundPayment(
        orderId: String,
        refundRequest: RefundRequest
    ): ResourceState<RefundResponse> = safeApiCall {
        edcOrderDetailApi.refundPayment(orderId, refundRequest)
    }
}
