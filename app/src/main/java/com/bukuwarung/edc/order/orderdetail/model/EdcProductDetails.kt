package com.bukuwarung.edc.order.orderdetail.model

import androidx.annotation.Keep

@Keep
data class EdcProductDetails(val result: Boolean? = null, val data: EdcProductData? = null)

@Keep
data class EdcProductData(
    val cartId: String? = null,
    val orderId: String? = null,
    val products: List<Product?>? = null,
    val totalAmount: Double? = null,
    val createdAt: String? = null,
    val updatedAt: String? = null
)

@Keep
data class Product(
    val id: String? = null,
    val name: String? = null,
    val description: String? = null,
    val image: String? = null,
    val availableQuantity: Int? = null,
    val createdAt: String? = null,
    val updatedAt: String? = null,
    val plans: List<Plan?>? = null,
    val isActive: Boolean? = null
)

@Keep
data class Plan(
    val planId: String? = null,
    val planType: String? = null,
    val name: String? = null,
    val description: String? = null,
    val price: Double? = null,
    val discount: Double? = null,
    val margin: Double? = null,
    val createdAt: String? = null,
    val updatedAt: String? = null
)
