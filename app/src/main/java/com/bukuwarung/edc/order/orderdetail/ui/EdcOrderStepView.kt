package com.bukuwarung.edc.order.orderdetail.ui

import android.content.Context
import android.content.res.ColorStateList
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.core.content.res.getStringOrThrow
import com.bukuwarung.edc.R
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.showView

class EdcOrderStepView(context: Context, attributeSet: AttributeSet) :
    ConstraintLayout(context, attributeSet) {

    private val view =
        LayoutInflater.from(context).inflate(R.layout.invoice_steps, this, true)
    private val stepImage: AppCompatImageView = view.findViewById(R.id.ivStepImage)
    private val clItemInvoiceOnboarding: ConstraintLayout = view.findViewById(R.id.clInvoiceStep)
    private val stepTitle: AppCompatTextView = view.findViewById(R.id.tvStepTitle)
    private val stepNumber: AppCompatTextView = view.findViewById(R.id.tvStepNumber)
    private val ivDone: AppCompatImageView = view.findViewById(R.id.ivDone)

    init {
        context.theme.obtainStyledAttributes(
            attributeSet,
            R.styleable.InvoiceOnboardingItemView,
            0,
            0
        ).apply {
            try {
                if (getBoolean(R.styleable.InvoiceOnboardingItemView_isDone, false)) {
                    ivDone.showView()
                } else {
                    ivDone.hideView()
                }

                if (hasValue(R.styleable.InvoiceOnboardingItemView_background)) {
                    clItemInvoiceOnboarding.background = ContextCompat.getDrawable(
                        context,
                        getResourceId(
                            R.styleable.InvoiceOnboardingItemView_background,
                            R.color.white
                        )
                    )
                } else {
                    clItemInvoiceOnboarding.setBackgroundColor(
                        ResourcesCompat.getColor(
                            context.resources,
                            R.color.white,
                            null
                        )
                    )
                }
                if (hasValue(R.styleable.InvoiceOnboardingItemView_iconBackground)) {
                    stepImage.background =
                        ContextCompat.getDrawable(
                            context,
                            getResourceId(
                                R.styleable.InvoiceOnboardingItemView_iconBackground,
                                R.drawable.bg_solid_white_stroke_grey_border_4dp
                            )
                        )
                }
                if (hasValue(R.styleable.InvoiceOnboardingItemView_TextColor)) {
                    stepNumber.setTextColor(
                        ColorStateList.valueOf(
                            this.getColor(
                                R.styleable.InvoiceOnboardingItemView_TextColor,
                                resources.getColor(R.color.black)
                            )
                        )
                    )
                }
                if (hasValue(R.styleable.InvoiceOnboardingItemView_title)) {
                    stepTitle.text = getStringOrThrow(R.styleable.InvoiceOnboardingItemView_title)
                }
                if (hasValue(R.styleable.InvoiceOnboardingItemView_stepNumber)) {
                    stepNumber.text =
                        getStringOrThrow(R.styleable.InvoiceOnboardingItemView_stepNumber)
                }
            } finally {
                recycle()
            }
        }
    }

    fun setStepTitle(name: String) {
        stepTitle.text = name
    }

    fun setStepNumber(number: String) {
        stepNumber.text = number
    }

    fun setDisabledStep() {
        stepTitle.setTextColor(ContextCompat.getColor(context, R.color.black_10))
        stepImage.backgroundTintList = ContextCompat.getColorStateList(context, R.color.black_10)
        clItemInvoiceOnboarding.background =
            ContextCompat.getDrawable(context, R.drawable.bg_solid_white_corner_8dp_black5)
        ivDone.showView()
        ivDone.setImageResource(R.drawable.ic_arrow_circle_right_black10)
        stepNumber.setTextColor(ContextCompat.getColor(context, R.color.white))
    }

    fun setCurrentStep() {
        stepTitle.setTextColor(ContextCompat.getColor(context, R.color.colorPrimary))
        stepImage.background =
            ContextCompat.getDrawable(context, R.drawable.blue_drawable_round_20dp)
        clItemInvoiceOnboarding.background =
            ContextCompat.getDrawable(context, R.drawable.bg_solid_blue5_corner_8dp_black5)
        ivDone.showView()
        stepNumber.setTextColor(ContextCompat.getColor(context, R.color.white))
    }

    fun setDone() {
        stepNumber.hideView()
        stepImage.backgroundTintList = ContextCompat.getColorStateList(context, R.color.transparent)
        clItemInvoiceOnboarding.background =
            ContextCompat.getDrawable(context, R.drawable.bg_solid_white_corner_8dp_black5)
        stepTitle.setTextColor(ContextCompat.getColor(context, R.color.black_60))
        stepImage.setImageResource(com.bukuwarung.bluetooth_printer.R.drawable.ic_checklist_rounded)
        ivDone.hideView()
    }

    fun setPendingStep() {
        stepNumber.hideView()
        stepImage.backgroundTintList = ContextCompat.getColorStateList(context, R.color.transparent)
        clItemInvoiceOnboarding.background =
            ContextCompat.getDrawable(context, R.drawable.bg_solid_white_corner_8dp_black5)
        stepTitle.setTextColor(ContextCompat.getColor(context, R.color.black_60))
        stepImage.setImageResource(R.drawable.ic_clock_yellow)
        ivDone.hideView()
    }

    fun setRejectedStep() {
        stepNumber.hideView()
        stepImage.backgroundTintList = ContextCompat.getColorStateList(context, R.color.transparent)
        clItemInvoiceOnboarding.background =
            ContextCompat.getDrawable(context, R.drawable.bg_solid_red5_corner_8dp)
        stepTitle.setTextColor(ContextCompat.getColor(context, R.color.black_60))
        stepImage.setImageResource(R.drawable.ic_cross_filled_circle)
        ivDone.showView()
        ivDone.setImageResource(R.drawable.ic_red_arrow_circle_right)
    }
}
