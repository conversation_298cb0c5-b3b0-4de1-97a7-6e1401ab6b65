package com.bukuwarung.edc.onboarding.ui

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import androidx.appcompat.app.AppCompatActivity
import androidx.viewpager.widget.ViewPager
import androidx.viewpager2.widget.ViewPager2
import androidx.viewpager2.widget.ViewPager2.SCROLL_STATE_DRAGGING
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.ActivityWelcomeBinding
import com.bukuwarung.edc.login.ui.LoginActivity
import com.bukuwarung.edc.onboarding.WelcomeAdapter
import com.bukuwarung.edc.onboarding.data.model.WelcomeScreens
import com.bukuwarung.edc.util.RemoteConfigUtils
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.showView
import com.google.android.material.tabs.TabLayoutMediator
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken

class WelcomeActivity : AppCompatActivity() {

    lateinit var binding: ActivityWelcomeBinding
    private val thresholdOffset = 0.5f
    private var scrollStarted = false
    private var checkDirection: Boolean = false

    override fun onCreate(bundle: Bundle?) {
        super.onCreate(bundle)
        binding = ActivityWelcomeBinding.inflate(layoutInflater)
        setContentView(binding.root)

        val screens = RemoteConfigUtils.getWelcomeScreens()

        val gson: Gson = GsonBuilder().create()
        val jsonType = object : TypeToken<List<WelcomeScreens?>?>() {}.type
        val screenToDisplay: List<WelcomeScreens> = gson.fromJson(screens, jsonType)

        val images = arrayListOf(R.drawable.welcome1, R.drawable.welcome2, R.drawable.welcome3)
        val titles = arrayListOf(
            getString(R.string.title_1),
            getString(R.string.title_2),
            getString(
                R.string.title_3
            )
        )
        val subTitles = arrayListOf(
            getString(R.string.subtitle_1),
            getString(R.string.subtitle_2),
            getString(
                R.string.subtitle_3
            )
        )
        val welcomeAdapter = WelcomeAdapter(images, titles, subTitles, false)

        val handler = Handler()

        welcomeAdapter.setItem(screenToDisplay)
        binding.welcomeViewpager.adapter = welcomeAdapter

        binding.btnLogin.setOnClickListener {
            goToLogin()
        }

        binding.btnSkip.setOnClickListener {
            goToLogin()
        }

        binding.btnNext.setOnClickListener {
            if (getCurrentItem() < screenToDisplay.size) {
                binding.welcomeViewpager.currentItem = getCurrentItem() + 1
            }
        }

        binding.welcomeViewpager.registerOnPageChangeCallback(object :
            ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)

                val isLast = (position + 1 == welcomeAdapter.itemCount)

                if (isLast) {
                    showLogin()
                } else {
                    hideLogin()
                }
            }

            override fun onPageScrollStateChanged(state: Int) {
                super.onPageScrollStateChanged(state)
                /**
                 * The user swiped forward or back and we need to
                 * invalidate the previous handler.
                 */
                if (state == SCROLL_STATE_DRAGGING) {
                    handler.removeMessages(0)
                }
                if (!scrollStarted && state == ViewPager.SCROLL_STATE_DRAGGING) {
                    scrollStarted = true
                    checkDirection = true
                } else {
                    scrollStarted = false
                }
            }
        })

        TabLayoutMediator(binding.tabLayout, binding.welcomeViewpager) { tab, position ->
        }.attach()
    }

    private fun showLogin() {
        binding.btnNext.hideView()
        binding.btnSkip.hideView()
        binding.btnLogin.showView()
    }

    private fun hideLogin() {
        binding.btnNext.showView()
        binding.btnSkip.showView()
        binding.btnLogin.hideView()
    }

    private fun goToLogin() {
        val intent = Intent(this, LoginActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
        startActivity(intent)
        finish()
    }

    private fun getCurrentItem(): Int = binding.welcomeViewpager.currentItem
}
