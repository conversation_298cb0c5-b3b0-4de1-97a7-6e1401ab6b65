package com.bukuwarung.edc.onboarding

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.edc.databinding.WelcomeScreenItemBinding
import com.bukuwarung.edc.onboarding.data.model.WelcomeScreens
import com.bumptech.glide.Glide
import javax.inject.Inject

class WelcomeAdapter @Inject constructor(
    private val images: List<Int>,
    private val titles: List<String>,
    private val subTitles: List<String>,
    private val shouldShowNewLoginScreen: Boolean
) : RecyclerView.Adapter<WelcomeAdapter.WelcomeViewHolder>() {

    private var list: List<WelcomeScreens> = listOf()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): WelcomeViewHolder {
        val binding =
            WelcomeScreenItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return WelcomeViewHolder(binding)
    }

    override fun onBindViewHolder(holder: WelcomeViewHolder, position: Int) {
        holder.bind(
            position + 1 == images.size,
            list[position],
            images[position % images.size],
            titles[position % titles.size],
            subTitles[position % subTitles.size]
        )
    }

    fun setItem(list: List<WelcomeScreens>) {
        this.list = list
        notifyDataSetChanged()
    }

    override fun getItemCount(): Int = list.size

    inner class WelcomeViewHolder(private val binding: WelcomeScreenItemBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(
            isLast: Boolean,
            welcomeScreen: WelcomeScreens,
            imageResId: Int,
            title: String,
            subTitle: String
        ) {
            with(binding) {
                Glide.with(root.context.applicationContext)
                    .load(welcomeScreen.backGroundImage)
                    .placeholder(imageResId)
                    .centerInside()
                    .into(ivWelcome)
                tvTitle.text = title
                tvSubtitle.text = subTitle
            }
        }
    }
}
