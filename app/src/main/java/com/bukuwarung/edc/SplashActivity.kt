package com.bukuwarung.edc

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.bukuwarung.edc.homepage.ui.home.HomePageActivity
import com.bukuwarung.edc.login.ui.LoginActivity
import com.bukuwarung.edc.onboarding.ui.WelcomeActivity
import com.bukuwarung.edc.util.EncryptedPreferencesHelper
import com.bukuwarung.edc.util.openActivity
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import com.bukuwarung.edc.app.config.VariantConfig

@AndroidEntryPoint
class SplashActivity : AppCompatActivity() {

    @Inject
    lateinit var variantConfig: VariantConfig

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (EncryptedPreferencesHelper.get("uuid", "").isNotEmpty()) {
            openActivity(HomePageActivity::class.java) { intent.extras?.let { putAll(it) } }
        } else {
            if (variantConfig.shouldRedirectToLogin) {
                openActivity(LoginActivity::class.java)
            } else {
                openActivity(WelcomeActivity::class.java)
            }
        }
        finish()
    }
}
