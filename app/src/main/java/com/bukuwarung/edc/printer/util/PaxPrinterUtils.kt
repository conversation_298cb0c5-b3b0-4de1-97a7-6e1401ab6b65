package com.bukuwarung.edc.printer.util

import android.graphics.Bitmap
import android.util.Log
import com.bukuwarung.edc.global.EdcApplication
import com.pax.dal.IPrinter
import com.pax.dal.entity.EFontTypeAscii
import com.pax.dal.entity.EFontTypeExtCode
import com.pax.dal.exceptions.PrinterDevException

object PaxPrinterUtils {
    private val printer: IPrinter? by lazy {
        try {
            EdcApplication.dal?.printer
        } catch (e: Exception) {
            Log.e("PrinterUtils", "Error initializing printer", e)
            null
        }
    }

    private val printActions = mutableListOf<() -> Unit>()

    @Throws(PrinterDevException::class)
    fun commitPrintJob() {
        try {
            printer?.apply {
                init()
                printActions.forEach { it() }
                start()
            }
        } finally {
            printActions.clear()
        }
    }

    fun printMediumBigCenteredText(text: String) {
        printActions.add {
            printer?.apply {
                leftIndent(0)
                setGray(1)
                fontSet(EFontTypeAscii.FONT_16_32, EFontTypeExtCode.FONT_16_32)
                val lineWidth = 24
                val centeredText = getCenteredText(text, lineWidth)
                printStr(centeredText, null)
                step(10)
            }
        }
    }

    fun printMultiline(vararg lines: String) {
        printActions.add {
            printer?.apply {
                leftIndent(0)
                fontSet(EFontTypeAscii.FONT_16_24, EFontTypeExtCode.FONT_16_32)
                lines.forEach { printStr(it, null) }
                step(10)
            }
        }
    }

    fun printBitmapOptimized(bitmap: Bitmap, paperWidthPx: Int = 384) {
        printActions.add {
            printer?.apply {
                val cropped = cropWhiteSpace(bitmap)
                val leftMargin = ((paperWidthPx - cropped.width) / 2).coerceAtLeast(0)
                leftIndent(leftMargin)
                printBitmap(cropped)
                step(5)
            }
        }
    }

    private fun getCenteredText(text: String, lineWidth: Int): String {
        val padding = (lineWidth - text.length) / 2
        return " ".repeat(padding.coerceAtLeast(0)) + text
    }

    private fun cropWhiteSpace(bitmap: Bitmap): Bitmap {
        val width = bitmap.width
        val height = bitmap.height

        var top = 0
        var bottom = height - 1

        while (top < bottom && isRowWhite(bitmap, top)) top++
        while (bottom > top && isRowWhite(bitmap, bottom)) bottom--

        return Bitmap.createBitmap(bitmap, 0, top, width, bottom - top + 1)
    }

    private fun isRowWhite(bitmap: Bitmap, row: Int): Boolean {
        for (x in 0 until bitmap.width) {
            if (bitmap.getPixel(x, row) != -0x1) return false
        }
        return true
    }
}
