package com.bukuwarung.edc.printer

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.widget.Toast
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.global.EdcApplication
import com.bukuwarung.edc.printer.util.PrintUtil
import com.pax.dal.entity.EFontTypeAscii
import com.pax.dal.entity.EFontTypeExtCode

object EDCPrint {

    var doubleHeight = false
    var doubleWidth = false
    var invert = false
    var fontAscii = EFontTypeAscii.FONT_12_24
    var fontType = EFontTypeExtCode.FONT_16_16
    var leftIndent = 4
    var wordSpace = 1
    var lineSpace = 0
    var gray = 1
    var stepSize = 150
    var bitmap: Bitmap? = null
    var containBitmap = false
    var hasDottedLine = false

    fun startPrinting(printString: String, footer: String? = null) {
        kotlin.run {
            PrintUtil.printUtil.apply {
                init()

                if (containBitmap) {
                    val options = BitmapFactory.Options()
                    options.inScaled = false
                    if (bitmap?.height!! <= 0 && bitmap?.width!! <= 0) {
                        return
                    }
                    printBitmap(bitmap)
                }

                parseTextForPrinting(printString, footer)

                spaceSet(
                    wordSpace.toByte(),
                    lineSpace.toByte()
                )

                setGray(gray)

                if (doubleWidth) {
                    setDoubleWidth(doubleWidth, doubleWidth)
                }
                if (doubleHeight) {
                    setDoubleHeight(doubleHeight, doubleHeight)
                }
                setInvert(invert)

                step(stepSize)
            }
        }
    }

    private fun parseTextForPrinting(content: String, footer: String?) {
        val printableString = content.split("\n")
        var printableText = ""

        for (i in printableString) {
            printableText += i
            when (i) {
                "left_spacing_70" -> {
                    PrintUtil.printUtil.leftIndents(70.toShort())
                }

                "left_spacing_30" -> {
                    PrintUtil.printUtil.leftIndents(30.toShort())
                }

                "normal" -> {
                    fontAscii = EFontTypeAscii.FONT_12_24
                    fontType = EFontTypeExtCode.FONT_16_32
                    PrintUtil.printUtil.fontSet(fontAscii, fontType)
                }

                "bold" -> {
                    fontAscii = EFontTypeAscii.FONT_12_48
                    fontType = EFontTypeExtCode.FONT_32_16
                    PrintUtil.printUtil.fontSet(fontAscii, fontType)
                }

                "super" -> {
                    fontAscii = EFontTypeAscii.FONT_24_48
                    fontType = EFontTypeExtCode.FONT_48_24
                }

                else -> {
                    printableText = printableText.replace("super", "")
                    printableText = printableText.replace("bold", "")
                    printableText = printableText.replace("left_spacing_70", "")
                    printableText = printableText.replace("left_spacing_30", "")
                    printableText = printableText.replace("dotted_line", getDottedLine(30))
                    printableText = printableText.replace("normal", "")

                    PrintUtil.printUtil.printStr(printableText, null)
                    printableText = "\n"
                }
            }
        }

        if (footer.isNullOrEmpty()) {
            PrintUtil.printUtil.printStr(footer, null)
        }

        val status: String? = PrintUtil.printUtil.start()
        Toast.makeText(
            EdcApplication.instance,
            "Printing $status",
            Toast.LENGTH_LONG
        ).show()

        if (!content.contains("Test")) {
            val map = HashMap<String, String>()
            map["status"] = status.toString()
            Analytics.trackEvent("invoice_print_completed", map)
        }
    }

    fun getDottedLine(n: Int): String {
        var str = "\n"
        repeat(n) { str += "-" }
        return str
    }
}
