package com.bukuwarung.edc.printer.ui.dialog

import android.content.Context
import android.os.Bundle
import com.bukuwarung.edc.databinding.DialogBukuBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.global.base.BaseDialog
import com.bukuwarung.edc.global.enums.BaseDialogType
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.isNotNullOrBlank
import com.bukuwarung.edc.util.orNil
import com.bukuwarung.edc.util.singleClick
import com.google.firebase.crashlytics.FirebaseCrashlytics

class BukuDialog(
    context: Context,
    private val title: String?,
    private val subTitle: String?,
    private val image: Int?,
    private val isLoader: Boolean,
    private val btnLeftListener: () -> Unit,
    private val btnRightListener: () -> Unit,
    private val btnLeftText: String,
    private val btnRightText: String
) : BaseDialog(context, BaseDialogType.POPUP_ROUND_CORNERED) {

    override val resId: Int = 0

    private val binding by lazy {
        DialogBukuBinding.inflate(layoutInflater).also {
            setupViewBinding(it.root)
        }
    }

    init {
        setUseFullWidth(false)
        setCancellable(false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val minWidth = (context.resources.displayMetrics.widthPixels * 0.90).toInt()
        setMinWidth(minWidth)
        super.onCreate(savedInstanceState)

        with(binding) {
            ivImage.visibility = (image != null).asVisibility()
            ivImage.setImageResource(image.orNil)
            tvTitle.text = title
            tvTitle.visibility = title.isNotNullOrBlank().asVisibility()
            tvSubtitle.text = subTitle
            tvSubtitle.visibility = subTitle.isNotNullOrBlank().asVisibility()
            btnRight.visibility = btnRightText.isNotNullOrBlank().asVisibility()
            btnLeft.visibility = btnLeftText.isNotNullOrBlank().asVisibility()
            if (isLoader) {
                btnLeft.hideView()
                btnRight.hideView()
            }
            btnLeft.singleClick {
                dismiss()
                btnLeftListener()
            }
            btnLeft.text = btnLeftText

            btnRight.singleClick {
                dismiss()
                btnRightListener()
            }
            btnRight.text = btnRightText
        }
        trackGenericDialog()
    }

    private fun trackGenericDialog() {
        try {
            val props = HashMap<String, String>()
            props["title"] = title.orEmpty()
            props["subtitle"] = subTitle.orEmpty()
            Analytics.trackEvent("show_general_dialog", props)
        } catch (e: Exception) {
            FirebaseCrashlytics.getInstance().recordException(e)
        }
    }
}
