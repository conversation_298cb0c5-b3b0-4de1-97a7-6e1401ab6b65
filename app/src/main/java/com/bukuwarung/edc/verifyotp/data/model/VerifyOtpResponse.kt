package com.bukuwarung.edc.verifyotp.data.model

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class VerifyOtpResponse(
    @SerializedName("success") val success: Boolean?,
    @SerializedName("idToken") val idToken: String? = "",
    @SerializedName("message") val message: String? = "",
    @SerializedName("newUser") val newUser: Boolean? = false,
    @SerializedName("sessionToken") val sessionToken: String? = "",
    val responseCode: String? = ""
)
