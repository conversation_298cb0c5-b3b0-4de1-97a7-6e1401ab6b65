package com.bukuwarung.edc.verifyotp.usecase

import com.bukuwarung.edc.verifyotp.data.model.VerifyOtpRequest
import com.bukuwarung.edc.verifyotp.data.repository.VerifyOtpRepository
import javax.inject.Inject

class VerifyOtpUseCase @Inject constructor(private val verifyOtpRepository: VerifyOtpRepository) {

    suspend fun verifyOtp(request: VerifyOtpRequest) = verifyOtpRepository.verifyOtp(request)

    suspend fun verifyGenericOtp(opsToken: String, request: VerifyOtpRequest) =
        verifyOtpRepository.verifyGenericOtp(opsToken, request)
}
