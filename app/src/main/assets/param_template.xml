<?xml version="1.0" encoding="UTF-8"?>
<Schema>
    <Groups>
        <Group>
            <ID>merchant</ID>
            <Title>Merchant</Title>
            <Description>merchant params</Description>
        </Group>
        <Group>
            <ID>terminal</ID>
            <Title>Terminal</Title>
            <Description>terminal params</Description>
        </Group>
        <Group>
            <ID>settings</ID>
            <Title>Settings</Title>
            <Description>terminal settings</Description>
        </Group>
    </Groups>
    <Files>
        <File>
            <ID>sys_F1</ID>
            <FileName>sys_cap.p</FileName>
            <FileType>xml</FileType>
        </File>
    </Files>
    <Parameters>
        <Header>
            <Title>Merchant</Title>
            <Display>true</Display>
            <DisplayStyle>common</DisplayStyle>
            <DefaultStyle>open</DefaultStyle>
        </Header>
        <Parameter>
            <Title>Merchant id</Title>
            <PID>TP-Merchant-merchantId</PID>
            <Defaultvalue>718039455370001</Defaultvalue>
            <Type>single</Type>
            <InputType>text</InputType>
            <DataType>String</DataType>
            <Display>true</Display>
            <GroupID>merchant</GroupID>
            <FileID>sys_F1</FileID>
            <Required>true</Required>
            <Readonly>false</Readonly>
        </Parameter>
        <Parameter>
            <Title>Merchant address</Title>
            <PID>TP-Merchant-merchantAddress</PID>
            <Defaultvalue></Defaultvalue>
            <Type>single</Type>
            <InputType>text</InputType>
            <DataType>String</DataType>
            <Display>true</Display>
            <GroupID>merchant</GroupID>
            <FileID>sys_F1</FileID>
            <Required>false</Required>
            <Readonly>false</Readonly>
        </Parameter>
        <Parameter>
            <Title>Merchant Phone Number</Title>
            <PID>TP-Merchant-phoneNumber</PID>
            <Defaultvalue></Defaultvalue>
            <Type>single</Type>
            <InputType>text</InputType>
            <DataType>String</DataType>
            <Display>true</Display>
            <GroupID>merchant</GroupID>
            <FileID>sys_F1</FileID>
            <Required>false</Required>
            <Readonly>false</Readonly>
        </Parameter>
        <Header>
            <Title>Terminal</Title>
            <Display>true</Display>
            <DisplayStyle>common</DisplayStyle>
            <DefaultStyle>open</DefaultStyle>
        </Header>
        <Parameter>
            <Title>Terminal id</Title>
            <PID>TP-Terminal-terminalId</PID>
            <Defaultvalue></Defaultvalue>
            <Type>single</Type>
            <InputType>text</InputType>
            <DataType>String</DataType>
            <Display>true</Display>
            <GroupID>terminal</GroupID>
            <FileID>sys_F1</FileID>
            <Required>true</Required>
            <Readonly>false</Readonly>
        </Parameter>
        <Parameter>
            <Title>Terminal TMK</Title>
            <PID>TP-Terminal-TMK</PID>
            <Defaultvalue></Defaultvalue>
            <Type>single</Type>
            <InputType>text</InputType>
            <DataType>String</DataType>
            <Display>true</Display>
            <GroupID>terminal</GroupID>
            <FileID>sys_F1</FileID>
            <Required>true</Required>
            <Readonly>false</Readonly>
        </Parameter>
        <Header>
            <Title>Settings</Title>
            <Display>true</Display>
            <DisplayStyle>common</DisplayStyle>
            <DefaultStyle>open</DefaultStyle>
        </Header>
        <Parameter>
            <Title>Mag Card Enabled</Title>
            <PID>TP-Settings-isMagCardEnabled</PID>
            <Defaultvalue>true</Defaultvalue>
            <Type>single</Type>
            <InputType>text</InputType>
            <DataType>String</DataType>
            <Display>true</Display>
            <GroupID>settings</GroupID>
            <FileID>sys_F1</FileID>
            <Required>true</Required>
            <Readonly>false</Readonly>
        </Parameter>
        <Parameter>
            <Title>Mag Card Valid Prefix</Title>
            <PID>TP-Settings-magCardPrefix</PID>
            <Defaultvalue>194690,60329898,60349493,60130167,60130177</Defaultvalue>
            <Type>single</Type>
            <InputType>text</InputType>
            <DataType>String</DataType>
            <Display>true</Display>
            <GroupID>settings</GroupID>
            <FileID>sys_F1</FileID>
            <Required>true</Required>
            <Readonly>false</Readonly>
        </Parameter>
        <Parameter>
            <Title>Application ID</Title>
            <PID>TP-Settings-applicationId</PID>
            <Defaultvalue></Defaultvalue>
            <Type>single</Type>
            <InputType>text</InputType>
            <DataType>String</DataType>
            <Display>true</Display>
            <GroupID>settings</GroupID>
            <FileID>sys_F1</FileID>
            <Required>false</Required>
            <Readonly>false</Readonly>
        </Parameter>
    </Parameters>
</Schema>