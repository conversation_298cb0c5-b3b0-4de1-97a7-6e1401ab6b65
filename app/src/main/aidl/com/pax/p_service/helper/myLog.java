package com.pax.p_service.helper;

import android.util.Log;

import com.bukuwarung.edc.BuildConfig;


public class myLog {
    public static void logI(String TAG, String string) {
        if (BuildConfig.DEBUG) {
            Log.i(TAG, string);
        }
    }

    public static void logI(String TAG, String string, Exception e) {
        if (BuildConfig.DEBUG) {
            Log.i(TAG, string, e);
        }
    }

    public static void logD(String TAG, String string) {
        if (BuildConfig.DEBUG) {
            Log.d(TAG, string);
        }
    }

    public static void logE(String TAG, String string) {
        if (BuildConfig.DEBUG) {
            Log.e(TAG, string);
        }
    }

    public static void logE(String TAG, String string, Exception e) {
        if (BuildConfig.DEBUG) {
            Log.e(TAG, string, e);
        }
    }

    public static void logV(String TAG, String string) {
        if (BuildConfig.DEBUG) {
            Log.v(TAG, string);
        }
    }

    public static void logW(String TAG, String string) {
        if (BuildConfig.DEBUG) {
            Log.w(TAG, string);
        }
    }

    public static void logW(String TAG, Exception e) {
        if (BuildConfig.DEBUG) {
            Log.w(TAG, e);
        }
    }

    public static void logW(String TAG, String string, Exception e) {
        if (BuildConfig.DEBUG) {
            Log.w(TAG, string, e);
        }
    }

    public static void logWTF(String TAG, String string) {
        if (BuildConfig.DEBUG) {
            Log.wtf(TAG, string);
        }
    }
}
