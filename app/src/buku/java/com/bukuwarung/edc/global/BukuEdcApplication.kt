package com.bukuwarung.edc.global

import android.app.Application
import com.bukuwarung.edc.BuildConfig
import com.bukuwarung.edc.app.config.AppConfig
import com.bukuwarung.edc.app.config.BukuLoginConfig
import com.bukuwarung.edc.app.config.BukuVariantConfig
import com.bukuwarung.edc.app.config.NetworkConfig
import com.bukuwarung.edc.app.config.types.AppEnv
import com.bukuwarung.edc.app.config.types.AppId
import com.bukuwarung.edc.app.config.types.AppVariant
import com.bukuwarung.edc.app.config.types.AppVersion
import dagger.hilt.android.HiltAndroidApp
import timber.log.Timber

/**
 * Application class for Buku variant
 * Provides Buku-specific initialization and configuration
 */
@HiltAndroidApp
class BukuEdcApplication : EdcApplication() {

    override fun appInitialization() {
        val env = AppEnv.parse(BuildConfig.FLAVOR_env)
        val networkConfig = when (env) {
            AppEnv.DEV -> NetworkConfig(
                apiBaseUrl = "https://api-dev.bukuwarung.com/",
                env = env
            )
            AppEnv.STG -> NetworkConfig(
                apiBaseUrl = "https://api-staging-v1.bukuwarung.com/",
                env = env
            )
            AppEnv.PROD -> NetworkConfig(
                apiBaseUrl = "https://api-v4.bukuwarung.com/",
                env = env
            )
        }
        AppConfig.current = AppConfig(
            appVariant = AppVariant.BUKU,
            appId = AppId(BuildConfig.APPLICATION_ID),
            appVersion = AppVersion(BuildConfig.VERSION_CODE),
            networkConfig = networkConfig,
            variantConfig = BukuVariantConfig(),
            loginConfig = BukuLoginConfig(),
            env = env
        )
        super.appInitialization()
    }
}
