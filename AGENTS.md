This is an kotlin android studio project repository. It is an edc app with multiple app target variants. Please follow these guidelines when contributing:

## Key Guidelines
- Our JIRA project is https://bukuwarung.atlassian.net/jira/software/c/projects/BUKU/summary
- Always find out which JIRA issue the user is working on. Check the branch name, or ask the user to create a proper branch if it's not there. 
- Planning: Before starting the code development, agents should devise an md file document inside the plans folder like "plans/<JIRA-KEY>/<example_plan>.md" that contains all of the development plan instead of jumping straight to code development.
  - Always put the user's initial prompt in the plan file.
  - **Reading JIRA Documents**: If there are links to documents in the JIRA task (description or comments), always read them:
      - **IMPORTANT**: When fetching JIRA issues, use `expand=renderedFields` parameter to get the full description with links. The default `description` field strips all links and formatting.
      - **Confluence documents**: In `renderedFields.description`, JIRA wraps Confluence links in `<custom data-type="smartlink">URL</custom>` tags or as `<a href>` tags. Search for `atlassian.net/wiki/` in the rendered description/comments, extract the page ID (numbers after `/pages/`), then use `getConfluencePage(cloudId="bukuwarung.atlassian.net", pageId="<ID>")` to read the content.
      - **Other sources** (GitHub, external docs): Use web_fetch or web_search tools
      - Include relevant information from these documents in your planning
  - Confirmation: After writing the plan, ask for the user's confirmation first before executing it. 
  - As you executes the plan, always update the plan document with what has been implemented.
- Follow Kotlin and Android best practices and idiomatic patterns
- Follow Android architecture guidelines (MVVM, Clean Architecture)
- For Firebase tools, use [this json as reference](app/src/prod/google-services.json)
- Do not hardcode any strings or values
- Do not add new dependencies without approval 
- Always ignore files in /build folder
- Maintain existing code structure and organization
- Use dependency injection patterns, using Hilt where appropriate
- Always write unit tests for new code. Use manual mocking whenever possible
- Document public APIs, use KDoc syntax.
- Annotate complex logic with comments.
- Obey Single Responsibility principle.
- Always try to make a reusable functions if you see a similar code repeated in more than 2 places
- Avoid adding to big files. Prefer to create smaller pure functions in separate files
- Optional: branch should follow conventional commits as well

## Firebase
- for com.bukuwarung.bukuagen, the app_id is: "1:************:android:f7b3fa06837469e6ae5e2e"
- for com.bukuwarung.edc, the app_id is: "1:************:android:30a4dd79b19840adae5e2e"

## PR instructions
- PR Titles must follow conventional commits with Jira Ticket as it's scope. For example: ci(BUKU-10927): sanity pr check
- Must mention the Jira Ticket in PR description
- Before committing, ensure the user has given info how his opinion on your generated code. ask if he hasn't given it.
  - Scoring Scale 
    - 4 : Excellent; Fully meets standard with no gaps. 
    - 3 : Good: Minor gaps; easy to fix. 
    - 2 : Partial: Several gaps; moderate fixes required. 
    - 1 : Weak: Major gaps; significant rework needed. 
    - 0 : Poor: Not attempted or contradicts standard.
  - Commit with this message format : "[AI:0/1/2/3/4]: <commit message>"

## Testing instructions & guideline
- After before committing, run `./gradlew allUnitTest` to run every test in the project and make sure it passes.
- Add or update tests for the code you change, even if nobody asked.
- The test name should follow this pattern `<expected behavior> when <given condition>`.
- Always use the Given-When-Then pattern in the unit test method body
- Use JUnit 4 as the primary testing framework.
- Use Google Truth as the assertion library of choice.
- Use kotlin-faker to generate realistic dummy data for tests
- Use kotlinx-coroutines-test as needed when testing coroutines, flows and suspend functions
    - Always use runTest, avoid runBlockingTest
- Use OkHttp MockWebServer: Mock HTTP responses for network testing (only when necessary)
- Add the mentioned library dependency for tests if the module hasn't included it.
- Mocking Strategy (in order of preference)
  1. Always check whether your needed mocks or fakes already exist inside testFixtures folders.
        - Prefer to edit existing rather than create new ones.
        - New ones must be put on the testFixtures.
  2. New Fake implementations: Custom implementations for interfaces/abstract classes (if
      feasible). Put it inside testFixtures/fakes
  3. New Manual mocking: Hand-written mock classes for reusable scenarios. implement all the stubs.
      Put it inside testFixtures/mocks
  4. Use Mokkery: Code generation-based mocking library (when manual mocking insufficient)
- Always put reusable mocks, test fixtures and utilities in the `testFixtures` source set.
- Test Coverage Requirements
    - Unit tests should cover
        - All public methods and functions
        - All business logic branches (if/else, when statements)
        - Error handling paths
        - Edge cases and boundary conditions
    - Paths that can be excluded:
        - Simple getters/setters without logic
        - Framework-generated code
        - Third-party library integrations (covered by integration tests)
